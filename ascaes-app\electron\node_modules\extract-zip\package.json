{"name": "extract-zip", "version": "2.0.1", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "types": "index.d.ts", "bin": {"extract-zip": "cli.js"}, "scripts": {"ava": "ava", "coverage": "nyc ava", "lint": "yarn lint:js && yarn lint:ts && yarn tsd", "lint:js": "eslint .", "lint:ts": "eslint --config .eslintrc.typescript.js --ext .ts .", "test": "yarn lint && ava", "tsd": "tsd"}, "files": ["cli.js", "index.d.ts"], "author": "max ogden", "license": "BSD-2-<PERSON><PERSON>", "repository": "maxogden/extract-zip", "keywords": ["unzip", "zip", "extract"], "engines": {"node": ">= 10.17.0"}, "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^3.2.0", "@typescript-eslint/parser": "^3.2.0", "ava": "^3.5.1", "eslint": "^7.2.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-ava": "^10.2.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "fs-extra": "^9.0.0", "husky": "^4.2.3", "lint-staged": "^10.0.9", "nyc": "^15.0.0", "tsd": "^0.11.0", "typescript": "^3.8.3"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:ava/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:node/recommended", "plugin:promise/recommended", "standard"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": "yarn lint:js --fix", "*.ts": "yarn lint:ts --fix"}}