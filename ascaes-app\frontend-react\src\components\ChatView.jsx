import { useState } from 'react'
import { Send } from 'lucide-react'
import useStore from '../store/useStore'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import SettingsPanel from './SettingsPanel'

const ChatView = () => {
  const { 
    generationSettings, 
    updateGenerationSettings, 
    isGenerating, 
    generatedText, 
    setIsGenerating, 
    setGeneratedText,
    appendGeneratedText 
  } = useStore()
  
  const [message, setMessage] = useState('')

  const handleGenerate = async () => {
    if (!generationSettings.topic.trim()) return

    setIsGenerating(true)
    setGeneratedText('')

    try {
      const response = await fetch('http://localhost:8001/api/v1/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: generationSettings.topic,
          writing_style: generationSettings.writingStyle,
          cultural_inflection_config: generationSettings.culturalInflection,
          total_pages: generationSettings.pages,
          humanization: generationSettings.humanization,
          academic_formatting: generationSettings.academicFormatting.enabled 
            ? generationSettings.academicFormatting 
            : null
        }),
      })

      if (!response.ok) {
        throw new Error('Generation failed')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        appendGeneratedText(chunk)
      }
    } catch (error) {
      console.error('Generation error:', error)
      setGeneratedText('Error: Failed to generate document. Please check if Ollama is running and the model is available.')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSendMessage = () => {
    if (!message.trim()) return
    
    updateGenerationSettings({ topic: message })
    setMessage('')
    handleGenerate()
  }

  return (
    <div className="flex h-full">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <h2 className="text-xl font-semibold">Document Generation</h2>
          <p className="text-sm text-muted-foreground">
            Generate academic documents using AI
          </p>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 p-4 overflow-y-auto">
          {generatedText ? (
            <div className="bg-card border border-border rounded-lg p-4">
              <div className="text-sm text-muted-foreground mb-2">Generated Document:</div>
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {generatedText}
              </div>
              {isGenerating && (
                <div className="mt-2 text-sm text-muted-foreground animate-pulse">
                  Generating...
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <p className="text-lg mb-2">Welcome to ASCAES</p>
                <p className="text-sm">Enter a topic below to generate an academic document</p>
              </div>
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-border">
          <div className="flex gap-2">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your document topic..."
              className="flex-1 min-h-[60px] resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSendMessage()
                }
              }}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={isGenerating || !message.trim()}
              className="self-end"
            >
              <Send size={16} />
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      <SettingsPanel />
    </div>
  )
}

export default ChatView

