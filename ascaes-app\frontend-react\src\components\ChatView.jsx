import { useState } from 'react'
import { Send, Loader2, Square } from 'lucide-react'
import useStore from '../store/useStore'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import { Progress } from './ui/progress'
import SettingsPanel from './SettingsPanel'

const ChatView = () => {
  const {
    generationSettings,
    updateGenerationSettings,
    isGenerating,
    generatedText,
    setIsGenerating,
    setGeneratedText,
    appendGeneratedText,
    generationProgress,
    setGenerationProgress,
    setGenerationAbortController,
    stopGeneration
  } = useStore()
  
  const [message, setMessage] = useState('')



  const handleSendMessage = async () => {
    if (!message.trim()) return

    // Update the topic and trigger generation
    updateGenerationSettings({ topic: message })
    const currentTopic = message
    setMessage('')

    // Start generation with the current topic
    await handleGenerateWithTopic(currentTopic)
  }

  const handleGenerateWithTopic = async (topic) => {
    setIsGenerating(true)
    setGeneratedText('')

    // Create abort controller
    const abortController = new AbortController()
    setGenerationAbortController(abortController)

    // Initialize progress
    const totalPages = generationSettings.pages
    setGenerationProgress({
      current: 0,
      total: totalPages,
      status: 'Starting generation...',
      batchStart: 0,
      batchEnd: 0,
      wordCount: 0
    })

    try {
      const response = await fetch('http://localhost:8001/api/v1/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: abortController.signal,
        body: JSON.stringify({
          topic: topic,
          writing_style: generationSettings.writingStyle,
          cultural_inflection_config: generationSettings.culturalInflection,
          total_pages: generationSettings.pages,
          humanization: generationSettings.humanization,
          model_name: generationSettings.selectedModel,
          academic_formatting: generationSettings.academicFormatting.enabled
            ? generationSettings.academicFormatting
            : null
        }),
      })

      if (!response.ok) {
        throw new Error('Generation failed')
      }

      setGenerationProgress({
        current: 0,
        total: totalPages,
        status: 'Generating content...',
        batchStart: 0,
        batchEnd: 0,
        wordCount: 0
      })

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let currentWordCount = 0

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)

        // Check for progress messages
        if (chunk.startsWith('PROGRESS:')) {
          const [, batchStart, total, status] = chunk.split(':')
          const batchEnd = Math.min(parseInt(batchStart) + 9, parseInt(total)) // Assuming 10 pages per batch
          setGenerationProgress({
            current: parseInt(batchStart),
            total: parseInt(total),
            status: status.trim(),
            batchStart: parseInt(batchStart),
            batchEnd: batchEnd,
            wordCount: currentWordCount
          })
        } else {
          // Regular content
          appendGeneratedText(chunk)
          // Count words in the chunk
          const words = chunk.trim().split(/\s+/).filter(word => word.length > 0)
          currentWordCount += words.length
        }
      }

      setGenerationProgress({
        current: totalPages,
        total: totalPages,
        status: 'Generation completed!',
        batchStart: totalPages,
        batchEnd: totalPages,
        wordCount: currentWordCount
      })

    } catch (error) {
      console.error('Generation error:', error)
      if (error.name === 'AbortError') {
        setGeneratedText('Generation was stopped by user.')
        setGenerationProgress({
          current: 0,
          total: totalPages,
          status: 'Generation stopped',
          batchStart: 0,
          batchEnd: 0,
          wordCount: 0
        })
      } else {
        setGeneratedText('Error: Failed to generate document. Please check if Ollama is running and the model is available.')
        setGenerationProgress({
          current: 0,
          total: totalPages,
          status: 'Generation failed',
          batchStart: 0,
          batchEnd: 0,
          wordCount: 0
        })
      }
    } finally {
      setIsGenerating(false)
      setGenerationAbortController(null)
    }
  }

  return (
    <div className="flex h-full">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <h2 className="text-xl font-semibold">Document Generation</h2>
          <p className="text-sm text-muted-foreground">
            Generate academic documents using AI
          </p>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 p-4 overflow-y-auto">
          {isGenerating ? (
            <div className="space-y-4">
              {/* Progress Bar */}
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm font-medium">{generationProgress.status}</span>
                </div>
                <Progress
                  value={(generationProgress.current / generationProgress.total) * 100}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-2">
                  <span>
                    {generationProgress.batchStart > 0 && generationProgress.batchEnd > 0
                      ? `Generating pages ${generationProgress.batchStart}-${generationProgress.batchEnd}`
                      : `Page ${generationProgress.current} of ${generationProgress.total}`
                    }
                  </span>
                  <span>
                    {generationProgress.wordCount > 0 && `${generationProgress.wordCount.toLocaleString()} words`}
                  </span>
                </div>
              </div>
            </div>
          ) : generatedText ? (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <p className="text-lg mb-2">✅ Document Generated</p>
                <p className="text-sm">Check the Document tab in the right panel to view your generated content</p>
                <p className="text-xs mt-2 text-green-600">
                  {generatedText.trim().split(/\s+/).length.toLocaleString()} words generated
                </p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <p className="text-lg mb-2">Welcome to ASCAES</p>
                <p className="text-sm">Enter a topic below to generate an academic document</p>
              </div>
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-border">
          <div className="flex gap-2">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your document topic..."
              className="flex-1 min-h-[60px] resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSendMessage()
                }
              }}
            />
            <div className="flex flex-col gap-2 self-end">
              {isGenerating && (
                <Button
                  onClick={stopGeneration}
                  variant="destructive"
                  size="sm"
                  className="h-8"
                >
                  <Square size={14} />
                </Button>
              )}
              <Button
                onClick={handleSendMessage}
                disabled={isGenerating || !message.trim()}
              >
                {isGenerating ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <Send size={16} />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      <SettingsPanel />
    </div>
  )
}

export default ChatView

