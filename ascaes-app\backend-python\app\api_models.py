from fastapi import APIRouter, HTTPException
import httpx

router = APIRouter()

OLLAMA_API_URL = "http://localhost:11434/api/tags"

@router.get("/models")
async def get_ollama_models():
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(OLLAMA_API_URL, timeout=10)
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as exc:
        raise HTTPException(status_code=500, detail=f"Could not connect to Ollama: {exc}")
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=exc.response.status_code, detail=f"Ollama API error: {exc.response.text}")


