from pydantic import BaseModel
from typing import Optional, Literal

class AcademicFormattingSettings(BaseModel):
    enabled: bool
    citation_style: Literal['MLA', 'APA', 'Chicago']
    student_name: str
    professor_name: str
    course_info: str

class GenerationRequest(BaseModel):
    topic: str
    writing_style: str
    cultural_inflection_config: dict
    total_pages: int
    humanization: bool
    academic_formatting: Optional[AcademicFormattingSettings]


