import { useState, useEffect } from 'react'
import { Setting<PERSON>, FileText } from 'lucide-react'
import useStore from '../store/useStore'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Switch } from './ui/switch'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from './ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog'

// Document Preview Component
const DocumentPreview = ({ text, pages }) => {
  const wordsPerPage = 250
  const totalWords = text.trim().split(/\s+/).length
  const actualPages = Math.max(1, Math.ceil(totalWords / wordsPerPage))

  // Split text into pages
  const words = text.trim().split(/\s+/)
  const pageContents = []

  for (let i = 0; i < actualPages; i++) {
    const startWord = i * wordsPerPage
    const endWord = Math.min((i + 1) * wordsPerPage, words.length)
    const pageText = words.slice(startWord, endWord).join(' ')
    pageContents.push(pageText)
  }

  return (
    <div className="space-y-4 p-4">
      {pageContents.map((pageText, index) => (
        <div key={index} className="border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
          <div className="flex justify-between items-center mb-4 pb-2 border-b border-gray-100">
            <div className="text-xs text-gray-500">Page {index + 1}</div>
            <div className="text-xs text-gray-500">
              ~{pageText.split(/\s+/).length} words
            </div>
          </div>
          <div className="text-sm leading-relaxed text-gray-800 whitespace-pre-wrap">
            {pageText}
          </div>
          <div className="mt-4 pt-2 border-t border-gray-100 text-center">
            <div className="text-xs text-gray-400">{index + 1}</div>
          </div>
        </div>
      ))}
      {actualPages < pages && (
        <div className="text-center text-gray-500 text-sm py-4">
          {pages - actualPages} more pages will be generated...
        </div>
      )}
    </div>
  )
}

const SettingsPanel = () => {
  const { generationSettings, updateGenerationSettings, generatedText, models } = useStore()
  const [culturalModalOpen, setCulturalModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('settings')
  const [culturalSettings, setCulturalSettings] = useState({
    American: 100,
    Russian: 0,
    German: 0,
    Japanese: 0,
    French: 0
  })

  const writingStyles = [
    'Analytical',
    'Instructional', 
    'Reporting',
    'Argumentative / Persuasive',
    'Exploratory / Reflective',
    'Descriptive',
    'Narrative',
    'Schematic / Referential'
  ]

  const citationStyles = ['MLA', 'APA', 'Chicago']

  // Switch to document tab when text is generated
  useEffect(() => {
    if (generatedText && activeTab === 'settings') {
      setActiveTab('document')
    }
  }, [generatedText])

  const handleCulturalInflectionSave = () => {
    // Ensure only two cultures are active and total is 100%
    const activeCultures = Object.entries(culturalSettings).filter(([_, value]) => value > 0)
    if (activeCultures.length <= 2) {
      const total = activeCultures.reduce((sum, [_, value]) => sum + value, 0)
      if (total === 100) {
        const config = {}
        activeCultures.forEach(([culture, percentage]) => {
          config[culture] = percentage
        })
        updateGenerationSettings({ culturalInflection: config })
        setCulturalModalOpen(false)
      }
    }
  }

  return (
    <div className="w-80 bg-card border-l border-border flex flex-col h-full">
      <Tabs defaultValue="settings" className="flex-1 flex flex-col h-full">
        <TabsList className="grid w-full grid-cols-2 m-4 mb-0 flex-shrink-0">
          <TabsTrigger value="document" className="flex items-center gap-2">
            <FileText size={16} />
            Document
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings size={16} />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="document" className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Document Preview</Label>
              <div className="mt-2 bg-white border border-border rounded-lg shadow-sm max-h-96 overflow-y-auto">
                {generatedText ? (
                  <DocumentPreview text={generatedText} pages={generationSettings.pages} />
                ) : (
                  <div className="p-8 text-center text-muted-foreground">
                    <div className="text-lg mb-2">📄</div>
                    <div>No document generated yet</div>
                    <div className="text-xs mt-1">Preview will appear here after generation</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="flex-1 p-4 overflow-y-auto min-h-0">
          <div className="space-y-6 pb-4">
            {/* Writing Style */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Writing Style</Label>
              <Select 
                value={generationSettings.writingStyle} 
                onValueChange={(value) => updateGenerationSettings({ writingStyle: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>
                      {style}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Model Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">AI Model</Label>
              <Select
                value={generationSettings.selectedModel}
                onValueChange={(value) => updateGenerationSettings({ selectedModel: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a model" />
                </SelectTrigger>
                <SelectContent>
                  {models.length > 0 ? (
                    models.map((model) => (
                      <SelectItem key={model.name} value={model.name}>
                        {model.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="llama3.2:1b">llama3.2:1b (default)</SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {models.length === 0 ? 'Loading models...' : `${models.length} models available`}
              </p>
            </div>

            {/* Cultural Inflection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Cultural Inflection</Label>
              <Dialog open={culturalModalOpen} onOpenChange={setCulturalModalOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    Configure Cultures
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Cultural Inflection Settings</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Select up to 2 cultures. Total must equal 100%.
                    </p>
                    {Object.entries(culturalSettings).map(([culture, percentage]) => (
                      <div key={culture} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">{culture}</Label>
                          <span className="text-sm font-medium">{percentage}%</span>
                        </div>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={percentage}
                          onChange={(e) => {
                            const newValue = parseInt(e.target.value)
                            setCulturalSettings(prev => {
                              const newSettings = { ...prev, [culture]: newValue }

                              // Auto-adjust other values to maintain 100% total
                              const total = Object.values(newSettings).reduce((sum, val) => sum + val, 0)
                              if (total > 100) {
                                const excess = total - 100
                                const otherCultures = Object.keys(newSettings).filter(c => c !== culture)
                                otherCultures.forEach(c => {
                                  if (newSettings[c] > 0) {
                                    const reduction = Math.min(newSettings[c], excess)
                                    newSettings[c] = Math.max(0, newSettings[c] - reduction)
                                  }
                                })
                              }

                              return newSettings
                            })
                          }}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                          style={{
                            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`
                          }}
                        />
                      </div>
                    ))}
                    <Button onClick={handleCulturalInflectionSave} className="w-full">
                      Save Settings
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Pages */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pages</Label>
              <Input
                type="number"
                min="1"
                max="1500"
                value={generationSettings.pages}
                onChange={(e) => updateGenerationSettings({ pages: parseInt(e.target.value) || 1 })}
              />
              <p className="text-xs text-muted-foreground">
                Approx. {generationSettings.pages * 250} words
              </p>
            </div>

            {/* Humanization */}
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Improve realism</Label>
              <Switch
                checked={generationSettings.humanization}
                onCheckedChange={(checked) => updateGenerationSettings({ humanization: checked })}
              />
            </div>

            {/* Academic Formatting */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Enable standard academic formatting</Label>
                <Switch
                  checked={generationSettings.academicFormatting.enabled}
                  onCheckedChange={(checked) => 
                    updateGenerationSettings({ 
                      academicFormatting: { 
                        ...generationSettings.academicFormatting, 
                        enabled: checked 
                      } 
                    })
                  }
                />
              </div>

              {generationSettings.academicFormatting.enabled && (
                <div className="space-y-3 pl-4 border-l-2 border-border">
                  <div className="space-y-2">
                    <Label className="text-sm">Citation Style</Label>
                    <Select 
                      value={generationSettings.academicFormatting.citationStyle}
                      onValueChange={(value) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            citationStyle: value 
                          } 
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {citationStyles.map((style) => (
                          <SelectItem key={style} value={style}>
                            {style}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Student Name</Label>
                    <Input
                      value={generationSettings.academicFormatting.studentName}
                      onChange={(e) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            studentName: e.target.value 
                          } 
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Professor Name</Label>
                    <Input
                      value={generationSettings.academicFormatting.professorName}
                      onChange={(e) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            professorName: e.target.value 
                          } 
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Course Info</Label>
                    <Input
                      value={generationSettings.academicFormatting.courseInfo}
                      onChange={(e) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            courseInfo: e.target.value 
                          } 
                        })
                      }
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SettingsPanel

