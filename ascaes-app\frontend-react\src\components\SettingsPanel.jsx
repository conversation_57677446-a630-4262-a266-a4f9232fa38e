import { useState } from 'react'
import { Settings, FileText } from 'lucide-react'
import useStore from '../store/useStore'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Switch } from './ui/switch'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from './ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog'

const SettingsPanel = () => {
  const { generationSettings, updateGenerationSettings, generatedText } = useStore()
  const [culturalModalOpen, setCulturalModalOpen] = useState(false)
  const [culturalSettings, setCulturalSettings] = useState({
    American: 100,
    Russian: 0,
    German: 0,
    Japanese: 0,
    French: 0
  })

  const writingStyles = [
    'Analytical',
    'Instructional', 
    'Reporting',
    'Argumentative / Persuasive',
    'Exploratory / Reflective',
    'Descriptive',
    'Narrative',
    'Schematic / Referential'
  ]

  const citationStyles = ['MLA', 'APA', 'Chicago']

  const handleCulturalInflectionSave = () => {
    // Ensure only two cultures are active and total is 100%
    const activeCultures = Object.entries(culturalSettings).filter(([_, value]) => value > 0)
    if (activeCultures.length <= 2) {
      const total = activeCultures.reduce((sum, [_, value]) => sum + value, 0)
      if (total === 100) {
        const config = {}
        activeCultures.forEach(([culture, percentage]) => {
          config[culture] = percentage
        })
        updateGenerationSettings({ culturalInflection: config })
        setCulturalModalOpen(false)
      }
    }
  }

  return (
    <div className="w-80 bg-card border-l border-border flex flex-col">
      <Tabs defaultValue="settings" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 m-4 mb-0">
          <TabsTrigger value="document" className="flex items-center gap-2">
            <FileText size={16} />
            Document
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings size={16} />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="document" className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Document Preview</Label>
              <div className="mt-2 p-3 bg-muted rounded-lg text-sm max-h-96 overflow-y-auto">
                {generatedText ? (
                  <div className="whitespace-pre-wrap">{generatedText.substring(0, 500)}...</div>
                ) : (
                  <div className="text-muted-foreground">No document generated yet</div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-6">
            {/* Writing Style */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Writing Style</Label>
              <Select 
                value={generationSettings.writingStyle} 
                onValueChange={(value) => updateGenerationSettings({ writingStyle: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>
                      {style}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Cultural Inflection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Cultural Inflection</Label>
              <Dialog open={culturalModalOpen} onOpenChange={setCulturalModalOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    Configure Cultures
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Cultural Inflection Settings</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Select up to 2 cultures. Total must equal 100%.
                    </p>
                    {Object.entries(culturalSettings).map(([culture, percentage]) => (
                      <div key={culture} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">{culture}</Label>
                          <span className="text-sm font-medium">{percentage}%</span>
                        </div>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={percentage}
                          onChange={(e) => {
                            const newValue = parseInt(e.target.value)
                            setCulturalSettings(prev => {
                              const newSettings = { ...prev, [culture]: newValue }

                              // Auto-adjust other values to maintain 100% total
                              const total = Object.values(newSettings).reduce((sum, val) => sum + val, 0)
                              if (total > 100) {
                                const excess = total - 100
                                const otherCultures = Object.keys(newSettings).filter(c => c !== culture)
                                otherCultures.forEach(c => {
                                  if (newSettings[c] > 0) {
                                    const reduction = Math.min(newSettings[c], excess)
                                    newSettings[c] = Math.max(0, newSettings[c] - reduction)
                                  }
                                })
                              }

                              return newSettings
                            })
                          }}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                          style={{
                            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`
                          }}
                        />
                      </div>
                    ))}
                    <Button onClick={handleCulturalInflectionSave} className="w-full">
                      Save Settings
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Pages */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pages</Label>
              <Input
                type="number"
                min="1"
                max="1500"
                value={generationSettings.pages}
                onChange={(e) => updateGenerationSettings({ pages: parseInt(e.target.value) || 1 })}
              />
              <p className="text-xs text-muted-foreground">
                Approx. {generationSettings.pages * 250} words
              </p>
            </div>

            {/* Humanization */}
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Improve realism</Label>
              <Switch
                checked={generationSettings.humanization}
                onCheckedChange={(checked) => updateGenerationSettings({ humanization: checked })}
              />
            </div>

            {/* Academic Formatting */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Enable standard academic formatting</Label>
                <Switch
                  checked={generationSettings.academicFormatting.enabled}
                  onCheckedChange={(checked) => 
                    updateGenerationSettings({ 
                      academicFormatting: { 
                        ...generationSettings.academicFormatting, 
                        enabled: checked 
                      } 
                    })
                  }
                />
              </div>

              {generationSettings.academicFormatting.enabled && (
                <div className="space-y-3 pl-4 border-l-2 border-border">
                  <div className="space-y-2">
                    <Label className="text-sm">Citation Style</Label>
                    <Select 
                      value={generationSettings.academicFormatting.citationStyle}
                      onValueChange={(value) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            citationStyle: value 
                          } 
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {citationStyles.map((style) => (
                          <SelectItem key={style} value={style}>
                            {style}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Student Name</Label>
                    <Input
                      value={generationSettings.academicFormatting.studentName}
                      onChange={(e) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            studentName: e.target.value 
                          } 
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Professor Name</Label>
                    <Input
                      value={generationSettings.academicFormatting.professorName}
                      onChange={(e) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            professorName: e.target.value 
                          } 
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">Course Info</Label>
                    <Input
                      value={generationSettings.academicFormatting.courseInfo}
                      onChange={(e) => 
                        updateGenerationSettings({ 
                          academicFormatting: { 
                            ...generationSettings.academicFormatting, 
                            courseInfo: e.target.value 
                          } 
                        })
                      }
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default SettingsPanel

