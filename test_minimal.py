#!/usr/bin/env python3
import requests
import json

def test_minimal_generation():
    """Test minimal generation request"""
    print("Testing minimal generation...")
    
    payload = {
        "topic": "climate change",
        "writing_style": "Analytical", 
        "cultural_inflection_config": {},
        "total_pages": 1,
        "humanization": False,
        "academic_formatting": None
    }
    
    print("Payload:", json.dumps(payload, indent=2))
    
    try:
        print("Sending request...")
        response = requests.post(
            "http://localhost:8001/api/v1/generate",
            json=payload,
            timeout=120,
            stream=True
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Generation started successfully!")
            print("Generated content:")
            print("-" * 50)
            
            content = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    content += chunk
                    print(chunk, end="", flush=True)
            
            print("\n" + "-" * 50)
            print(f"✅ Generation completed! Total length: {len(content)} characters")
            return True
        else:
            print(f"❌ Generation failed with status {response.status_code}")
            print("Response:", response.text)
            return False
            
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

if __name__ == "__main__":
    test_minimal_generation()
