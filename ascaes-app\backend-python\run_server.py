#!/usr/bin/env python3
import uvicorn
import sys
import os

if __name__ == "__main__":
    print("Starting ASCAES Backend Server...")
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    
    try:
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",
            port=8001,
            log_level="info",
            reload=False
        )
    except Exception as e:
        print(f"Error starting server: {e}")
        input("Press Enter to exit...")
