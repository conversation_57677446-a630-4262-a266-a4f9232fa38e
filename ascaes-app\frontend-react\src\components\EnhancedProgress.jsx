import { Loader2, FileText, Clock, Target } from 'lucide-react'
import { Progress } from './ui/progress'

const EnhancedProgress = ({ progress }) => {
  const {
    current = 0,
    total = 0,
    status = '',
    batchStart = 0,
    batchEnd = 0,
    currentBatch = 0,
    totalBatches = 0,
    wordCount = 0
  } = progress

  const percentage = total > 0 ? Math.round((current / total) * 100) : 0
  const estimatedWords = total * 250
  const wordsProgress = wordCount > 0 ? Math.round((wordCount / estimatedWords) * 100) : 0

  // Use provided batch info or calculate fallback
  const actualCurrentBatch = currentBatch || Math.ceil(current / 10)
  const actualTotalBatches = totalBatches || Math.ceil(total / 10)
  const batchProgress = actualTotalBatches > 0 ? Math.round((actualCurrentBatch / actualTotalBatches) * 100) : 0

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 shadow-lg">
      {/* Header */}
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-800">Document Generation</h3>
          <p className="text-sm text-gray-600">{status}</p>
        </div>
      </div>

      {/* Main Progress Bar */}
      <div className="space-y-4">
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm font-bold text-blue-600">{percentage}%</span>
          </div>
          <Progress 
            value={percentage} 
            className="h-3 bg-gray-200"
          />
        </div>

        {/* Batch Progress */}
        {batchStart > 0 && batchEnd > 0 && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">
                Current Batch: Pages {batchStart}-{batchEnd}
              </span>
              <span className="text-sm font-bold text-green-600">
                Batch {actualCurrentBatch}/{actualTotalBatches}
              </span>
            </div>
            <Progress 
              value={batchProgress} 
              className="h-2 bg-gray-200"
            />
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-4 mt-6">
          {/* Pages */}
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center gap-2 mb-1">
              <FileText className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-gray-600">Pages</span>
            </div>
            <div className="text-lg font-bold text-gray-800">
              {current}/{total}
            </div>
          </div>

          {/* Words */}
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center gap-2 mb-1">
              <Target className="h-4 w-4 text-green-500" />
              <span className="text-xs font-medium text-gray-600">Words</span>
            </div>
            <div className="text-lg font-bold text-gray-800">
              {wordCount.toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">
              of ~{estimatedWords.toLocaleString()}
            </div>
          </div>

          {/* Speed */}
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="h-4 w-4 text-purple-500" />
              <span className="text-xs font-medium text-gray-600">Progress</span>
            </div>
            <div className="text-lg font-bold text-gray-800">
              {wordsProgress}%
            </div>
            <div className="text-xs text-gray-500">
              word target
            </div>
          </div>
        </div>

        {/* Batch Indicator */}
        {total > 10 && (
          <div className="mt-4">
            <div className="text-xs font-medium text-gray-600 mb-2">Batch Processing</div>
            <div className="flex flex-wrap gap-1">
              {Array.from({ length: actualTotalBatches }, (_, i) => {
                const batchNum = i + 1
                const isActive = batchNum === actualCurrentBatch
                const isCompleted = batchNum < actualCurrentBatch
                
                return (
                  <div
                    key={batchNum}
                    className={`
                      px-2 py-1 rounded text-xs font-medium
                      ${isActive ? 'bg-blue-500 text-white animate-pulse' : ''}
                      ${isCompleted ? 'bg-green-500 text-white' : ''}
                      ${!isActive && !isCompleted ? 'bg-gray-200 text-gray-600' : ''}
                    `}
                  >
                    {(batchNum - 1) * 10 + 1}-{Math.min(batchNum * 10, total)}
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default EnhancedProgress
