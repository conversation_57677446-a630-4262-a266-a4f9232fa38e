@echo off
echo ========================================
echo    ASCAES Application Startup Script
echo ========================================
echo.

:: Check if Ollama is installed
where ollama >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Ollama is not installed or not in PATH
    echo Please install Ollama from: https://ollama.ai/
    pause
    exit /b 1
)

:: Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

:: Check if Python is installed
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from: https://python.org/
    pause
    exit /b 1
)

echo All prerequisites found!
echo.

:: Create log directory
if not exist "logs" mkdir logs

echo Checking Ollama server status...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting Ollama server...
    start "Ollama Server" cmd /k "echo Starting Ollama Server... && ollama serve"
    :: Wait for Ollama to start
    timeout /t 5 /nobreak >nul
) else (
    echo Ollama server is already running.
)

echo Checking if default model exists...
ollama list | findstr "llama3.2:1b" >nul
if %errorlevel% neq 0 (
    echo Pulling default model llama3.2:1b...
    start "Ollama Model Pull" cmd /k "echo Pulling llama3.2:1b model... && ollama pull llama3.2:1b && echo Model pulled successfully! && pause"
) else (
    echo Default model llama3.2:1b already exists.
)

echo.
echo Installing backend dependencies...
cd backend-python
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Installing Python packages...
pip install --upgrade pip >nul 2>&1
pip install fastapi uvicorn httpx >nul 2>&1
pip install -r requirements.txt >../logs/backend_install.log 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Some packages may have failed to install
    echo Installing essential packages manually...
    pip install fastapi uvicorn httpx pydantic >nul 2>&1
)

echo Starting backend server...
start "ASCAES Backend" cmd /k "cd /d %cd% && echo Starting ASCAES Backend Server... && venv\Scripts\python.exe -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload"

cd ..

echo.
echo Installing frontend dependencies...
cd frontend-react

if not exist "node_modules" (
    echo Installing Node.js packages...
    npm install >../logs/frontend_install.log 2>&1
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install frontend dependencies
        echo Check logs\frontend_install.log for details
        pause
        exit /b 1
    )
) else (
    echo Node modules already installed.
)

echo Starting frontend development server...
start "ASCAES Frontend" cmd /k "cd /d %cd% && echo Starting ASCAES Frontend... && npm run dev"

cd ..

echo.
echo ========================================
echo    All services are starting up!
echo ========================================
echo.
echo Services:
echo - Ollama Server: Running in background
echo - Backend API: http://localhost:8001
echo - Frontend App: http://localhost:5173
echo.
echo The application will open automatically in a few seconds...
echo.

:: Wait for services to start
echo Waiting for services to initialize...
timeout /t 15 /nobreak >nul

:: Verify services are running
echo Verifying services...
echo Checking backend...
curl -s http://localhost:8001/api/v1/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Backend is running
) else (
    echo ✗ Backend may not be ready yet
)

echo Checking frontend...
curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Frontend is running
) else (
    echo ✗ Frontend may not be ready yet
)

:: Open the application in default browser
echo Opening ASCAES application...
start http://localhost:5173

echo.
echo ========================================
echo    ASCAES is now running!
echo ========================================
echo.
echo To stop all services:
echo 1. Close all opened command windows
echo 2. Or run: stop_all.bat
echo.
echo Press any key to exit this window...
pause >nul
