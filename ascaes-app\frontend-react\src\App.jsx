import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useEffect } from 'react'
import useStore from './store/useStore'
import Sidebar from './components/Sidebar'
import ChatView from './components/ChatView'
import DocumentsView from './components/DocumentsView'
import './App.css'

function App() {
  const { theme } = useStore()

  useEffect(() => {
    // Apply theme to document
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [theme])

  return (
    <Router>
      <div className="flex h-screen bg-background text-foreground">
        <Sidebar />
        <main className="flex-1 overflow-hidden">
          <Routes>
            <Route path="/" element={<ChatView />} />
            <Route path="/chat" element={<ChatView />} />
            <Route path="/documents" element={<DocumentsView />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App

