{"version": 3, "sources": ["../../src/factories/createNodeWriter.js"], "names": ["createBlockingWriter", "stream", "write", "message", "targetStream", "process", "env", "ROARR_STREAM", "toUpperCase", "stdout", "stderr"], "mappings": ";;;;;;;AAMA,MAAMA,oBAAoB,GAAIC,MAAD,IAAyC;AACpE,SAAO;AACLC,IAAAA,KAAK,EAAGC,OAAD,IAAqB;AAC1BF,MAAAA,MAAM,CAACC,KAAP,CAAaC,OAAO,GAAG,IAAvB;AACD;AAHI,GAAP;AAKD,CAND;;+BAQiC;AAC/B;AACA,QAAMC,YAAY,GAAG,CAACC,OAAO,CAACC,GAAR,CAAYC,YAAZ,IAA4B,QAA7B,EAAuCC,WAAvC,EAArB;AAEA,QAAMP,MAAM,GAAGG,YAAY,CAACI,WAAb,OAA+B,QAA/B,GAA0CH,OAAO,CAACI,MAAlD,GAA2DJ,OAAO,CAACK,MAAlF;AAEA,SAAOV,oBAAoB,CAACC,MAAD,CAA3B;AACD,C", "sourcesContent": ["// @flow\n\nimport type {\n  WriterType,\n} from '../types';\n\nconst createBlockingWriter = (stream: stream$Writable): WriterType => {\n  return {\n    write: (message: string) => {\n      stream.write(message + '\\n');\n    },\n  };\n};\n\nexport default (): WriterType => {\n  // eslint-disable-next-line no-process-env\n  const targetStream = (process.env.ROARR_STREAM || 'STDOUT').toUpperCase();\n\n  const stream = targetStream.toUpperCase() === 'STDOUT' ? process.stdout : process.stderr;\n\n  return createBlockingWriter(stream);\n};\n"], "file": "createNodeWriter.js"}