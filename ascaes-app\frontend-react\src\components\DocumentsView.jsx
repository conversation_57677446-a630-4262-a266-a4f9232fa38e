import { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON> } from 'lucide-react'
import useStore from '../store/useStore'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog'

const DocumentsView = () => {
  const { documents, setDocuments, deleteDocument } = useStore()
  const [selectedDocument, setSelectedDocument] = useState(null)

  useEffect(() => {
    fetchDocuments()
  }, [])

  const fetchDocuments = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/documents')
      if (response.ok) {
        const docs = await response.json()
        setDocuments(docs)
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error)
    }
  }

  const handleDeleteDocument = async (id) => {
    try {
      const response = await fetch(`http://localhost:8001/api/v1/documents/${id}`, {
        method: 'DELETE'
      })
      if (response.ok) {
        deleteDocument(id)
      }
    } catch (error) {
      console.error('Failed to delete document:', error)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <h2 className="text-2xl font-semibold">Documents</h2>
        <p className="text-muted-foreground">
          Manage your generated academic documents
        </p>
      </div>

      {/* Documents Grid */}
      <div className="flex-1 p-6 overflow-y-auto">
        {documents.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <FileText size={48} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">No documents yet</p>
              <p className="text-sm">Generate your first document in the Chat view</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {documents.map((doc) => (
              <Card key={doc.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg line-clamp-2">{doc.title}</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(doc.timestamp)}
                  </p>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm">
                    <p><span className="font-medium">Style:</span> {doc.writing_style}</p>
                    <p><span className="font-medium">Pages:</span> {doc.total_pages}</p>
                    <p><span className="font-medium">Humanization:</span> {doc.humanization ? 'Yes' : 'No'}</p>
                  </div>
                  
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="flex-1"
                          onClick={() => setSelectedDocument(doc)}
                        >
                          <Eye size={16} className="mr-2" />
                          View
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[80vh]">
                        <DialogHeader>
                          <DialogTitle>{selectedDocument?.title}</DialogTitle>
                        </DialogHeader>
                        <div className="overflow-y-auto max-h-[60vh] p-4 bg-muted rounded-lg">
                          <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                            {selectedDocument?.generated_text}
                          </pre>
                        </div>
                      </DialogContent>
                    </Dialog>
                    
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => handleDeleteDocument(doc.id)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default DocumentsView

