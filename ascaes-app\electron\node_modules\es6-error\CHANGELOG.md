# Change Log

## [v4.0.1] - 2017-01-04
### Fixed
  - jsnext build uses `babel-plugin-transform-builtin-extend` (#27)

## [v4.0.0] - 2016-10-03
### Added
 - jsnext build (#26)

## [v3.2.0] - 2016-09-29
### Added
 - TypeScript definitions (#24)

## [v3.1.0] - 2016-09-08
### Changed
 - Point jsnext build to transpiled code (#23)

## [v3.0.1] - 2016-07-14
### Changed
 - Move Babel config to `.babelrc` (#20)

## [v3.0.0] - 2016-05-18
### Changed
 - Upgrade to Babel 6 (#16)
 - Make `message`, `name`, and `stack` properties configurable (to match built-in `Error`) (#17)
