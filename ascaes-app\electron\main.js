const { app, BrowserWindow, ipcMain } = require('electron')
const path = require('path')
const { spawn } = require('child_process')

let mainWindow
let backendProcess = null
let backendStatus = 'stopped'

const isDev = process.argv.includes('--dev')

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../frontend-react/public/favicon.ico'),
    titleBarStyle: 'default',
    show: false
  })

  // Load the app
  if (isDev) {
    // Development mode - load from Vite dev server
    mainWindow.loadURL('http://localhost:5173')
    mainWindow.webContents.openDevTools()
  } else {
    // Production mode - load built files
    mainWindow.loadFile(path.join(__dirname, '../frontend-react/dist/index.html'))
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

function startBackend() {
  if (backendProcess) {
    console.log('Backend already running')
    return
  }

  console.log('Starting FastAPI backend...')
  backendStatus = 'starting'
  
  const backendPath = path.join(__dirname, '../backend-python')
  const pythonExecutable = process.platform === 'win32' ? 'python' : 'python3'
  
  backendProcess = spawn(pythonExecutable, ['-m', 'uvicorn', 'app.main:app', '--host', '0.0.0.0', '--port', '8001'], {
    cwd: backendPath,
    stdio: ['pipe', 'pipe', 'pipe']
  })

  backendProcess.stdout.on('data', (data) => {
    console.log(`Backend stdout: ${data}`)
    if (data.toString().includes('Uvicorn running')) {
      backendStatus = 'running'
      if (mainWindow) {
        mainWindow.webContents.send('backend-status-changed', backendStatus)
      }
    }
  })

  backendProcess.stderr.on('data', (data) => {
    console.error(`Backend stderr: ${data}`)
  })

  backendProcess.on('close', (code) => {
    console.log(`Backend process exited with code ${code}`)
    backendStatus = 'stopped'
    backendProcess = null
    if (mainWindow) {
      mainWindow.webContents.send('backend-status-changed', backendStatus)
    }
  })

  backendProcess.on('error', (error) => {
    console.error('Failed to start backend:', error)
    backendStatus = 'error'
    backendProcess = null
    if (mainWindow) {
      mainWindow.webContents.send('backend-status-changed', backendStatus)
    }
  })
}

function stopBackend() {
  if (backendProcess) {
    console.log('Stopping FastAPI backend...')
    backendProcess.kill()
    backendProcess = null
    backendStatus = 'stopped'
  }
}

// App event handlers
app.whenReady().then(() => {
  createWindow()
  
  // Start backend automatically
  startBackend()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  stopBackend()
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  stopBackend()
})

// IPC handlers
ipcMain.handle('start-backend', () => {
  startBackend()
  return backendStatus
})

ipcMain.handle('stop-backend', () => {
  stopBackend()
  return backendStatus
})

ipcMain.handle('get-backend-status', () => {
  return backendStatus
})

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize()
  }
})

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize()
    } else {
      mainWindow.maximize()
    }
  }
})

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close()
  }
})

ipcMain.handle('get-version', () => {
  return app.getVersion()
})

