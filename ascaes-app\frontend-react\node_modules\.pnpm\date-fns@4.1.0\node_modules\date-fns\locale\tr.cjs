"use strict";
exports.tr = void 0;
var _index = require("./tr/_lib/formatDistance.cjs");
var _index2 = require("./tr/_lib/formatLong.cjs");
var _index3 = require("./tr/_lib/formatRelative.cjs");
var _index4 = require("./tr/_lib/localize.cjs");
var _index5 = require("./tr/_lib/match.cjs");

/**
 * @category Locales
 * @summary Turkish locale.
 * @language Turkish
 * @iso-639-2 tur
 * <AUTHOR> [@alpcanaydin](https://github.com/alpcanaydin)
 * <AUTHOR> [@berkaey](https://github.com/berkaey)
 * <AUTHOR> <PERSON> [@bulutfatih](https://github.com/bulutfatih)
 * <AUTHOR> [@dbtek](https://github.com/dbtek)
 * <AUTHOR> [@ikayar](https://github.com/ikayar)
 *
 *
 */
const tr = (exports.tr = {
  code: "tr",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
