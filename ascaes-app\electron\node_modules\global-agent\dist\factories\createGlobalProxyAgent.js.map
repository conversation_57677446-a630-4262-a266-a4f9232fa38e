{"version": 3, "sources": ["../../src/factories/createGlobalProxyAgent.js"], "names": ["httpGet", "http", "get", "httpRequest", "request", "httpsGet", "https", "httpsRequest", "log", "<PERSON><PERSON>", "child", "namespace", "defaultConfigurationInput", "environmentVariableNamespace", "undefined", "forceGlobalAgent", "socketConnectionTimeout", "omitUndefined", "subject", "keys", "Object", "result", "key", "value", "createConfiguration", "configurationInput", "environment", "process", "env", "defaultConfiguration", "GLOBAL_AGENT_ENVIRONMENT_VARIABLE_NAMESPACE", "GLOBAL_AGENT_FORCE_GLOBAL_AGENT", "GLOBAL_AGENT_SOCKET_CONNECTION_TIMEOUT", "Number", "parseInt", "configuration", "proxyController", "HTTP_PROXY", "HTTPS_PROXY", "NO_PROXY", "info", "state", "mustUrlUseProxy", "getProxy", "url", "getUrlProxy", "proxy", "UnexpectedStateError", "getHttpProxy", "BoundHttpProxyAgent", "HttpProxyAgent", "constructor", "globalAgent", "httpAgent", "getHttpsProxy", "BoundHttpsProxyAgent", "HttpsProxyAgent", "httpsAgent", "semver", "gte", "version", "warn"], "mappings": ";;;;;;;AAEA;;AACA;;AACA;;AAGA;;AACA;;AACA;;AAIA;;AAGA;;AASA;;;;AAEA,MAAMA,OAAO,GAAGC,cAAKC,GAArB;AACA,MAAMC,WAAW,GAAGF,cAAKG,OAAzB;AACA,MAAMC,QAAQ,GAAGC,eAAMJ,GAAvB;AACA,MAAMK,YAAY,GAAGD,eAAMF,OAA3B;;AAEA,MAAMI,GAAG,GAAGC,gBAAOC,KAAP,CAAa;AACvBC,EAAAA,SAAS,EAAE;AADY,CAAb,CAAZ;;AAIA,MAAMC,yBAAyB,GAAG;AAChCC,EAAAA,4BAA4B,EAAEC,SADE;AAEhCC,EAAAA,gBAAgB,EAAED,SAFc;AAGhCE,EAAAA,uBAAuB,EAAE;AAHO,CAAlC;;AAMA,MAAMC,aAAa,GAAIC,OAAD,IAAa;AACjC,QAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYD,OAAZ,CAAb;AAEA,QAAMG,MAAM,GAAG,EAAf;;AAEA,OAAK,MAAMC,GAAX,IAAkBH,IAAlB,EAAwB;AACtB,UAAMI,KAAK,GAAGL,OAAO,CAACI,GAAD,CAArB;;AAEA,QAAIC,KAAK,KAAKT,SAAd,EAAyB;AACvBO,MAAAA,MAAM,CAACC,GAAD,CAAN,GAAcC,KAAd;AACD;AACF;;AAED,SAAOF,MAAP;AACD,CAdD;;AAgBA,MAAMG,mBAAmB,GAAIC,kBAAD,IAAuF;AACjH;AACA,QAAMC,WAAW,GAAGC,OAAO,CAACC,GAA5B;AAEA,QAAMC,oBAAoB,GAAG;AAC3BhB,IAAAA,4BAA4B,EAAE,OAAOa,WAAW,CAACI,2CAAnB,KAAmE,QAAnE,GAA8EJ,WAAW,CAACI,2CAA1F,GAAwI,eAD3I;AAE3Bf,IAAAA,gBAAgB,EAAE,OAAOW,WAAW,CAACK,+BAAnB,KAAuD,QAAvD,GAAkE,sBAAaL,WAAW,CAACK,+BAAzB,CAAlE,GAA8H,IAFrH;AAG3Bf,IAAAA,uBAAuB,EAAE,OAAOU,WAAW,CAACM,sCAAnB,KAA8D,QAA9D,GAAyEC,MAAM,CAACC,QAAP,CAAgBR,WAAW,CAACM,sCAA5B,EAAoE,EAApE,CAAzE,GAAmJpB,yBAAyB,CAACI;AAH3K,GAA7B,CAJiH,CAUjH;;AACA,SAAO,EACL,GAAGa,oBADE;AAEL,OAAGZ,aAAa,CAACQ,kBAAD;AAFX,GAAP;AAID,CAfD;;gCAiBgBA,kBAAoD,GAAGb,yB,KAA8B;AACnG,QAAMuB,aAAa,GAAGX,mBAAmB,CAACC,kBAAD,CAAzC;AAEA,QAAMW,eAAe,GAAG,qCAAxB,CAHmG,CAKnG;;AACAA,EAAAA,eAAe,CAACC,UAAhB,GAA6BV,OAAO,CAACC,GAAR,CAAYO,aAAa,CAACtB,4BAAd,GAA6C,YAAzD,KAA0E,IAAvG,CANmG,CAQnG;;AACAuB,EAAAA,eAAe,CAACE,WAAhB,GAA8BX,OAAO,CAACC,GAAR,CAAYO,aAAa,CAACtB,4BAAd,GAA6C,aAAzD,KAA2E,IAAzG,CATmG,CAWnG;;AACAuB,EAAAA,eAAe,CAACG,QAAhB,GAA2BZ,OAAO,CAACC,GAAR,CAAYO,aAAa,CAACtB,4BAAd,GAA6C,UAAzD,KAAwE,IAAnG;AAEAL,EAAAA,GAAG,CAACgC,IAAJ,CAAS;AACPL,IAAAA,aADO;AAEPM,IAAAA,KAAK,EAAEL;AAFA,GAAT,EAGG,mCAHH;;AAKA,QAAMM,eAAe,GAAIC,QAAD,IAAc;AACpC,WAAQC,GAAD,IAAS;AACd,UAAI,CAACD,QAAQ,EAAb,EAAiB;AACf,eAAO,KAAP;AACD;;AAED,UAAI,CAACP,eAAe,CAACG,QAArB,EAA+B;AAC7B,eAAO,IAAP;AACD;;AAED,aAAO,CAAC,qCAAqBK,GAArB,EAA0BR,eAAe,CAACG,QAA1C,CAAR;AACD,KAVD;AAWD,GAZD;;AAcA,QAAMM,WAAW,GAAIF,QAAD,IAAc;AAChC,WAAO,MAAM;AACX,YAAMG,KAAK,GAAGH,QAAQ,EAAtB;;AAEA,UAAI,CAACG,KAAL,EAAY;AACV,cAAM,IAAIC,4BAAJ,CAAyB,mCAAzB,CAAN;AACD;;AAED,aAAO,8BAAcD,KAAd,CAAP;AACD,KARD;AASD,GAVD;;AAYA,QAAME,YAAY,GAAG,MAAM;AACzB,WAAOZ,eAAe,CAACC,UAAvB;AACD,GAFD;;AAIA,QAAMY,mBAAmB,GAAG,cAAcC,uBAAd,CAA6B;AACvDC,IAAAA,WAAW,GAAI;AACb,YACE,MAAM;AACJ,eAAOH,YAAY,EAAnB;AACD,OAHH,EAIEN,eAAe,CAACM,YAAD,CAJjB,EAKEH,WAAW,CAACG,YAAD,CALb,EAME/C,cAAKmD,WANP,EAOEjB,aAAa,CAACnB,uBAPhB;AASD;;AAXsD,GAAzD;AAcA,QAAMqC,SAAS,GAAG,IAAIJ,mBAAJ,EAAlB;;AAEA,QAAMK,aAAa,GAAG,MAAM;AAC1B,WAAOlB,eAAe,CAACE,WAAhB,IAA+BF,eAAe,CAACC,UAAtD;AACD,GAFD;;AAIA,QAAMkB,oBAAoB,GAAG,cAAcC,wBAAd,CAA8B;AACzDL,IAAAA,WAAW,GAAI;AACb,YACE,MAAM;AACJ,eAAOG,aAAa,EAApB;AACD,OAHH,EAIEZ,eAAe,CAACY,aAAD,CAJjB,EAKET,WAAW,CAACS,aAAD,CALb,EAMEhD,eAAM8C,WANR,EAOEjB,aAAa,CAACnB,uBAPhB;AASD;;AAXwD,GAA3D;AAcA,QAAMyC,UAAU,GAAG,IAAIF,oBAAJ,EAAnB,CAnFmG,CAqFnG;AACA;;AACA,MAAIG,gBAAOC,GAAP,CAAWhC,OAAO,CAACiC,OAAnB,EAA4B,SAA5B,CAAJ,EAA4C;AAC1C;AACA;AACA3D,kBAAKmD,WAAL,GAAmBC,SAAnB,CAH0C,CAK1C;;AACA/C,mBAAM8C,WAAN,GAAoBK,UAApB;AACD,GA9FkG,CAgGnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAIC,gBAAOC,GAAP,CAAWhC,OAAO,CAACiC,OAAnB,EAA4B,SAA5B,CAAJ,EAA4C;AAC1C;AACA3D,kBAAKC,GAAL,GAAW,+BAAeF,OAAf,EAAwBqD,SAAxB,EAAmClB,aAAa,CAACpB,gBAAjD,CAAX,CAF0C,CAI1C;;AACAd,kBAAKG,OAAL,GAAe,+BAAeD,WAAf,EAA4BkD,SAA5B,EAAuClB,aAAa,CAACpB,gBAArD,CAAf,CAL0C,CAO1C;;AACAT,mBAAMJ,GAAN,GAAY,+BAAeG,QAAf,EAAyBoD,UAAzB,EAAqCtB,aAAa,CAACpB,gBAAnD,CAAZ,CAR0C,CAU1C;;AACAT,mBAAMF,OAAN,GAAgB,+BAAeG,YAAf,EAA6BkD,UAA7B,EAAyCtB,aAAa,CAACpB,gBAAvD,CAAhB;AACD,GAZD,MAYO;AACLP,IAAAA,GAAG,CAACqD,IAAJ,CAAS,+EAAT;AACD;;AAED,SAAOzB,eAAP;AACD,C", "sourcesContent": ["// @flow\n\nimport http from 'http';\nimport https from 'https';\nimport {\n  boolean as parseBoolean,\n} from 'boolean';\nimport semver from 'semver';\nimport Logger from '../Logger';\nimport {\n  HttpProxyAgent,\n  HttpsProxyAgent,\n} from '../classes';\nimport {\n  UnexpectedStateError,\n} from '../errors';\nimport {\n  bindHttpMethod,\n  isUrlMatchingNoProxy,\n  parseProxyUrl,\n} from '../utilities';\nimport type {\n  ProxyAgentConfigurationInputType,\n  ProxyAgentConfigurationType,\n} from '../types';\nimport createProxyController from './createProxyController';\n\nconst httpGet = http.get;\nconst httpRequest = http.request;\nconst httpsGet = https.get;\nconst httpsRequest = https.request;\n\nconst log = Logger.child({\n  namespace: 'createGlobalProxyAgent',\n});\n\nconst defaultConfigurationInput = {\n  environmentVariableNamespace: undefined,\n  forceGlobalAgent: undefined,\n  socketConnectionTimeout: 60000,\n};\n\nconst omitUndefined = (subject) => {\n  const keys = Object.keys(subject);\n\n  const result = {};\n\n  for (const key of keys) {\n    const value = subject[key];\n\n    if (value !== undefined) {\n      result[key] = value;\n    }\n  }\n\n  return result;\n};\n\nconst createConfiguration = (configurationInput: ProxyAgentConfigurationInputType): ProxyAgentConfigurationType => {\n  // eslint-disable-next-line no-process-env\n  const environment = process.env;\n\n  const defaultConfiguration = {\n    environmentVariableNamespace: typeof environment.GLOBAL_AGENT_ENVIRONMENT_VARIABLE_NAMESPACE === 'string' ? environment.GLOBAL_AGENT_ENVIRONMENT_VARIABLE_NAMESPACE : 'GLOBAL_AGENT_',\n    forceGlobalAgent: typeof environment.GLOBAL_AGENT_FORCE_GLOBAL_AGENT === 'string' ? parseBoolean(environment.GLOBAL_AGENT_FORCE_GLOBAL_AGENT) : true,\n    socketConnectionTimeout: typeof environment.GLOBAL_AGENT_SOCKET_CONNECTION_TIMEOUT === 'string' ? Number.parseInt(environment.GLOBAL_AGENT_SOCKET_CONNECTION_TIMEOUT, 10) : defaultConfigurationInput.socketConnectionTimeout,\n  };\n\n  // $FlowFixMe\n  return {\n    ...defaultConfiguration,\n    ...omitUndefined(configurationInput),\n  };\n};\n\nexport default (configurationInput: ProxyAgentConfigurationInputType = defaultConfigurationInput) => {\n  const configuration = createConfiguration(configurationInput);\n\n  const proxyController = createProxyController();\n\n  // eslint-disable-next-line no-process-env\n  proxyController.HTTP_PROXY = process.env[configuration.environmentVariableNamespace + 'HTTP_PROXY'] || null;\n\n  // eslint-disable-next-line no-process-env\n  proxyController.HTTPS_PROXY = process.env[configuration.environmentVariableNamespace + 'HTTPS_PROXY'] || null;\n\n  // eslint-disable-next-line no-process-env\n  proxyController.NO_PROXY = process.env[configuration.environmentVariableNamespace + 'NO_PROXY'] || null;\n\n  log.info({\n    configuration,\n    state: proxyController,\n  }, 'global agent has been initialized');\n\n  const mustUrlUseProxy = (getProxy) => {\n    return (url) => {\n      if (!getProxy()) {\n        return false;\n      }\n\n      if (!proxyController.NO_PROXY) {\n        return true;\n      }\n\n      return !isUrlMatchingNoProxy(url, proxyController.NO_PROXY);\n    };\n  };\n\n  const getUrlProxy = (getProxy) => {\n    return () => {\n      const proxy = getProxy();\n\n      if (!proxy) {\n        throw new UnexpectedStateError('HTTP(S) proxy must be configured.');\n      }\n\n      return parseProxyUrl(proxy);\n    };\n  };\n\n  const getHttpProxy = () => {\n    return proxyController.HTTP_PROXY;\n  };\n\n  const BoundHttpProxyAgent = class extends HttpProxyAgent {\n    constructor () {\n      super(\n        () => {\n          return getHttpProxy();\n        },\n        mustUrlUseProxy(getHttpProxy),\n        getUrlProxy(getHttpProxy),\n        http.globalAgent,\n        configuration.socketConnectionTimeout,\n      );\n    }\n  };\n\n  const httpAgent = new BoundHttpProxyAgent();\n\n  const getHttpsProxy = () => {\n    return proxyController.HTTPS_PROXY || proxyController.HTTP_PROXY;\n  };\n\n  const BoundHttpsProxyAgent = class extends HttpsProxyAgent {\n    constructor () {\n      super(\n        () => {\n          return getHttpsProxy();\n        },\n        mustUrlUseProxy(getHttpsProxy),\n        getUrlProxy(getHttpsProxy),\n        https.globalAgent,\n        configuration.socketConnectionTimeout,\n      );\n    }\n  };\n\n  const httpsAgent = new BoundHttpsProxyAgent();\n\n  // Overriding globalAgent was added in v11.7.\n  // @see https://nodejs.org/uk/blog/release/v11.7.0/\n  if (semver.gte(process.version, 'v11.7.0')) {\n    // @see https://github.com/facebook/flow/issues/7670\n    // $FlowFixMe\n    http.globalAgent = httpAgent;\n\n    // $FlowFixMe\n    https.globalAgent = httpsAgent;\n  }\n\n  // The reason this logic is used in addition to overriding http(s).globalAgent\n  // is because there is no guarantee that we set http(s).globalAgent variable\n  // before an instance of http(s).Agent has been already constructed by someone,\n  // e.g. Stripe SDK creates instances of http(s).Agent at the top-level.\n  // @see https://github.com/gajus/global-agent/pull/13\n  //\n  // We still want to override http(s).globalAgent when possible to enable logic\n  // in `bindHttpMethod`.\n  if (semver.gte(process.version, 'v10.0.0')) {\n    // $FlowFixMe\n    http.get = bindHttpMethod(httpGet, httpAgent, configuration.forceGlobalAgent);\n\n    // $FlowFixMe\n    http.request = bindHttpMethod(httpRequest, httpAgent, configuration.forceGlobalAgent);\n\n    // $FlowFixMe\n    https.get = bindHttpMethod(httpsGet, httpsAgent, configuration.forceGlobalAgent);\n\n    // $FlowFixMe\n    https.request = bindHttpMethod(httpsRequest, httpsAgent, configuration.forceGlobalAgent);\n  } else {\n    log.warn('attempt to initialize global-agent in unsupported Node.js version was ignored');\n  }\n\n  return proxyController;\n};\n"], "file": "createGlobalProxyAgent.js"}