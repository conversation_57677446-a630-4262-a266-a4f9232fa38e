function px(a,r){for(var u=0;u<r.length;u++){const o=r[u];if(typeof o!="string"&&!Array.isArray(o)){for(const s in o)if(s!=="default"&&!(s in a)){const f=Object.getOwnPropertyDescriptor(o,s);f&&Object.defineProperty(a,s,f.get?f:{enumerable:!0,get:()=>o[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function u(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(s){if(s.ep)return;s.ep=!0;const f=u(s);fetch(s.href,f)}})();function mg(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var os={exports:{}},Lr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cp;function gx(){if(cp)return Lr;cp=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(o,s,f){var d=null;if(f!==void 0&&(d=""+f),s.key!==void 0&&(d=""+s.key),"key"in s){f={};for(var h in s)h!=="key"&&(f[h]=s[h])}else f=s;return s=f.ref,{$$typeof:a,type:o,key:d,ref:s!==void 0?s:null,props:f}}return Lr.Fragment=r,Lr.jsx=u,Lr.jsxs=u,Lr}var sp;function vx(){return sp||(sp=1,os.exports=gx()),os.exports}var b=vx(),us={exports:{}},ve={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fp;function yx(){if(fp)return ve;fp=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),w=Symbol.iterator;function S(C){return C===null||typeof C!="object"?null:(C=w&&C[w]||C["@@iterator"],typeof C=="function"?C:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,E={};function N(C,X,J){this.props=C,this.context=X,this.refs=E,this.updater=J||R}N.prototype.isReactComponent={},N.prototype.setState=function(C,X){if(typeof C!="object"&&typeof C!="function"&&C!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,C,X,"setState")},N.prototype.forceUpdate=function(C){this.updater.enqueueForceUpdate(this,C,"forceUpdate")};function O(){}O.prototype=N.prototype;function _(C,X,J){this.props=C,this.context=X,this.refs=E,this.updater=J||R}var D=_.prototype=new O;D.constructor=_,T(D,N.prototype),D.isPureReactComponent=!0;var G=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function P(C,X,J,$,I,pe){return J=pe.ref,{$$typeof:a,type:C,key:X,ref:J!==void 0?J:null,props:pe}}function Y(C,X){return P(C.type,X,void 0,void 0,void 0,C.props)}function ee(C){return typeof C=="object"&&C!==null&&C.$$typeof===a}function ce(C){var X={"=":"=0",":":"=2"};return"$"+C.replace(/[=:]/g,function(J){return X[J]})}var he=/\/+/g;function de(C,X){return typeof C=="object"&&C!==null&&C.key!=null?ce(""+C.key):X.toString(36)}function ge(){}function ye(C){switch(C.status){case"fulfilled":return C.value;case"rejected":throw C.reason;default:switch(typeof C.status=="string"?C.then(ge,ge):(C.status="pending",C.then(function(X){C.status==="pending"&&(C.status="fulfilled",C.value=X)},function(X){C.status==="pending"&&(C.status="rejected",C.reason=X)})),C.status){case"fulfilled":return C.value;case"rejected":throw C.reason}}throw C}function oe(C,X,J,$,I){var pe=typeof C;(pe==="undefined"||pe==="boolean")&&(C=null);var ie=!1;if(C===null)ie=!0;else switch(pe){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(C.$$typeof){case a:case r:ie=!0;break;case y:return ie=C._init,oe(ie(C._payload),X,J,$,I)}}if(ie)return I=I(C),ie=$===""?"."+de(C,0):$,G(I)?(J="",ie!=null&&(J=ie.replace(he,"$&/")+"/"),oe(I,X,J,"",function(De){return De})):I!=null&&(ee(I)&&(I=Y(I,J+(I.key==null||C&&C.key===I.key?"":(""+I.key).replace(he,"$&/")+"/")+ie)),X.push(I)),1;ie=0;var W=$===""?".":$+":";if(G(C))for(var ue=0;ue<C.length;ue++)$=C[ue],pe=W+de($,ue),ie+=oe($,X,J,pe,I);else if(ue=S(C),typeof ue=="function")for(C=ue.call(C),ue=0;!($=C.next()).done;)$=$.value,pe=W+de($,ue++),ie+=oe($,X,J,pe,I);else if(pe==="object"){if(typeof C.then=="function")return oe(ye(C),X,J,$,I);throw X=String(C),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(C).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return ie}function j(C,X,J){if(C==null)return C;var $=[],I=0;return oe(C,$,"","",function(pe){return X.call(J,pe,I++)}),$}function K(C){if(C._status===-1){var X=C._result;X=X(),X.then(function(J){(C._status===0||C._status===-1)&&(C._status=1,C._result=J)},function(J){(C._status===0||C._status===-1)&&(C._status=2,C._result=J)}),C._status===-1&&(C._status=0,C._result=X)}if(C._status===1)return C._result.default;throw C._result}var H=typeof reportError=="function"?reportError:function(C){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof C=="object"&&C!==null&&typeof C.message=="string"?String(C.message):String(C),error:C});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",C);return}console.error(C)};function le(){}return ve.Children={map:j,forEach:function(C,X,J){j(C,function(){X.apply(this,arguments)},J)},count:function(C){var X=0;return j(C,function(){X++}),X},toArray:function(C){return j(C,function(X){return X})||[]},only:function(C){if(!ee(C))throw Error("React.Children.only expected to receive a single React element child.");return C}},ve.Component=N,ve.Fragment=u,ve.Profiler=s,ve.PureComponent=_,ve.StrictMode=o,ve.Suspense=p,ve.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,ve.__COMPILER_RUNTIME={__proto__:null,c:function(C){return V.H.useMemoCache(C)}},ve.cache=function(C){return function(){return C.apply(null,arguments)}},ve.cloneElement=function(C,X,J){if(C==null)throw Error("The argument must be a React element, but you passed "+C+".");var $=T({},C.props),I=C.key,pe=void 0;if(X!=null)for(ie in X.ref!==void 0&&(pe=void 0),X.key!==void 0&&(I=""+X.key),X)!F.call(X,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&X.ref===void 0||($[ie]=X[ie]);var ie=arguments.length-2;if(ie===1)$.children=J;else if(1<ie){for(var W=Array(ie),ue=0;ue<ie;ue++)W[ue]=arguments[ue+2];$.children=W}return P(C.type,I,void 0,void 0,pe,$)},ve.createContext=function(C){return C={$$typeof:d,_currentValue:C,_currentValue2:C,_threadCount:0,Provider:null,Consumer:null},C.Provider=C,C.Consumer={$$typeof:f,_context:C},C},ve.createElement=function(C,X,J){var $,I={},pe=null;if(X!=null)for($ in X.key!==void 0&&(pe=""+X.key),X)F.call(X,$)&&$!=="key"&&$!=="__self"&&$!=="__source"&&(I[$]=X[$]);var ie=arguments.length-2;if(ie===1)I.children=J;else if(1<ie){for(var W=Array(ie),ue=0;ue<ie;ue++)W[ue]=arguments[ue+2];I.children=W}if(C&&C.defaultProps)for($ in ie=C.defaultProps,ie)I[$]===void 0&&(I[$]=ie[$]);return P(C,pe,void 0,void 0,null,I)},ve.createRef=function(){return{current:null}},ve.forwardRef=function(C){return{$$typeof:h,render:C}},ve.isValidElement=ee,ve.lazy=function(C){return{$$typeof:y,_payload:{_status:-1,_result:C},_init:K}},ve.memo=function(C,X){return{$$typeof:m,type:C,compare:X===void 0?null:X}},ve.startTransition=function(C){var X=V.T,J={};V.T=J;try{var $=C(),I=V.S;I!==null&&I(J,$),typeof $=="object"&&$!==null&&typeof $.then=="function"&&$.then(le,H)}catch(pe){H(pe)}finally{V.T=X}},ve.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},ve.use=function(C){return V.H.use(C)},ve.useActionState=function(C,X,J){return V.H.useActionState(C,X,J)},ve.useCallback=function(C,X){return V.H.useCallback(C,X)},ve.useContext=function(C){return V.H.useContext(C)},ve.useDebugValue=function(){},ve.useDeferredValue=function(C,X){return V.H.useDeferredValue(C,X)},ve.useEffect=function(C,X,J){var $=V.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return $.useEffect(C,X)},ve.useId=function(){return V.H.useId()},ve.useImperativeHandle=function(C,X,J){return V.H.useImperativeHandle(C,X,J)},ve.useInsertionEffect=function(C,X){return V.H.useInsertionEffect(C,X)},ve.useLayoutEffect=function(C,X){return V.H.useLayoutEffect(C,X)},ve.useMemo=function(C,X){return V.H.useMemo(C,X)},ve.useOptimistic=function(C,X){return V.H.useOptimistic(C,X)},ve.useReducer=function(C,X,J){return V.H.useReducer(C,X,J)},ve.useRef=function(C){return V.H.useRef(C)},ve.useState=function(C){return V.H.useState(C)},ve.useSyncExternalStore=function(C,X,J){return V.H.useSyncExternalStore(C,X,J)},ve.useTransition=function(){return V.H.useTransition()},ve.version="19.1.0",ve}var dp;function Ys(){return dp||(dp=1,us.exports=yx()),us.exports}var v=Ys();const en=mg(v),hg=px({__proto__:null,default:en},[v]);var cs={exports:{}},kr={},ss={exports:{}},fs={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mp;function bx(){return mp||(mp=1,function(a){function r(j,K){var H=j.length;j.push(K);e:for(;0<H;){var le=H-1>>>1,C=j[le];if(0<s(C,K))j[le]=K,j[H]=C,H=le;else break e}}function u(j){return j.length===0?null:j[0]}function o(j){if(j.length===0)return null;var K=j[0],H=j.pop();if(H!==K){j[0]=H;e:for(var le=0,C=j.length,X=C>>>1;le<X;){var J=2*(le+1)-1,$=j[J],I=J+1,pe=j[I];if(0>s($,H))I<C&&0>s(pe,$)?(j[le]=pe,j[I]=H,le=I):(j[le]=$,j[J]=H,le=J);else if(I<C&&0>s(pe,H))j[le]=pe,j[I]=H,le=I;else break e}}return K}function s(j,K){var H=j.sortIndex-K.sortIndex;return H!==0?H:j.id-K.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();a.unstable_now=function(){return d.now()-h}}var p=[],m=[],y=1,w=null,S=3,R=!1,T=!1,E=!1,N=!1,O=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;function G(j){for(var K=u(m);K!==null;){if(K.callback===null)o(m);else if(K.startTime<=j)o(m),K.sortIndex=K.expirationTime,r(p,K);else break;K=u(m)}}function V(j){if(E=!1,G(j),!T)if(u(p)!==null)T=!0,F||(F=!0,de());else{var K=u(m);K!==null&&oe(V,K.startTime-j)}}var F=!1,P=-1,Y=5,ee=-1;function ce(){return N?!0:!(a.unstable_now()-ee<Y)}function he(){if(N=!1,F){var j=a.unstable_now();ee=j;var K=!0;try{e:{T=!1,E&&(E=!1,_(P),P=-1),R=!0;var H=S;try{t:{for(G(j),w=u(p);w!==null&&!(w.expirationTime>j&&ce());){var le=w.callback;if(typeof le=="function"){w.callback=null,S=w.priorityLevel;var C=le(w.expirationTime<=j);if(j=a.unstable_now(),typeof C=="function"){w.callback=C,G(j),K=!0;break t}w===u(p)&&o(p),G(j)}else o(p);w=u(p)}if(w!==null)K=!0;else{var X=u(m);X!==null&&oe(V,X.startTime-j),K=!1}}break e}finally{w=null,S=H,R=!1}K=void 0}}finally{K?de():F=!1}}}var de;if(typeof D=="function")de=function(){D(he)};else if(typeof MessageChannel<"u"){var ge=new MessageChannel,ye=ge.port2;ge.port1.onmessage=he,de=function(){ye.postMessage(null)}}else de=function(){O(he,0)};function oe(j,K){P=O(function(){j(a.unstable_now())},K)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(j){j.callback=null},a.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<j?Math.floor(1e3/j):5},a.unstable_getCurrentPriorityLevel=function(){return S},a.unstable_next=function(j){switch(S){case 1:case 2:case 3:var K=3;break;default:K=S}var H=S;S=K;try{return j()}finally{S=H}},a.unstable_requestPaint=function(){N=!0},a.unstable_runWithPriority=function(j,K){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var H=S;S=j;try{return K()}finally{S=H}},a.unstable_scheduleCallback=function(j,K,H){var le=a.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?le+H:le):H=le,j){case 1:var C=-1;break;case 2:C=250;break;case 5:C=1073741823;break;case 4:C=1e4;break;default:C=5e3}return C=H+C,j={id:y++,callback:K,priorityLevel:j,startTime:H,expirationTime:C,sortIndex:-1},H>le?(j.sortIndex=H,r(m,j),u(p)===null&&j===u(m)&&(E?(_(P),P=-1):E=!0,oe(V,H-le))):(j.sortIndex=C,r(p,j),T||R||(T=!0,F||(F=!0,de()))),j},a.unstable_shouldYield=ce,a.unstable_wrapCallback=function(j){var K=S;return function(){var H=S;S=K;try{return j.apply(this,arguments)}finally{S=H}}}}(fs)),fs}var hp;function xx(){return hp||(hp=1,ss.exports=bx()),ss.exports}var ds={exports:{}},ft={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pp;function Sx(){if(pp)return ft;pp=1;var a=Ys();function r(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)m+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(p,m,y){var w=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:w==null?null:""+w,children:p,containerInfo:m,implementation:y}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return ft.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,ft.createPortal=function(p,m){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(r(299));return f(p,m,null,y)},ft.flushSync=function(p){var m=d.T,y=o.p;try{if(d.T=null,o.p=2,p)return p()}finally{d.T=m,o.p=y,o.d.f()}},ft.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,o.d.C(p,m))},ft.prefetchDNS=function(p){typeof p=="string"&&o.d.D(p)},ft.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var y=m.as,w=h(y,m.crossOrigin),S=typeof m.integrity=="string"?m.integrity:void 0,R=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;y==="style"?o.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:w,integrity:S,fetchPriority:R}):y==="script"&&o.d.X(p,{crossOrigin:w,integrity:S,fetchPriority:R,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},ft.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var y=h(m.as,m.crossOrigin);o.d.M(p,{crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&o.d.M(p)},ft.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var y=m.as,w=h(y,m.crossOrigin);o.d.L(p,y,{crossOrigin:w,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},ft.preloadModule=function(p,m){if(typeof p=="string")if(m){var y=h(m.as,m.crossOrigin);o.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else o.d.m(p)},ft.requestFormReset=function(p){o.d.r(p)},ft.unstable_batchedUpdates=function(p,m){return p(m)},ft.useFormState=function(p,m,y){return d.H.useFormState(p,m,y)},ft.useFormStatus=function(){return d.H.useHostTransitionStatus()},ft.version="19.1.0",ft}var gp;function pg(){if(gp)return ds.exports;gp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),ds.exports=Sx(),ds.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vp;function wx(){if(vp)return kr;vp=1;var a=xx(),r=Ys(),u=pg();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(o(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,l=t;;){var i=n.return;if(i===null)break;var c=i.alternate;if(c===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===c.child){for(c=i.child;c;){if(c===n)return h(i),e;if(c===l)return h(i),t;c=c.sibling}throw Error(o(188))}if(n.return!==l.return)n=i,l=c;else{for(var g=!1,x=i.child;x;){if(x===n){g=!0,n=i,l=c;break}if(x===l){g=!0,l=i,n=c;break}x=x.sibling}if(!g){for(x=c.child;x;){if(x===n){g=!0,n=c,l=i;break}if(x===l){g=!0,l=c,n=i;break}x=x.sibling}if(!g)throw Error(o(189))}}if(n.alternate!==l)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,w=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),O=Symbol.for("react.provider"),_=Symbol.for("react.consumer"),D=Symbol.for("react.context"),G=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),ee=Symbol.for("react.activity"),ce=Symbol.for("react.memo_cache_sentinel"),he=Symbol.iterator;function de(e){return e===null||typeof e!="object"?null:(e=he&&e[he]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Symbol.for("react.client.reference");function ye(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ge?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case T:return"Fragment";case N:return"Profiler";case E:return"StrictMode";case V:return"Suspense";case F:return"SuspenseList";case ee:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case R:return"Portal";case D:return(e.displayName||"Context")+".Provider";case _:return(e._context.displayName||"Context")+".Consumer";case G:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case P:return t=e.displayName||null,t!==null?t:ye(e.type)||"Memo";case Y:t=e._payload,e=e._init;try{return ye(e(t))}catch{}}return null}var oe=Array.isArray,j=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H={pending:!1,data:null,method:null,action:null},le=[],C=-1;function X(e){return{current:e}}function J(e){0>C||(e.current=le[C],le[C]=null,C--)}function $(e,t){C++,le[C]=e.current,e.current=t}var I=X(null),pe=X(null),ie=X(null),W=X(null);function ue(e,t){switch($(ie,t),$(pe,e),$(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?kh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=kh(t),e=Bh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(I),$(I,e)}function De(){J(I),J(pe),J(ie)}function Re(e){e.memoizedState!==null&&$(W,e);var t=I.current,n=Bh(t,e.type);t!==n&&($(pe,e),$(I,n))}function we(e){pe.current===e&&(J(I),J(pe)),W.current===e&&(J(W),_r._currentValue=H)}var Ee=Object.prototype.hasOwnProperty,lt=a.unstable_scheduleCallback,pt=a.unstable_cancelCallback,ca=a.unstable_shouldYield,sa=a.unstable_requestPaint,ut=a.unstable_now,$o=a.unstable_getCurrentPriorityLevel,fa=a.unstable_ImmediatePriority,yf=a.unstable_UserBlockingPriority,ei=a.unstable_NormalPriority,Fy=a.unstable_LowPriority,bf=a.unstable_IdlePriority,Jy=a.log,Wy=a.unstable_setDisableYieldValue,Hl=null,St=null;function Mn(e){if(typeof Jy=="function"&&Wy(e),St&&typeof St.setStrictMode=="function")try{St.setStrictMode(Hl,e)}catch{}}var wt=Math.clz32?Math.clz32:t0,Iy=Math.log,e0=Math.LN2;function t0(e){return e>>>=0,e===0?32:31-(Iy(e)/e0|0)|0}var ti=256,ni=4194304;function da(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ai(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var i=0,c=e.suspendedLanes,g=e.pingedLanes;e=e.warmLanes;var x=l&134217727;return x!==0?(l=x&~c,l!==0?i=da(l):(g&=x,g!==0?i=da(g):n||(n=x&~e,n!==0&&(i=da(n))))):(x=l&~c,x!==0?i=da(x):g!==0?i=da(g):n||(n=l&~e,n!==0&&(i=da(n)))),i===0?0:t!==0&&t!==i&&(t&c)===0&&(c=i&-i,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:i}function Gl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function n0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xf(){var e=ti;return ti<<=1,(ti&4194048)===0&&(ti=256),e}function Sf(){var e=ni;return ni<<=1,(ni&62914560)===0&&(ni=4194304),e}function Fo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Vl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function a0(e,t,n,l,i,c){var g=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var x=e.entanglements,A=e.expirationTimes,L=e.hiddenUpdates;for(n=g&~n;0<n;){var q=31-wt(n),Z=1<<q;x[q]=0,A[q]=-1;var k=L[q];if(k!==null)for(L[q]=null,q=0;q<k.length;q++){var B=k[q];B!==null&&(B.lane&=-536870913)}n&=~Z}l!==0&&wf(e,l,0),c!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=c&~(g&~t))}function wf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-wt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function Ef(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-wt(n),i=1<<l;i&t|e[l]&t&&(e[l]|=t),n&=~i}}function Jo(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Wo(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Tf(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:ap(e.type))}function l0(e,t){var n=K.p;try{return K.p=e,t()}finally{K.p=n}}var _n=Math.random().toString(36).slice(2),ct="__reactFiber$"+_n,gt="__reactProps$"+_n,ka="__reactContainer$"+_n,Io="__reactEvents$"+_n,r0="__reactListeners$"+_n,i0="__reactHandles$"+_n,Af="__reactResources$"+_n,ql="__reactMarker$"+_n;function eu(e){delete e[ct],delete e[gt],delete e[Io],delete e[r0],delete e[i0]}function Ba(e){var t=e[ct];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ka]||n[ct]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qh(e);e!==null;){if(n=e[ct])return n;e=qh(e)}return t}e=n,n=e.parentNode}return null}function Ha(e){if(e=e[ct]||e[ka]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Yl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function Ga(e){var t=e[Af];return t||(t=e[Af]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function et(e){e[ql]=!0}var Rf=new Set,Cf={};function ma(e,t){Va(e,t),Va(e+"Capture",t)}function Va(e,t){for(Cf[e]=t,e=0;e<t.length;e++)Rf.add(t[e])}var o0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Nf={},Of={};function u0(e){return Ee.call(Of,e)?!0:Ee.call(Nf,e)?!1:o0.test(e)?Of[e]=!0:(Nf[e]=!0,!1)}function li(e,t,n){if(u0(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function ri(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function un(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var tu,Mf;function qa(e){if(tu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);tu=t&&t[1]||"",Mf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+tu+e+Mf}var nu=!1;function au(e,t){if(!e||nu)return"";nu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Z=function(){throw Error()};if(Object.defineProperty(Z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Z,[])}catch(B){var k=B}Reflect.construct(e,[],Z)}else{try{Z.call()}catch(B){k=B}e.call(Z.prototype)}}else{try{throw Error()}catch(B){k=B}(Z=e())&&typeof Z.catch=="function"&&Z.catch(function(){})}}catch(B){if(B&&k&&typeof B.stack=="string")return[B.stack,k.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),g=c[0],x=c[1];if(g&&x){var A=g.split(`
`),L=x.split(`
`);for(i=l=0;l<A.length&&!A[l].includes("DetermineComponentFrameRoot");)l++;for(;i<L.length&&!L[i].includes("DetermineComponentFrameRoot");)i++;if(l===A.length||i===L.length)for(l=A.length-1,i=L.length-1;1<=l&&0<=i&&A[l]!==L[i];)i--;for(;1<=l&&0<=i;l--,i--)if(A[l]!==L[i]){if(l!==1||i!==1)do if(l--,i--,0>i||A[l]!==L[i]){var q=`
`+A[l].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=l&&0<=i);break}}}finally{nu=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?qa(n):""}function c0(e){switch(e.tag){case 26:case 27:case 5:return qa(e.type);case 16:return qa("Lazy");case 13:return qa("Suspense");case 19:return qa("SuspenseList");case 0:case 15:return au(e.type,!1);case 11:return au(e.type.render,!1);case 1:return au(e.type,!0);case 31:return qa("Activity");default:return""}}function _f(e){try{var t="";do t+=c0(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Df(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function s0(e){var t=Df(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(g){l=""+g,c.call(this,g)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(g){l=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ii(e){e._valueTracker||(e._valueTracker=s0(e))}function zf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=Df(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function oi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var f0=/[\n"\\]/g;function zt(e){return e.replace(f0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function lu(e,t,n,l,i,c,g,x){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),t!=null?g==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Dt(t)):e.value!==""+Dt(t)&&(e.value=""+Dt(t)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),t!=null?ru(e,g,Dt(t)):n!=null?ru(e,g,Dt(n)):l!=null&&e.removeAttribute("value"),i==null&&c!=null&&(e.defaultChecked=!!c),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?e.name=""+Dt(x):e.removeAttribute("name")}function jf(e,t,n,l,i,c,g,x){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+Dt(n):"",t=t!=null?""+Dt(t):n,x||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=x?e.checked:!!l,e.defaultChecked=!!l,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function ru(e,t,n){t==="number"&&oi(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ya(e,t,n,l){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&l&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Uf(e,t,n){if(t!=null&&(t=""+Dt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Dt(n):""}function Lf(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(o(92));if(oe(l)){if(1<l.length)throw Error(o(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=Dt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function Xa(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var d0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function kf(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||d0.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Bf(e,t,n){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&n[i]!==l&&kf(e,i,l)}else for(var c in t)t.hasOwnProperty(c)&&kf(e,c,t[c])}function iu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var m0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),h0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ui(e){return h0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var ou=null;function uu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Qa=null,Za=null;function Hf(e){var t=Ha(e);if(t&&(e=t.stateNode)){var n=e[gt]||null;e:switch(e=t.stateNode,t.type){case"input":if(lu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+zt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var i=l[gt]||null;if(!i)throw Error(o(90));lu(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&zf(l)}break e;case"textarea":Uf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ya(e,!!n.multiple,t,!1)}}}var cu=!1;function Gf(e,t,n){if(cu)return e(t,n);cu=!0;try{var l=e(t);return l}finally{if(cu=!1,(Qa!==null||Za!==null)&&(Ki(),Qa&&(t=Qa,e=Za,Za=Qa=null,Hf(t),e)))for(t=0;t<e.length;t++)Hf(e[t])}}function Xl(e,t){var n=e.stateNode;if(n===null)return null;var l=n[gt]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var cn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),su=!1;if(cn)try{var Ql={};Object.defineProperty(Ql,"passive",{get:function(){su=!0}}),window.addEventListener("test",Ql,Ql),window.removeEventListener("test",Ql,Ql)}catch{su=!1}var Dn=null,fu=null,ci=null;function Vf(){if(ci)return ci;var e,t=fu,n=t.length,l,i="value"in Dn?Dn.value:Dn.textContent,c=i.length;for(e=0;e<n&&t[e]===i[e];e++);var g=n-e;for(l=1;l<=g&&t[n-l]===i[c-l];l++);return ci=i.slice(e,1<l?1-l:void 0)}function si(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function fi(){return!0}function qf(){return!1}function vt(e){function t(n,l,i,c,g){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=c,this.target=g,this.currentTarget=null;for(var x in e)e.hasOwnProperty(x)&&(n=e[x],this[x]=n?n(c):c[x]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?fi:qf,this.isPropagationStopped=qf,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fi)},persist:function(){},isPersistent:fi}),t}var ha={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},di=vt(ha),Zl=y({},ha,{view:0,detail:0}),p0=vt(Zl),du,mu,Kl,mi=y({},Zl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Kl&&(Kl&&e.type==="mousemove"?(du=e.screenX-Kl.screenX,mu=e.screenY-Kl.screenY):mu=du=0,Kl=e),du)},movementY:function(e){return"movementY"in e?e.movementY:mu}}),Yf=vt(mi),g0=y({},mi,{dataTransfer:0}),v0=vt(g0),y0=y({},Zl,{relatedTarget:0}),hu=vt(y0),b0=y({},ha,{animationName:0,elapsedTime:0,pseudoElement:0}),x0=vt(b0),S0=y({},ha,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),w0=vt(S0),E0=y({},ha,{data:0}),Xf=vt(E0),T0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},A0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},R0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function C0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=R0[e])?!!t[e]:!1}function pu(){return C0}var N0=y({},Zl,{key:function(e){if(e.key){var t=T0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=si(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?A0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pu,charCode:function(e){return e.type==="keypress"?si(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?si(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),O0=vt(N0),M0=y({},mi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qf=vt(M0),_0=y({},Zl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pu}),D0=vt(_0),z0=y({},ha,{propertyName:0,elapsedTime:0,pseudoElement:0}),j0=vt(z0),U0=y({},mi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),L0=vt(U0),k0=y({},ha,{newState:0,oldState:0}),B0=vt(k0),H0=[9,13,27,32],gu=cn&&"CompositionEvent"in window,Pl=null;cn&&"documentMode"in document&&(Pl=document.documentMode);var G0=cn&&"TextEvent"in window&&!Pl,Zf=cn&&(!gu||Pl&&8<Pl&&11>=Pl),Kf=" ",Pf=!1;function $f(e,t){switch(e){case"keyup":return H0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ff(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ka=!1;function V0(e,t){switch(e){case"compositionend":return Ff(t);case"keypress":return t.which!==32?null:(Pf=!0,Kf);case"textInput":return e=t.data,e===Kf&&Pf?null:e;default:return null}}function q0(e,t){if(Ka)return e==="compositionend"||!gu&&$f(e,t)?(e=Vf(),ci=fu=Dn=null,Ka=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Zf&&t.locale!=="ko"?null:t.data;default:return null}}var Y0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Jf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Y0[e.type]:t==="textarea"}function Wf(e,t,n,l){Qa?Za?Za.push(l):Za=[l]:Qa=l,t=Ii(t,"onChange"),0<t.length&&(n=new di("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var $l=null,Fl=null;function X0(e){Dh(e,0)}function hi(e){var t=Yl(e);if(zf(t))return e}function If(e,t){if(e==="change")return t}var ed=!1;if(cn){var vu;if(cn){var yu="oninput"in document;if(!yu){var td=document.createElement("div");td.setAttribute("oninput","return;"),yu=typeof td.oninput=="function"}vu=yu}else vu=!1;ed=vu&&(!document.documentMode||9<document.documentMode)}function nd(){$l&&($l.detachEvent("onpropertychange",ad),Fl=$l=null)}function ad(e){if(e.propertyName==="value"&&hi(Fl)){var t=[];Wf(t,Fl,e,uu(e)),Gf(X0,t)}}function Q0(e,t,n){e==="focusin"?(nd(),$l=t,Fl=n,$l.attachEvent("onpropertychange",ad)):e==="focusout"&&nd()}function Z0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hi(Fl)}function K0(e,t){if(e==="click")return hi(t)}function P0(e,t){if(e==="input"||e==="change")return hi(t)}function $0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:$0;function Jl(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Ee.call(t,i)||!Et(e[i],t[i]))return!1}return!0}function ld(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function rd(e,t){var n=ld(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ld(n)}}function id(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?id(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function od(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=oi(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=oi(e.document)}return t}function bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var F0=cn&&"documentMode"in document&&11>=document.documentMode,Pa=null,xu=null,Wl=null,Su=!1;function ud(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Su||Pa==null||Pa!==oi(l)||(l=Pa,"selectionStart"in l&&bu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Wl&&Jl(Wl,l)||(Wl=l,l=Ii(xu,"onSelect"),0<l.length&&(t=new di("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Pa)))}function pa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $a={animationend:pa("Animation","AnimationEnd"),animationiteration:pa("Animation","AnimationIteration"),animationstart:pa("Animation","AnimationStart"),transitionrun:pa("Transition","TransitionRun"),transitionstart:pa("Transition","TransitionStart"),transitioncancel:pa("Transition","TransitionCancel"),transitionend:pa("Transition","TransitionEnd")},wu={},cd={};cn&&(cd=document.createElement("div").style,"AnimationEvent"in window||(delete $a.animationend.animation,delete $a.animationiteration.animation,delete $a.animationstart.animation),"TransitionEvent"in window||delete $a.transitionend.transition);function ga(e){if(wu[e])return wu[e];if(!$a[e])return e;var t=$a[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in cd)return wu[e]=t[n];return e}var sd=ga("animationend"),fd=ga("animationiteration"),dd=ga("animationstart"),J0=ga("transitionrun"),W0=ga("transitionstart"),I0=ga("transitioncancel"),md=ga("transitionend"),hd=new Map,Eu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Eu.push("scrollEnd");function Vt(e,t){hd.set(e,t),ma(t,[e])}var pd=new WeakMap;function jt(e,t){if(typeof e=="object"&&e!==null){var n=pd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:_f(t)},pd.set(e,t),t)}return{value:e,source:t,stack:_f(t)}}var Ut=[],Fa=0,Tu=0;function pi(){for(var e=Fa,t=Tu=Fa=0;t<e;){var n=Ut[t];Ut[t++]=null;var l=Ut[t];Ut[t++]=null;var i=Ut[t];Ut[t++]=null;var c=Ut[t];if(Ut[t++]=null,l!==null&&i!==null){var g=l.pending;g===null?i.next=i:(i.next=g.next,g.next=i),l.pending=i}c!==0&&gd(n,i,c)}}function gi(e,t,n,l){Ut[Fa++]=e,Ut[Fa++]=t,Ut[Fa++]=n,Ut[Fa++]=l,Tu|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Au(e,t,n,l){return gi(e,t,n,l),vi(e)}function Ja(e,t){return gi(e,null,null,t),vi(e)}function gd(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var i=!1,c=e.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(i=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,i&&t!==null&&(i=31-wt(n),e=c.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=n|536870912),c):null}function vi(e){if(50<Er)throw Er=0,_c=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Wa={};function eb(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tt(e,t,n,l){return new eb(e,t,n,l)}function Ru(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sn(e,t){var n=e.alternate;return n===null?(n=Tt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function vd(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function yi(e,t,n,l,i,c){var g=0;if(l=e,typeof e=="function")Ru(e)&&(g=1);else if(typeof e=="string")g=nx(e,n,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ee:return e=Tt(31,n,t,i),e.elementType=ee,e.lanes=c,e;case T:return va(n.children,i,c,t);case E:g=8,i|=24;break;case N:return e=Tt(12,n,t,i|2),e.elementType=N,e.lanes=c,e;case V:return e=Tt(13,n,t,i),e.elementType=V,e.lanes=c,e;case F:return e=Tt(19,n,t,i),e.elementType=F,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case O:case D:g=10;break e;case _:g=9;break e;case G:g=11;break e;case P:g=14;break e;case Y:g=16,l=null;break e}g=29,n=Error(o(130,e===null?"null":typeof e,"")),l=null}return t=Tt(g,n,t,i),t.elementType=e,t.type=l,t.lanes=c,t}function va(e,t,n,l){return e=Tt(7,e,l,t),e.lanes=n,e}function Cu(e,t,n){return e=Tt(6,e,null,t),e.lanes=n,e}function Nu(e,t,n){return t=Tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ia=[],el=0,bi=null,xi=0,Lt=[],kt=0,ya=null,fn=1,dn="";function ba(e,t){Ia[el++]=xi,Ia[el++]=bi,bi=e,xi=t}function yd(e,t,n){Lt[kt++]=fn,Lt[kt++]=dn,Lt[kt++]=ya,ya=e;var l=fn;e=dn;var i=32-wt(l)-1;l&=~(1<<i),n+=1;var c=32-wt(t)+i;if(30<c){var g=i-i%5;c=(l&(1<<g)-1).toString(32),l>>=g,i-=g,fn=1<<32-wt(t)+i|n<<i|l,dn=c+e}else fn=1<<c|n<<i|l,dn=e}function Ou(e){e.return!==null&&(ba(e,1),yd(e,1,0))}function Mu(e){for(;e===bi;)bi=Ia[--el],Ia[el]=null,xi=Ia[--el],Ia[el]=null;for(;e===ya;)ya=Lt[--kt],Lt[kt]=null,dn=Lt[--kt],Lt[kt]=null,fn=Lt[--kt],Lt[kt]=null}var mt=null,Qe=null,_e=!1,xa=null,$t=!1,_u=Error(o(519));function Sa(e){var t=Error(o(418,""));throw tr(jt(t,e)),_u}function bd(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[ct]=e,t[gt]=l,n){case"dialog":Ae("cancel",t),Ae("close",t);break;case"iframe":case"object":case"embed":Ae("load",t);break;case"video":case"audio":for(n=0;n<Ar.length;n++)Ae(Ar[n],t);break;case"source":Ae("error",t);break;case"img":case"image":case"link":Ae("error",t),Ae("load",t);break;case"details":Ae("toggle",t);break;case"input":Ae("invalid",t),jf(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ii(t);break;case"select":Ae("invalid",t);break;case"textarea":Ae("invalid",t),Lf(t,l.value,l.defaultValue,l.children),ii(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||Lh(t.textContent,n)?(l.popover!=null&&(Ae("beforetoggle",t),Ae("toggle",t)),l.onScroll!=null&&Ae("scroll",t),l.onScrollEnd!=null&&Ae("scrollend",t),l.onClick!=null&&(t.onclick=eo),t=!0):t=!1,t||Sa(e)}function xd(e){for(mt=e.return;mt;)switch(mt.tag){case 5:case 13:$t=!1;return;case 27:case 3:$t=!0;return;default:mt=mt.return}}function Il(e){if(e!==mt)return!1;if(!_e)return xd(e),_e=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Kc(e.type,e.memoizedProps)),n=!n),n&&Qe&&Sa(e),xd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Qe=Yt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Qe=null}}else t===27?(t=Qe,Pn(e.type)?(e=Jc,Jc=null,Qe=e):Qe=t):Qe=mt?Yt(e.stateNode.nextSibling):null;return!0}function er(){Qe=mt=null,_e=!1}function Sd(){var e=xa;return e!==null&&(xt===null?xt=e:xt.push.apply(xt,e),xa=null),e}function tr(e){xa===null?xa=[e]:xa.push(e)}var Du=X(null),wa=null,mn=null;function zn(e,t,n){$(Du,t._currentValue),t._currentValue=n}function hn(e){e._currentValue=Du.current,J(Du)}function zu(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function ju(e,t,n,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var c=i.dependencies;if(c!==null){var g=i.child;c=c.firstContext;e:for(;c!==null;){var x=c;c=i;for(var A=0;A<t.length;A++)if(x.context===t[A]){c.lanes|=n,x=c.alternate,x!==null&&(x.lanes|=n),zu(c.return,n,e),l||(g=null);break e}c=x.next}}else if(i.tag===18){if(g=i.return,g===null)throw Error(o(341));g.lanes|=n,c=g.alternate,c!==null&&(c.lanes|=n),zu(g,n,e),g=null}else g=i.child;if(g!==null)g.return=i;else for(g=i;g!==null;){if(g===e){g=null;break}if(i=g.sibling,i!==null){i.return=g.return,g=i;break}g=g.return}i=g}}function nr(e,t,n,l){e=null;for(var i=t,c=!1;i!==null;){if(!c){if((i.flags&524288)!==0)c=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var g=i.alternate;if(g===null)throw Error(o(387));if(g=g.memoizedProps,g!==null){var x=i.type;Et(i.pendingProps.value,g.value)||(e!==null?e.push(x):e=[x])}}else if(i===W.current){if(g=i.alternate,g===null)throw Error(o(387));g.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(_r):e=[_r])}i=i.return}e!==null&&ju(t,e,n,l),t.flags|=262144}function Si(e){for(e=e.firstContext;e!==null;){if(!Et(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ea(e){wa=e,mn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function st(e){return wd(wa,e)}function wi(e,t){return wa===null&&Ea(e),wd(e,t)}function wd(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},mn===null){if(e===null)throw Error(o(308));mn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else mn=mn.next=t;return n}var tb=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},nb=a.unstable_scheduleCallback,ab=a.unstable_NormalPriority,Je={$$typeof:D,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Uu(){return{controller:new tb,data:new Map,refCount:0}}function ar(e){e.refCount--,e.refCount===0&&nb(ab,function(){e.controller.abort()})}var lr=null,Lu=0,tl=0,nl=null;function lb(e,t){if(lr===null){var n=lr=[];Lu=0,tl=Bc(),nl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Lu++,t.then(Ed,Ed),t}function Ed(){if(--Lu===0&&lr!==null){nl!==null&&(nl.status="fulfilled");var e=lr;lr=null,tl=0,nl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function rb(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var Td=j.S;j.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&lb(e,t),Td!==null&&Td(e,t)};var Ta=X(null);function ku(){var e=Ta.current;return e!==null?e:He.pooledCache}function Ei(e,t){t===null?$(Ta,Ta.current):$(Ta,t.pool)}function Ad(){var e=ku();return e===null?null:{parent:Je._currentValue,pool:e}}var rr=Error(o(460)),Rd=Error(o(474)),Ti=Error(o(542)),Bu={then:function(){}};function Cd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ai(){}function Nd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Ai,Ai),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Md(e),e;default:if(typeof t.status=="string")t.then(Ai,Ai);else{if(e=He,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Md(e),e}throw ir=t,rr}}var ir=null;function Od(){if(ir===null)throw Error(o(459));var e=ir;return ir=null,e}function Md(e){if(e===rr||e===Ti)throw Error(o(483))}var jn=!1;function Hu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Gu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Un(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Ln(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ze&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=vi(e),gd(e,null,n),t}return gi(e,l,t,n),vi(e)}function or(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Ef(e,n)}}function Vu(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var g={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?i=c=g:c=c.next=g,n=n.next}while(n!==null);c===null?i=c=t:c=c.next=t}else i=c=t;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var qu=!1;function ur(){if(qu){var e=nl;if(e!==null)throw e}}function cr(e,t,n,l){qu=!1;var i=e.updateQueue;jn=!1;var c=i.firstBaseUpdate,g=i.lastBaseUpdate,x=i.shared.pending;if(x!==null){i.shared.pending=null;var A=x,L=A.next;A.next=null,g===null?c=L:g.next=L,g=A;var q=e.alternate;q!==null&&(q=q.updateQueue,x=q.lastBaseUpdate,x!==g&&(x===null?q.firstBaseUpdate=L:x.next=L,q.lastBaseUpdate=A))}if(c!==null){var Z=i.baseState;g=0,q=L=A=null,x=c;do{var k=x.lane&-536870913,B=k!==x.lane;if(B?(Oe&k)===k:(l&k)===k){k!==0&&k===tl&&(qu=!0),q!==null&&(q=q.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});e:{var me=e,se=x;k=t;var ke=n;switch(se.tag){case 1:if(me=se.payload,typeof me=="function"){Z=me.call(ke,Z,k);break e}Z=me;break e;case 3:me.flags=me.flags&-65537|128;case 0:if(me=se.payload,k=typeof me=="function"?me.call(ke,Z,k):me,k==null)break e;Z=y({},Z,k);break e;case 2:jn=!0}}k=x.callback,k!==null&&(e.flags|=64,B&&(e.flags|=8192),B=i.callbacks,B===null?i.callbacks=[k]:B.push(k))}else B={lane:k,tag:x.tag,payload:x.payload,callback:x.callback,next:null},q===null?(L=q=B,A=Z):q=q.next=B,g|=k;if(x=x.next,x===null){if(x=i.shared.pending,x===null)break;B=x,x=B.next,B.next=null,i.lastBaseUpdate=B,i.shared.pending=null}}while(!0);q===null&&(A=Z),i.baseState=A,i.firstBaseUpdate=L,i.lastBaseUpdate=q,c===null&&(i.shared.lanes=0),Xn|=g,e.lanes=g,e.memoizedState=Z}}function _d(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Dd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)_d(n[e],t)}var al=X(null),Ri=X(0);function zd(e,t){e=Sn,$(Ri,e),$(al,t),Sn=e|t.baseLanes}function Yu(){$(Ri,Sn),$(al,al.current)}function Xu(){Sn=Ri.current,J(al),J(Ri)}var kn=0,be=null,Ue=null,$e=null,Ci=!1,ll=!1,Aa=!1,Ni=0,sr=0,rl=null,ib=0;function Ke(){throw Error(o(321))}function Qu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Et(e[n],t[n]))return!1;return!0}function Zu(e,t,n,l,i,c){return kn=c,be=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,j.H=e===null||e.memoizedState===null?gm:vm,Aa=!1,c=n(l,i),Aa=!1,ll&&(c=Ud(t,n,l,i)),jd(e),c}function jd(e){j.H=ji;var t=Ue!==null&&Ue.next!==null;if(kn=0,$e=Ue=be=null,Ci=!1,sr=0,rl=null,t)throw Error(o(300));e===null||tt||(e=e.dependencies,e!==null&&Si(e)&&(tt=!0))}function Ud(e,t,n,l){be=e;var i=0;do{if(ll&&(rl=null),sr=0,ll=!1,25<=i)throw Error(o(301));if(i+=1,$e=Ue=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}j.H=mb,c=t(n,l)}while(ll);return c}function ob(){var e=j.H,t=e.useState()[0];return t=typeof t.then=="function"?fr(t):t,e=e.useState()[0],(Ue!==null?Ue.memoizedState:null)!==e&&(be.flags|=1024),t}function Ku(){var e=Ni!==0;return Ni=0,e}function Pu(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function $u(e){if(Ci){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ci=!1}kn=0,$e=Ue=be=null,ll=!1,sr=Ni=0,rl=null}function yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?be.memoizedState=$e=e:$e=$e.next=e,$e}function Fe(){if(Ue===null){var e=be.alternate;e=e!==null?e.memoizedState:null}else e=Ue.next;var t=$e===null?be.memoizedState:$e.next;if(t!==null)$e=t,Ue=e;else{if(e===null)throw be.alternate===null?Error(o(467)):Error(o(310));Ue=e,e={memoizedState:Ue.memoizedState,baseState:Ue.baseState,baseQueue:Ue.baseQueue,queue:Ue.queue,next:null},$e===null?be.memoizedState=$e=e:$e=$e.next=e}return $e}function Fu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function fr(e){var t=sr;return sr+=1,rl===null&&(rl=[]),e=Nd(rl,e,t),t=be,($e===null?t.memoizedState:$e.next)===null&&(t=t.alternate,j.H=t===null||t.memoizedState===null?gm:vm),e}function Oi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return fr(e);if(e.$$typeof===D)return st(e)}throw Error(o(438,String(e)))}function Ju(e){var t=null,n=be.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=be.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Fu(),be.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=ce;return t.index++,n}function pn(e,t){return typeof t=="function"?t(e):t}function Mi(e){var t=Fe();return Wu(t,Ue,e)}function Wu(e,t,n){var l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=n;var i=e.baseQueue,c=l.pending;if(c!==null){if(i!==null){var g=i.next;i.next=c.next,c.next=g}t.baseQueue=i=c,l.pending=null}if(c=e.baseState,i===null)e.memoizedState=c;else{t=i.next;var x=g=null,A=null,L=t,q=!1;do{var Z=L.lane&-536870913;if(Z!==L.lane?(Oe&Z)===Z:(kn&Z)===Z){var k=L.revertLane;if(k===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null}),Z===tl&&(q=!0);else if((kn&k)===k){L=L.next,k===tl&&(q=!0);continue}else Z={lane:0,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},A===null?(x=A=Z,g=c):A=A.next=Z,be.lanes|=k,Xn|=k;Z=L.action,Aa&&n(c,Z),c=L.hasEagerState?L.eagerState:n(c,Z)}else k={lane:Z,revertLane:L.revertLane,action:L.action,hasEagerState:L.hasEagerState,eagerState:L.eagerState,next:null},A===null?(x=A=k,g=c):A=A.next=k,be.lanes|=Z,Xn|=Z;L=L.next}while(L!==null&&L!==t);if(A===null?g=c:A.next=x,!Et(c,e.memoizedState)&&(tt=!0,q&&(n=nl,n!==null)))throw n;e.memoizedState=c,e.baseState=g,e.baseQueue=A,l.lastRenderedState=c}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Iu(e){var t=Fe(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var l=n.dispatch,i=n.pending,c=t.memoizedState;if(i!==null){n.pending=null;var g=i=i.next;do c=e(c,g.action),g=g.next;while(g!==i);Et(c,t.memoizedState)||(tt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,l]}function Ld(e,t,n){var l=be,i=Fe(),c=_e;if(c){if(n===void 0)throw Error(o(407));n=n()}else n=t();var g=!Et((Ue||i).memoizedState,n);g&&(i.memoizedState=n,tt=!0),i=i.queue;var x=Hd.bind(null,l,i,e);if(dr(2048,8,x,[e]),i.getSnapshot!==t||g||$e!==null&&$e.memoizedState.tag&1){if(l.flags|=2048,il(9,_i(),Bd.bind(null,l,i,n,t),null),He===null)throw Error(o(349));c||(kn&124)!==0||kd(l,t,n)}return n}function kd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=be.updateQueue,t===null?(t=Fu(),be.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bd(e,t,n,l){t.value=n,t.getSnapshot=l,Gd(t)&&Vd(e)}function Hd(e,t,n){return n(function(){Gd(t)&&Vd(e)})}function Gd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Et(e,n)}catch{return!0}}function Vd(e){var t=Ja(e,2);t!==null&&Ot(t,e,2)}function ec(e){var t=yt();if(typeof e=="function"){var n=e;if(e=n(),Aa){Mn(!0);try{n()}finally{Mn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:e},t}function qd(e,t,n,l){return e.baseState=n,Wu(e,Ue,typeof l=="function"?l:pn)}function ub(e,t,n,l,i){if(zi(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){c.listeners.push(g)}};j.T!==null?n(!0):c.isTransition=!1,l(c),n=t.pending,n===null?(c.next=t.pending=c,Yd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Yd(e,t){var n=t.action,l=t.payload,i=e.state;if(t.isTransition){var c=j.T,g={};j.T=g;try{var x=n(i,l),A=j.S;A!==null&&A(g,x),Xd(e,t,x)}catch(L){tc(e,t,L)}finally{j.T=c}}else try{c=n(i,l),Xd(e,t,c)}catch(L){tc(e,t,L)}}function Xd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Qd(e,t,l)},function(l){return tc(e,t,l)}):Qd(e,t,n)}function Qd(e,t,n){t.status="fulfilled",t.value=n,Zd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Yd(e,n)))}function tc(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,Zd(t),t=t.next;while(t!==l)}e.action=null}function Zd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Kd(e,t){return t}function Pd(e,t){if(_e){var n=He.formState;if(n!==null){e:{var l=be;if(_e){if(Qe){t:{for(var i=Qe,c=$t;i.nodeType!==8;){if(!c){i=null;break t}if(i=Yt(i.nextSibling),i===null){i=null;break t}}c=i.data,i=c==="F!"||c==="F"?i:null}if(i){Qe=Yt(i.nextSibling),l=i.data==="F!";break e}}Sa(l)}l=!1}l&&(t=n[0])}}return n=yt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Kd,lastRenderedState:t},n.queue=l,n=mm.bind(null,be,l),l.dispatch=n,l=ec(!1),c=ic.bind(null,be,!1,l.queue),l=yt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,n=ub.bind(null,be,i,c,n),i.dispatch=n,l.memoizedState=e,[t,n,!1]}function $d(e){var t=Fe();return Fd(t,Ue,e)}function Fd(e,t,n){if(t=Wu(e,t,Kd)[0],e=Mi(pn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=fr(t)}catch(g){throw g===rr?Ti:g}else l=t;t=Fe();var i=t.queue,c=i.dispatch;return n!==t.memoizedState&&(be.flags|=2048,il(9,_i(),cb.bind(null,i,n),null)),[l,c,e]}function cb(e,t){e.action=t}function Jd(e){var t=Fe(),n=Ue;if(n!==null)return Fd(t,n,e);Fe(),t=t.memoizedState,n=Fe();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function il(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=be.updateQueue,t===null&&(t=Fu(),be.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function _i(){return{destroy:void 0,resource:void 0}}function Wd(){return Fe().memoizedState}function Di(e,t,n,l){var i=yt();l=l===void 0?null:l,be.flags|=e,i.memoizedState=il(1|t,_i(),n,l)}function dr(e,t,n,l){var i=Fe();l=l===void 0?null:l;var c=i.memoizedState.inst;Ue!==null&&l!==null&&Qu(l,Ue.memoizedState.deps)?i.memoizedState=il(t,c,n,l):(be.flags|=e,i.memoizedState=il(1|t,c,n,l))}function Id(e,t){Di(8390656,8,e,t)}function em(e,t){dr(2048,8,e,t)}function tm(e,t){return dr(4,2,e,t)}function nm(e,t){return dr(4,4,e,t)}function am(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function lm(e,t,n){n=n!=null?n.concat([e]):null,dr(4,4,am.bind(null,t,e),n)}function nc(){}function rm(e,t){var n=Fe();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&Qu(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function im(e,t){var n=Fe();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&Qu(t,l[1]))return l[0];if(l=e(),Aa){Mn(!0);try{e()}finally{Mn(!1)}}return n.memoizedState=[l,t],l}function ac(e,t,n){return n===void 0||(kn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=ch(),be.lanes|=e,Xn|=e,n)}function om(e,t,n,l){return Et(n,t)?n:al.current!==null?(e=ac(e,n,l),Et(e,t)||(tt=!0),e):(kn&42)===0?(tt=!0,e.memoizedState=n):(e=ch(),be.lanes|=e,Xn|=e,t)}function um(e,t,n,l,i){var c=K.p;K.p=c!==0&&8>c?c:8;var g=j.T,x={};j.T=x,ic(e,!1,t,n);try{var A=i(),L=j.S;if(L!==null&&L(x,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var q=rb(A,l);mr(e,t,q,Nt(e))}else mr(e,t,l,Nt(e))}catch(Z){mr(e,t,{then:function(){},status:"rejected",reason:Z},Nt())}finally{K.p=c,j.T=g}}function sb(){}function lc(e,t,n,l){if(e.tag!==5)throw Error(o(476));var i=cm(e).queue;um(e,i,t,H,n===null?sb:function(){return sm(e),n(l)})}function cm(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:H,baseState:H,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:H},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function sm(e){var t=cm(e).next.queue;mr(e,t,{},Nt())}function rc(){return st(_r)}function fm(){return Fe().memoizedState}function dm(){return Fe().memoizedState}function fb(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Nt();e=Un(n);var l=Ln(t,e,n);l!==null&&(Ot(l,t,n),or(l,t,n)),t={cache:Uu()},e.payload=t;return}t=t.return}}function db(e,t,n){var l=Nt();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},zi(e)?hm(t,n):(n=Au(e,t,n,l),n!==null&&(Ot(n,e,l),pm(n,t,l)))}function mm(e,t,n){var l=Nt();mr(e,t,n,l)}function mr(e,t,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(zi(e))hm(t,i);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var g=t.lastRenderedState,x=c(g,n);if(i.hasEagerState=!0,i.eagerState=x,Et(x,g))return gi(e,t,i,0),He===null&&pi(),!1}catch{}finally{}if(n=Au(e,t,i,l),n!==null)return Ot(n,e,l),pm(n,t,l),!0}return!1}function ic(e,t,n,l){if(l={lane:2,revertLane:Bc(),action:l,hasEagerState:!1,eagerState:null,next:null},zi(e)){if(t)throw Error(o(479))}else t=Au(e,n,l,2),t!==null&&Ot(t,e,2)}function zi(e){var t=e.alternate;return e===be||t!==null&&t===be}function hm(e,t){ll=Ci=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function pm(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Ef(e,n)}}var ji={readContext:st,use:Oi,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useLayoutEffect:Ke,useInsertionEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useSyncExternalStore:Ke,useId:Ke,useHostTransitionStatus:Ke,useFormState:Ke,useActionState:Ke,useOptimistic:Ke,useMemoCache:Ke,useCacheRefresh:Ke},gm={readContext:st,use:Oi,useCallback:function(e,t){return yt().memoizedState=[e,t===void 0?null:t],e},useContext:st,useEffect:Id,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Di(4194308,4,am.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Di(4194308,4,e,t)},useInsertionEffect:function(e,t){Di(4,2,e,t)},useMemo:function(e,t){var n=yt();t=t===void 0?null:t;var l=e();if(Aa){Mn(!0);try{e()}finally{Mn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=yt();if(n!==void 0){var i=n(t);if(Aa){Mn(!0);try{n(t)}finally{Mn(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=db.bind(null,be,e),[l.memoizedState,e]},useRef:function(e){var t=yt();return e={current:e},t.memoizedState=e},useState:function(e){e=ec(e);var t=e.queue,n=mm.bind(null,be,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:nc,useDeferredValue:function(e,t){var n=yt();return ac(n,e,t)},useTransition:function(){var e=ec(!1);return e=um.bind(null,be,e.queue,!0,!1),yt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=be,i=yt();if(_e){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),He===null)throw Error(o(349));(Oe&124)!==0||kd(l,t,n)}i.memoizedState=n;var c={value:n,getSnapshot:t};return i.queue=c,Id(Hd.bind(null,l,c,e),[e]),l.flags|=2048,il(9,_i(),Bd.bind(null,l,c,n,t),null),n},useId:function(){var e=yt(),t=He.identifierPrefix;if(_e){var n=dn,l=fn;n=(l&~(1<<32-wt(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=Ni++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=ib++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:rc,useFormState:Pd,useActionState:Pd,useOptimistic:function(e){var t=yt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=ic.bind(null,be,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ju,useCacheRefresh:function(){return yt().memoizedState=fb.bind(null,be)}},vm={readContext:st,use:Oi,useCallback:rm,useContext:st,useEffect:em,useImperativeHandle:lm,useInsertionEffect:tm,useLayoutEffect:nm,useMemo:im,useReducer:Mi,useRef:Wd,useState:function(){return Mi(pn)},useDebugValue:nc,useDeferredValue:function(e,t){var n=Fe();return om(n,Ue.memoizedState,e,t)},useTransition:function(){var e=Mi(pn)[0],t=Fe().memoizedState;return[typeof e=="boolean"?e:fr(e),t]},useSyncExternalStore:Ld,useId:fm,useHostTransitionStatus:rc,useFormState:$d,useActionState:$d,useOptimistic:function(e,t){var n=Fe();return qd(n,Ue,e,t)},useMemoCache:Ju,useCacheRefresh:dm},mb={readContext:st,use:Oi,useCallback:rm,useContext:st,useEffect:em,useImperativeHandle:lm,useInsertionEffect:tm,useLayoutEffect:nm,useMemo:im,useReducer:Iu,useRef:Wd,useState:function(){return Iu(pn)},useDebugValue:nc,useDeferredValue:function(e,t){var n=Fe();return Ue===null?ac(n,e,t):om(n,Ue.memoizedState,e,t)},useTransition:function(){var e=Iu(pn)[0],t=Fe().memoizedState;return[typeof e=="boolean"?e:fr(e),t]},useSyncExternalStore:Ld,useId:fm,useHostTransitionStatus:rc,useFormState:Jd,useActionState:Jd,useOptimistic:function(e,t){var n=Fe();return Ue!==null?qd(n,Ue,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ju,useCacheRefresh:dm},ol=null,hr=0;function Ui(e){var t=hr;return hr+=1,ol===null&&(ol=[]),Nd(ol,e,t)}function pr(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Li(e,t){throw t.$$typeof===w?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function ym(e){var t=e._init;return t(e._payload)}function bm(e){function t(z,M){if(e){var U=z.deletions;U===null?(z.deletions=[M],z.flags|=16):U.push(M)}}function n(z,M){if(!e)return null;for(;M!==null;)t(z,M),M=M.sibling;return null}function l(z){for(var M=new Map;z!==null;)z.key!==null?M.set(z.key,z):M.set(z.index,z),z=z.sibling;return M}function i(z,M){return z=sn(z,M),z.index=0,z.sibling=null,z}function c(z,M,U){return z.index=U,e?(U=z.alternate,U!==null?(U=U.index,U<M?(z.flags|=67108866,M):U):(z.flags|=67108866,M)):(z.flags|=1048576,M)}function g(z){return e&&z.alternate===null&&(z.flags|=67108866),z}function x(z,M,U,Q){return M===null||M.tag!==6?(M=Cu(U,z.mode,Q),M.return=z,M):(M=i(M,U),M.return=z,M)}function A(z,M,U,Q){var te=U.type;return te===T?q(z,M,U.props.children,Q,U.key):M!==null&&(M.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===Y&&ym(te)===M.type)?(M=i(M,U.props),pr(M,U),M.return=z,M):(M=yi(U.type,U.key,U.props,null,z.mode,Q),pr(M,U),M.return=z,M)}function L(z,M,U,Q){return M===null||M.tag!==4||M.stateNode.containerInfo!==U.containerInfo||M.stateNode.implementation!==U.implementation?(M=Nu(U,z.mode,Q),M.return=z,M):(M=i(M,U.children||[]),M.return=z,M)}function q(z,M,U,Q,te){return M===null||M.tag!==7?(M=va(U,z.mode,Q,te),M.return=z,M):(M=i(M,U),M.return=z,M)}function Z(z,M,U){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return M=Cu(""+M,z.mode,U),M.return=z,M;if(typeof M=="object"&&M!==null){switch(M.$$typeof){case S:return U=yi(M.type,M.key,M.props,null,z.mode,U),pr(U,M),U.return=z,U;case R:return M=Nu(M,z.mode,U),M.return=z,M;case Y:var Q=M._init;return M=Q(M._payload),Z(z,M,U)}if(oe(M)||de(M))return M=va(M,z.mode,U,null),M.return=z,M;if(typeof M.then=="function")return Z(z,Ui(M),U);if(M.$$typeof===D)return Z(z,wi(z,M),U);Li(z,M)}return null}function k(z,M,U,Q){var te=M!==null?M.key:null;if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return te!==null?null:x(z,M,""+U,Q);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case S:return U.key===te?A(z,M,U,Q):null;case R:return U.key===te?L(z,M,U,Q):null;case Y:return te=U._init,U=te(U._payload),k(z,M,U,Q)}if(oe(U)||de(U))return te!==null?null:q(z,M,U,Q,null);if(typeof U.then=="function")return k(z,M,Ui(U),Q);if(U.$$typeof===D)return k(z,M,wi(z,U),Q);Li(z,U)}return null}function B(z,M,U,Q,te){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return z=z.get(U)||null,x(M,z,""+Q,te);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case S:return z=z.get(Q.key===null?U:Q.key)||null,A(M,z,Q,te);case R:return z=z.get(Q.key===null?U:Q.key)||null,L(M,z,Q,te);case Y:var Se=Q._init;return Q=Se(Q._payload),B(z,M,U,Q,te)}if(oe(Q)||de(Q))return z=z.get(U)||null,q(M,z,Q,te,null);if(typeof Q.then=="function")return B(z,M,U,Ui(Q),te);if(Q.$$typeof===D)return B(z,M,U,wi(M,Q),te);Li(M,Q)}return null}function me(z,M,U,Q){for(var te=null,Se=null,re=M,fe=M=0,at=null;re!==null&&fe<U.length;fe++){re.index>fe?(at=re,re=null):at=re.sibling;var Me=k(z,re,U[fe],Q);if(Me===null){re===null&&(re=at);break}e&&re&&Me.alternate===null&&t(z,re),M=c(Me,M,fe),Se===null?te=Me:Se.sibling=Me,Se=Me,re=at}if(fe===U.length)return n(z,re),_e&&ba(z,fe),te;if(re===null){for(;fe<U.length;fe++)re=Z(z,U[fe],Q),re!==null&&(M=c(re,M,fe),Se===null?te=re:Se.sibling=re,Se=re);return _e&&ba(z,fe),te}for(re=l(re);fe<U.length;fe++)at=B(re,z,fe,U[fe],Q),at!==null&&(e&&at.alternate!==null&&re.delete(at.key===null?fe:at.key),M=c(at,M,fe),Se===null?te=at:Se.sibling=at,Se=at);return e&&re.forEach(function(In){return t(z,In)}),_e&&ba(z,fe),te}function se(z,M,U,Q){if(U==null)throw Error(o(151));for(var te=null,Se=null,re=M,fe=M=0,at=null,Me=U.next();re!==null&&!Me.done;fe++,Me=U.next()){re.index>fe?(at=re,re=null):at=re.sibling;var In=k(z,re,Me.value,Q);if(In===null){re===null&&(re=at);break}e&&re&&In.alternate===null&&t(z,re),M=c(In,M,fe),Se===null?te=In:Se.sibling=In,Se=In,re=at}if(Me.done)return n(z,re),_e&&ba(z,fe),te;if(re===null){for(;!Me.done;fe++,Me=U.next())Me=Z(z,Me.value,Q),Me!==null&&(M=c(Me,M,fe),Se===null?te=Me:Se.sibling=Me,Se=Me);return _e&&ba(z,fe),te}for(re=l(re);!Me.done;fe++,Me=U.next())Me=B(re,z,fe,Me.value,Q),Me!==null&&(e&&Me.alternate!==null&&re.delete(Me.key===null?fe:Me.key),M=c(Me,M,fe),Se===null?te=Me:Se.sibling=Me,Se=Me);return e&&re.forEach(function(hx){return t(z,hx)}),_e&&ba(z,fe),te}function ke(z,M,U,Q){if(typeof U=="object"&&U!==null&&U.type===T&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case S:e:{for(var te=U.key;M!==null;){if(M.key===te){if(te=U.type,te===T){if(M.tag===7){n(z,M.sibling),Q=i(M,U.props.children),Q.return=z,z=Q;break e}}else if(M.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===Y&&ym(te)===M.type){n(z,M.sibling),Q=i(M,U.props),pr(Q,U),Q.return=z,z=Q;break e}n(z,M);break}else t(z,M);M=M.sibling}U.type===T?(Q=va(U.props.children,z.mode,Q,U.key),Q.return=z,z=Q):(Q=yi(U.type,U.key,U.props,null,z.mode,Q),pr(Q,U),Q.return=z,z=Q)}return g(z);case R:e:{for(te=U.key;M!==null;){if(M.key===te)if(M.tag===4&&M.stateNode.containerInfo===U.containerInfo&&M.stateNode.implementation===U.implementation){n(z,M.sibling),Q=i(M,U.children||[]),Q.return=z,z=Q;break e}else{n(z,M);break}else t(z,M);M=M.sibling}Q=Nu(U,z.mode,Q),Q.return=z,z=Q}return g(z);case Y:return te=U._init,U=te(U._payload),ke(z,M,U,Q)}if(oe(U))return me(z,M,U,Q);if(de(U)){if(te=de(U),typeof te!="function")throw Error(o(150));return U=te.call(U),se(z,M,U,Q)}if(typeof U.then=="function")return ke(z,M,Ui(U),Q);if(U.$$typeof===D)return ke(z,M,wi(z,U),Q);Li(z,U)}return typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint"?(U=""+U,M!==null&&M.tag===6?(n(z,M.sibling),Q=i(M,U),Q.return=z,z=Q):(n(z,M),Q=Cu(U,z.mode,Q),Q.return=z,z=Q),g(z)):n(z,M)}return function(z,M,U,Q){try{hr=0;var te=ke(z,M,U,Q);return ol=null,te}catch(re){if(re===rr||re===Ti)throw re;var Se=Tt(29,re,null,z.mode);return Se.lanes=Q,Se.return=z,Se}finally{}}}var ul=bm(!0),xm=bm(!1),Bt=X(null),Ft=null;function Bn(e){var t=e.alternate;$(We,We.current&1),$(Bt,e),Ft===null&&(t===null||al.current!==null||t.memoizedState!==null)&&(Ft=e)}function Sm(e){if(e.tag===22){if($(We,We.current),$(Bt,e),Ft===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ft=e)}}else Hn()}function Hn(){$(We,We.current),$(Bt,Bt.current)}function gn(e){J(Bt),Ft===e&&(Ft=null),J(We)}var We=X(0);function ki(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Fc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function oc(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var uc={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=Nt(),i=Un(l);i.payload=t,n!=null&&(i.callback=n),t=Ln(e,i,l),t!==null&&(Ot(t,e,l),or(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=Nt(),i=Un(l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Ln(e,i,l),t!==null&&(Ot(t,e,l),or(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Nt(),l=Un(n);l.tag=2,t!=null&&(l.callback=t),t=Ln(e,l,n),t!==null&&(Ot(t,e,n),or(t,e,n))}};function wm(e,t,n,l,i,c,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,g):t.prototype&&t.prototype.isPureReactComponent?!Jl(n,l)||!Jl(i,c):!0}function Em(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&uc.enqueueReplaceState(t,t.state,null)}function Ra(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Bi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Tm(e){Bi(e)}function Am(e){console.error(e)}function Rm(e){Bi(e)}function Hi(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Cm(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function cc(e,t,n){return n=Un(n),n.tag=3,n.payload={element:null},n.callback=function(){Hi(e,t)},n}function Nm(e){return e=Un(e),e.tag=3,e}function Om(e,t,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var c=l.value;e.payload=function(){return i(c)},e.callback=function(){Cm(t,n,l)}}var g=n.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){Cm(t,n,l),typeof i!="function"&&(Qn===null?Qn=new Set([this]):Qn.add(this));var x=l.stack;this.componentDidCatch(l.value,{componentStack:x!==null?x:""})})}function hb(e,t,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&nr(t,n,i,!0),n=Bt.current,n!==null){switch(n.tag){case 13:return Ft===null?zc():n.alternate===null&&Ze===0&&(Ze=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Bu?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),Uc(e,l,i)),!1;case 22:return n.flags|=65536,l===Bu?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),Uc(e,l,i)),!1}throw Error(o(435,n.tag))}return Uc(e,l,i),zc(),!1}if(_e)return t=Bt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==_u&&(e=Error(o(422),{cause:l}),tr(jt(e,n)))):(l!==_u&&(t=Error(o(423),{cause:l}),tr(jt(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=jt(l,n),i=cc(e.stateNode,l,i),Vu(e,i),Ze!==4&&(Ze=2)),!1;var c=Error(o(520),{cause:l});if(c=jt(c,n),wr===null?wr=[c]:wr.push(c),Ze!==4&&(Ze=2),t===null)return!0;l=jt(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=cc(n.stateNode,l,e),Vu(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Qn===null||!Qn.has(c))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Nm(i),Om(i,e,n,l),Vu(n,i),!1}n=n.return}while(n!==null);return!1}var Mm=Error(o(461)),tt=!1;function rt(e,t,n,l){t.child=e===null?xm(t,null,n,l):ul(t,e.child,n,l)}function _m(e,t,n,l,i){n=n.render;var c=t.ref;if("ref"in l){var g={};for(var x in l)x!=="ref"&&(g[x]=l[x])}else g=l;return Ea(t),l=Zu(e,t,n,g,c,i),x=Ku(),e!==null&&!tt?(Pu(e,t,i),vn(e,t,i)):(_e&&x&&Ou(t),t.flags|=1,rt(e,t,l,i),t.child)}function Dm(e,t,n,l,i){if(e===null){var c=n.type;return typeof c=="function"&&!Ru(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,zm(e,t,c,l,i)):(e=yi(n.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!vc(e,i)){var g=c.memoizedProps;if(n=n.compare,n=n!==null?n:Jl,n(g,l)&&e.ref===t.ref)return vn(e,t,i)}return t.flags|=1,e=sn(c,l),e.ref=t.ref,e.return=t,t.child=e}function zm(e,t,n,l,i){if(e!==null){var c=e.memoizedProps;if(Jl(c,l)&&e.ref===t.ref)if(tt=!1,t.pendingProps=l=c,vc(e,i))(e.flags&131072)!==0&&(tt=!0);else return t.lanes=e.lanes,vn(e,t,i)}return sc(e,t,n,l,i)}function jm(e,t,n){var l=t.pendingProps,i=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,e!==null){for(i=t.child=e.child,c=0;i!==null;)c=c|i.lanes|i.childLanes,i=i.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return Um(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ei(t,c!==null?c.cachePool:null),c!==null?zd(t,c):Yu(),Sm(t);else return t.lanes=t.childLanes=536870912,Um(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Ei(t,c.cachePool),zd(t,c),Hn(),t.memoizedState=null):(e!==null&&Ei(t,null),Yu(),Hn());return rt(e,t,i,n),t.child}function Um(e,t,n,l){var i=ku();return i=i===null?null:{parent:Je._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&Ei(t,null),Yu(),Sm(t),e!==null&&nr(e,t,l,!0),null}function Gi(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function sc(e,t,n,l,i){return Ea(t),n=Zu(e,t,n,l,void 0,i),l=Ku(),e!==null&&!tt?(Pu(e,t,i),vn(e,t,i)):(_e&&l&&Ou(t),t.flags|=1,rt(e,t,n,i),t.child)}function Lm(e,t,n,l,i,c){return Ea(t),t.updateQueue=null,n=Ud(t,l,n,i),jd(e),l=Ku(),e!==null&&!tt?(Pu(e,t,c),vn(e,t,c)):(_e&&l&&Ou(t),t.flags|=1,rt(e,t,n,c),t.child)}function km(e,t,n,l,i){if(Ea(t),t.stateNode===null){var c=Wa,g=n.contextType;typeof g=="object"&&g!==null&&(c=st(g)),c=new n(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=uc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},Hu(t),g=n.contextType,c.context=typeof g=="object"&&g!==null?st(g):Wa,c.state=t.memoizedState,g=n.getDerivedStateFromProps,typeof g=="function"&&(oc(t,n,g,l),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(g=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),g!==c.state&&uc.enqueueReplaceState(c,c.state,null),cr(t,l,c,i),ur(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var x=t.memoizedProps,A=Ra(n,x);c.props=A;var L=c.context,q=n.contextType;g=Wa,typeof q=="object"&&q!==null&&(g=st(q));var Z=n.getDerivedStateFromProps;q=typeof Z=="function"||typeof c.getSnapshotBeforeUpdate=="function",x=t.pendingProps!==x,q||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(x||L!==g)&&Em(t,c,l,g),jn=!1;var k=t.memoizedState;c.state=k,cr(t,l,c,i),ur(),L=t.memoizedState,x||k!==L||jn?(typeof Z=="function"&&(oc(t,n,Z,l),L=t.memoizedState),(A=jn||wm(t,n,A,l,k,L,g))?(q||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=L),c.props=l,c.state=L,c.context=g,l=A):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,Gu(e,t),g=t.memoizedProps,q=Ra(n,g),c.props=q,Z=t.pendingProps,k=c.context,L=n.contextType,A=Wa,typeof L=="object"&&L!==null&&(A=st(L)),x=n.getDerivedStateFromProps,(L=typeof x=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(g!==Z||k!==A)&&Em(t,c,l,A),jn=!1,k=t.memoizedState,c.state=k,cr(t,l,c,i),ur();var B=t.memoizedState;g!==Z||k!==B||jn||e!==null&&e.dependencies!==null&&Si(e.dependencies)?(typeof x=="function"&&(oc(t,n,x,l),B=t.memoizedState),(q=jn||wm(t,n,q,l,k,B,A)||e!==null&&e.dependencies!==null&&Si(e.dependencies))?(L||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,B,A),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,B,A)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||g===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=B),c.props=l,c.state=B,c.context=A,l=q):(typeof c.componentDidUpdate!="function"||g===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,Gi(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=ul(t,e.child,null,i),t.child=ul(t,null,n,i)):rt(e,t,n,i),t.memoizedState=c.state,e=t.child):e=vn(e,t,i),e}function Bm(e,t,n,l){return er(),t.flags|=256,rt(e,t,n,l),t.child}var fc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function dc(e){return{baseLanes:e,cachePool:Ad()}}function mc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Ht),e}function Hm(e,t,n){var l=t.pendingProps,i=!1,c=(t.flags&128)!==0,g;if((g=c)||(g=e!==null&&e.memoizedState===null?!1:(We.current&2)!==0),g&&(i=!0,t.flags&=-129),g=(t.flags&32)!==0,t.flags&=-33,e===null){if(_e){if(i?Bn(t):Hn(),_e){var x=Qe,A;if(A=x){e:{for(A=x,x=$t;A.nodeType!==8;){if(!x){x=null;break e}if(A=Yt(A.nextSibling),A===null){x=null;break e}}x=A}x!==null?(t.memoizedState={dehydrated:x,treeContext:ya!==null?{id:fn,overflow:dn}:null,retryLane:536870912,hydrationErrors:null},A=Tt(18,null,null,0),A.stateNode=x,A.return=t,t.child=A,mt=t,Qe=null,A=!0):A=!1}A||Sa(t)}if(x=t.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return Fc(x)?t.lanes=32:t.lanes=536870912,null;gn(t)}return x=l.children,l=l.fallback,i?(Hn(),i=t.mode,x=Vi({mode:"hidden",children:x},i),l=va(l,i,n,null),x.return=t,l.return=t,x.sibling=l,t.child=x,i=t.child,i.memoizedState=dc(n),i.childLanes=mc(e,g,n),t.memoizedState=fc,l):(Bn(t),hc(t,x))}if(A=e.memoizedState,A!==null&&(x=A.dehydrated,x!==null)){if(c)t.flags&256?(Bn(t),t.flags&=-257,t=pc(e,t,n)):t.memoizedState!==null?(Hn(),t.child=e.child,t.flags|=128,t=null):(Hn(),i=l.fallback,x=t.mode,l=Vi({mode:"visible",children:l.children},x),i=va(i,x,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,ul(t,e.child,null,n),l=t.child,l.memoizedState=dc(n),l.childLanes=mc(e,g,n),t.memoizedState=fc,t=i);else if(Bn(t),Fc(x)){if(g=x.nextSibling&&x.nextSibling.dataset,g)var L=g.dgst;g=L,l=Error(o(419)),l.stack="",l.digest=g,tr({value:l,source:null,stack:null}),t=pc(e,t,n)}else if(tt||nr(e,t,n,!1),g=(n&e.childLanes)!==0,tt||g){if(g=He,g!==null&&(l=n&-n,l=(l&42)!==0?1:Jo(l),l=(l&(g.suspendedLanes|n))!==0?0:l,l!==0&&l!==A.retryLane))throw A.retryLane=l,Ja(e,l),Ot(g,e,l),Mm;x.data==="$?"||zc(),t=pc(e,t,n)}else x.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=A.treeContext,Qe=Yt(x.nextSibling),mt=t,_e=!0,xa=null,$t=!1,e!==null&&(Lt[kt++]=fn,Lt[kt++]=dn,Lt[kt++]=ya,fn=e.id,dn=e.overflow,ya=t),t=hc(t,l.children),t.flags|=4096);return t}return i?(Hn(),i=l.fallback,x=t.mode,A=e.child,L=A.sibling,l=sn(A,{mode:"hidden",children:l.children}),l.subtreeFlags=A.subtreeFlags&65011712,L!==null?i=sn(L,i):(i=va(i,x,n,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,x=e.child.memoizedState,x===null?x=dc(n):(A=x.cachePool,A!==null?(L=Je._currentValue,A=A.parent!==L?{parent:L,pool:L}:A):A=Ad(),x={baseLanes:x.baseLanes|n,cachePool:A}),i.memoizedState=x,i.childLanes=mc(e,g,n),t.memoizedState=fc,l):(Bn(t),n=e.child,e=n.sibling,n=sn(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(g=t.deletions,g===null?(t.deletions=[e],t.flags|=16):g.push(e)),t.child=n,t.memoizedState=null,n)}function hc(e,t){return t=Vi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Vi(e,t){return e=Tt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function pc(e,t,n){return ul(t,e.child,null,n),e=hc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Gm(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),zu(e.return,t,n)}function gc(e,t,n,l,i){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=i)}function Vm(e,t,n){var l=t.pendingProps,i=l.revealOrder,c=l.tail;if(rt(e,t,l.children,n),l=We.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gm(e,n,t);else if(e.tag===19)Gm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch($(We,l),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&ki(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),gc(t,!1,i,n,c);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ki(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}gc(t,!0,n,null,c);break;case"together":gc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Xn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(nr(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=sn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=sn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function vc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Si(e)))}function pb(e,t,n){switch(t.tag){case 3:ue(t,t.stateNode.containerInfo),zn(t,Je,e.memoizedState.cache),er();break;case 27:case 5:Re(t);break;case 4:ue(t,t.stateNode.containerInfo);break;case 10:zn(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Bn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Hm(e,t,n):(Bn(t),e=vn(e,t,n),e!==null?e.sibling:null);Bn(t);break;case 19:var i=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(nr(e,t,n,!1),l=(n&t.childLanes)!==0),i){if(l)return Vm(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(We,We.current),l)break;return null;case 22:case 23:return t.lanes=0,jm(e,t,n);case 24:zn(t,Je,e.memoizedState.cache)}return vn(e,t,n)}function qm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)tt=!0;else{if(!vc(e,n)&&(t.flags&128)===0)return tt=!1,pb(e,t,n);tt=(e.flags&131072)!==0}else tt=!1,_e&&(t.flags&1048576)!==0&&yd(t,xi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")Ru(l)?(e=Ra(l,e),t.tag=1,t=km(null,t,l,e,n)):(t.tag=0,t=sc(null,t,l,e,n));else{if(l!=null){if(i=l.$$typeof,i===G){t.tag=11,t=_m(null,t,l,e,n);break e}else if(i===P){t.tag=14,t=Dm(null,t,l,e,n);break e}}throw t=ye(l)||l,Error(o(306,t,""))}}return t;case 0:return sc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,i=Ra(l,t.pendingProps),km(e,t,l,i,n);case 3:e:{if(ue(t,t.stateNode.containerInfo),e===null)throw Error(o(387));l=t.pendingProps;var c=t.memoizedState;i=c.element,Gu(e,t),cr(t,l,null,n);var g=t.memoizedState;if(l=g.cache,zn(t,Je,l),l!==c.cache&&ju(t,[Je],n,!0),ur(),l=g.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:g.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Bm(e,t,l,n);break e}else if(l!==i){i=jt(Error(o(424)),t),tr(i),t=Bm(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Qe=Yt(e.firstChild),mt=t,_e=!0,xa=null,$t=!0,n=xm(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(er(),l===i){t=vn(e,t,n);break e}rt(e,t,l,n)}t=t.child}return t;case 26:return Gi(e,t),e===null?(n=Zh(t.type,null,t.pendingProps,null))?t.memoizedState=n:_e||(n=t.type,e=t.pendingProps,l=to(ie.current).createElement(n),l[ct]=t,l[gt]=e,ot(l,n,e),et(l),t.stateNode=l):t.memoizedState=Zh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Re(t),e===null&&_e&&(l=t.stateNode=Yh(t.type,t.pendingProps,ie.current),mt=t,$t=!0,i=Qe,Pn(t.type)?(Jc=i,Qe=Yt(l.firstChild)):Qe=i),rt(e,t,t.pendingProps.children,n),Gi(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&_e&&((i=l=Qe)&&(l=Yb(l,t.type,t.pendingProps,$t),l!==null?(t.stateNode=l,mt=t,Qe=Yt(l.firstChild),$t=!1,i=!0):i=!1),i||Sa(t)),Re(t),i=t.type,c=t.pendingProps,g=e!==null?e.memoizedProps:null,l=c.children,Kc(i,c)?l=null:g!==null&&Kc(i,g)&&(t.flags|=32),t.memoizedState!==null&&(i=Zu(e,t,ob,null,null,n),_r._currentValue=i),Gi(e,t),rt(e,t,l,n),t.child;case 6:return e===null&&_e&&((e=n=Qe)&&(n=Xb(n,t.pendingProps,$t),n!==null?(t.stateNode=n,mt=t,Qe=null,e=!0):e=!1),e||Sa(t)),null;case 13:return Hm(e,t,n);case 4:return ue(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=ul(t,null,l,n):rt(e,t,l,n),t.child;case 11:return _m(e,t,t.type,t.pendingProps,n);case 7:return rt(e,t,t.pendingProps,n),t.child;case 8:return rt(e,t,t.pendingProps.children,n),t.child;case 12:return rt(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,zn(t,t.type,l.value),rt(e,t,l.children,n),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,Ea(t),i=st(i),l=l(i),t.flags|=1,rt(e,t,l,n),t.child;case 14:return Dm(e,t,t.type,t.pendingProps,n);case 15:return zm(e,t,t.type,t.pendingProps,n);case 19:return Vm(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=Vi(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=sn(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return jm(e,t,n);case 24:return Ea(t),l=st(Je),e===null?(i=ku(),i===null&&(i=He,c=Uu(),i.pooledCache=c,c.refCount++,c!==null&&(i.pooledCacheLanes|=n),i=c),t.memoizedState={parent:l,cache:i},Hu(t),zn(t,Je,i)):((e.lanes&n)!==0&&(Gu(e,t),cr(t,null,null,n),ur()),i=e.memoizedState,c=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),zn(t,Je,l)):(l=c.cache,zn(t,Je,l),l!==i.cache&&ju(t,[Je],n,!0))),rt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function yn(e){e.flags|=4}function Ym(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Jh(t)){if(t=Bt.current,t!==null&&((Oe&4194048)===Oe?Ft!==null:(Oe&62914560)!==Oe&&(Oe&536870912)===0||t!==Ft))throw ir=Bu,Rd;e.flags|=8192}}function qi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Sf():536870912,e.lanes|=t,dl|=t)}function gr(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function gb(e,t,n){var l=t.pendingProps;switch(Mu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return Ye(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),hn(Je),De(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Il(t)?yn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Sd())),Ye(t),null;case 26:return n=t.memoizedState,e===null?(yn(t),n!==null?(Ye(t),Ym(t,n)):(Ye(t),t.flags&=-16777217)):n?n!==e.memoizedState?(yn(t),Ye(t),Ym(t,n)):(Ye(t),t.flags&=-16777217):(e.memoizedProps!==l&&yn(t),Ye(t),t.flags&=-16777217),null;case 27:we(t),n=ie.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&yn(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return Ye(t),null}e=I.current,Il(t)?bd(t):(e=Yh(i,l,n),t.stateNode=e,yn(t))}return Ye(t),null;case 5:if(we(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&yn(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return Ye(t),null}if(e=I.current,Il(t))bd(t);else{switch(i=to(ie.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}e[ct]=t,e[gt]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ot(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&yn(t)}}return Ye(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&yn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(o(166));if(e=ie.current,Il(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,i=mt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[ct]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Lh(e.nodeValue,n)),e||Sa(t)}else e=to(e).createTextNode(l),e[ct]=t,t.stateNode=e}return Ye(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=Il(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(o(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(o(317));i[ct]=t}else er(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),i=!1}else i=Sd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(gn(t),t):(gn(t),null)}if(gn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==i&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),qi(t,t.updateQueue),Ye(t),null;case 4:return De(),e===null&&qc(t.stateNode.containerInfo),Ye(t),null;case 10:return hn(t.type),Ye(t),null;case 19:if(J(We),i=t.memoizedState,i===null)return Ye(t),null;if(l=(t.flags&128)!==0,c=i.rendering,c===null)if(l)gr(i,!1);else{if(Ze!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=ki(e),c!==null){for(t.flags|=128,gr(i,!1),e=c.updateQueue,t.updateQueue=e,qi(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)vd(n,e),n=n.sibling;return $(We,We.current&1|2),t.child}e=e.sibling}i.tail!==null&&ut()>Qi&&(t.flags|=128,l=!0,gr(i,!1),t.lanes=4194304)}else{if(!l)if(e=ki(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,qi(t,e),gr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!c.alternate&&!_e)return Ye(t),null}else 2*ut()-i.renderingStartTime>Qi&&n!==536870912&&(t.flags|=128,l=!0,gr(i,!1),t.lanes=4194304);i.isBackwards?(c.sibling=t.child,t.child=c):(e=i.last,e!==null?e.sibling=c:t.child=c,i.last=c)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ut(),t.sibling=null,e=We.current,$(We,l?e&1|2:e&1),t):(Ye(t),null);case 22:case 23:return gn(t),Xu(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),n=t.updateQueue,n!==null&&qi(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&J(Ta),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),hn(Je),Ye(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function vb(e,t){switch(Mu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return hn(Je),De(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return we(t),null;case 13:if(gn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(We),null;case 4:return De(),null;case 10:return hn(t.type),null;case 22:case 23:return gn(t),Xu(),e!==null&&J(Ta),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return hn(Je),null;case 25:return null;default:return null}}function Xm(e,t){switch(Mu(t),t.tag){case 3:hn(Je),De();break;case 26:case 27:case 5:we(t);break;case 4:De();break;case 13:gn(t);break;case 19:J(We);break;case 10:hn(t.type);break;case 22:case 23:gn(t),Xu(),e!==null&&J(Ta);break;case 24:hn(Je)}}function vr(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){l=void 0;var c=n.create,g=n.inst;l=c(),g.destroy=l}n=n.next}while(n!==i)}}catch(x){Be(t,t.return,x)}}function Gn(e,t,n){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var c=i.next;l=c;do{if((l.tag&e)===e){var g=l.inst,x=g.destroy;if(x!==void 0){g.destroy=void 0,i=t;var A=n,L=x;try{L()}catch(q){Be(i,A,q)}}}l=l.next}while(l!==c)}}catch(q){Be(t,t.return,q)}}function Qm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Dd(t,n)}catch(l){Be(e,e.return,l)}}}function Zm(e,t,n){n.props=Ra(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){Be(e,t,l)}}function yr(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(i){Be(e,t,i)}}function Jt(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){Be(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){Be(e,t,i)}else n.current=null}function Km(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){Be(e,e.return,i)}}function yc(e,t,n){try{var l=e.stateNode;Bb(l,e.type,n,t),l[gt]=t}catch(i){Be(e,e.return,i)}}function Pm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Pn(e.type)||e.tag===4}function bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Pm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Pn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function xc(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=eo));else if(l!==4&&(l===27&&Pn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(xc(e,t,n),e=e.sibling;e!==null;)xc(e,t,n),e=e.sibling}function Yi(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Pn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Yi(e,t,n),e=e.sibling;e!==null;)Yi(e,t,n),e=e.sibling}function $m(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ot(t,l,n),t[ct]=e,t[gt]=n}catch(c){Be(e,e.return,c)}}var bn=!1,Pe=!1,Sc=!1,Fm=typeof WeakSet=="function"?WeakSet:Set,nt=null;function yb(e,t){if(e=e.containerInfo,Qc=oo,e=od(e),bu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var g=0,x=-1,A=-1,L=0,q=0,Z=e,k=null;t:for(;;){for(var B;Z!==n||i!==0&&Z.nodeType!==3||(x=g+i),Z!==c||l!==0&&Z.nodeType!==3||(A=g+l),Z.nodeType===3&&(g+=Z.nodeValue.length),(B=Z.firstChild)!==null;)k=Z,Z=B;for(;;){if(Z===e)break t;if(k===n&&++L===i&&(x=g),k===c&&++q===l&&(A=g),(B=Z.nextSibling)!==null)break;Z=k,k=Z.parentNode}Z=B}n=x===-1||A===-1?null:{start:x,end:A}}else n=null}n=n||{start:0,end:0}}else n=null;for(Zc={focusedElem:e,selectionRange:n},oo=!1,nt=t;nt!==null;)if(t=nt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,nt=e;else for(;nt!==null;){switch(t=nt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,i=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var me=Ra(n.type,i,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(me,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(se){Be(n,n.return,se)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)$c(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":$c(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,nt=e;break}nt=t.return}}function Jm(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Vn(e,n),l&4&&vr(5,n);break;case 1:if(Vn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(g){Be(n,n.return,g)}else{var i=Ra(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(g){Be(n,n.return,g)}}l&64&&Qm(n),l&512&&yr(n,n.return);break;case 3:if(Vn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Dd(e,t)}catch(g){Be(n,n.return,g)}}break;case 27:t===null&&l&4&&$m(n);case 26:case 5:Vn(e,n),t===null&&l&4&&Km(n),l&512&&yr(n,n.return);break;case 12:Vn(e,n);break;case 13:Vn(e,n),l&4&&eh(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Cb.bind(null,n),Qb(e,n))));break;case 22:if(l=n.memoizedState!==null||bn,!l){t=t!==null&&t.memoizedState!==null||Pe,i=bn;var c=Pe;bn=l,(Pe=t)&&!c?qn(e,n,(n.subtreeFlags&8772)!==0):Vn(e,n),bn=i,Pe=c}break;case 30:break;default:Vn(e,n)}}function Wm(e){var t=e.alternate;t!==null&&(e.alternate=null,Wm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&eu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var qe=null,bt=!1;function xn(e,t,n){for(n=n.child;n!==null;)Im(e,t,n),n=n.sibling}function Im(e,t,n){if(St&&typeof St.onCommitFiberUnmount=="function")try{St.onCommitFiberUnmount(Hl,n)}catch{}switch(n.tag){case 26:Pe||Jt(n,t),xn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Pe||Jt(n,t);var l=qe,i=bt;Pn(n.type)&&(qe=n.stateNode,bt=!1),xn(e,t,n),Cr(n.stateNode),qe=l,bt=i;break;case 5:Pe||Jt(n,t);case 6:if(l=qe,i=bt,qe=null,xn(e,t,n),qe=l,bt=i,qe!==null)if(bt)try{(qe.nodeType===9?qe.body:qe.nodeName==="HTML"?qe.ownerDocument.body:qe).removeChild(n.stateNode)}catch(c){Be(n,t,c)}else try{qe.removeChild(n.stateNode)}catch(c){Be(n,t,c)}break;case 18:qe!==null&&(bt?(e=qe,Vh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Ur(e)):Vh(qe,n.stateNode));break;case 4:l=qe,i=bt,qe=n.stateNode.containerInfo,bt=!0,xn(e,t,n),qe=l,bt=i;break;case 0:case 11:case 14:case 15:Pe||Gn(2,n,t),Pe||Gn(4,n,t),xn(e,t,n);break;case 1:Pe||(Jt(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Zm(n,t,l)),xn(e,t,n);break;case 21:xn(e,t,n);break;case 22:Pe=(l=Pe)||n.memoizedState!==null,xn(e,t,n),Pe=l;break;default:xn(e,t,n)}}function eh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ur(e)}catch(n){Be(t,t.return,n)}}function bb(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Fm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Fm),t;default:throw Error(o(435,e.tag))}}function wc(e,t){var n=bb(e);t.forEach(function(l){var i=Nb.bind(null,e,l);n.has(l)||(n.add(l),l.then(i,i))})}function At(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],c=e,g=t,x=g;e:for(;x!==null;){switch(x.tag){case 27:if(Pn(x.type)){qe=x.stateNode,bt=!1;break e}break;case 5:qe=x.stateNode,bt=!1;break e;case 3:case 4:qe=x.stateNode.containerInfo,bt=!0;break e}x=x.return}if(qe===null)throw Error(o(160));Im(c,g,i),qe=null,bt=!1,c=i.alternate,c!==null&&(c.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)th(t,e),t=t.sibling}var qt=null;function th(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:At(t,e),Rt(e),l&4&&(Gn(3,e,e.return),vr(3,e),Gn(5,e,e.return));break;case 1:At(t,e),Rt(e),l&512&&(Pe||n===null||Jt(n,n.return)),l&64&&bn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=qt;if(At(t,e),Rt(e),l&512&&(Pe||n===null||Jt(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":c=i.getElementsByTagName("title")[0],(!c||c[ql]||c[ct]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=i.createElement(l),i.head.insertBefore(c,i.querySelector("head > title"))),ot(c,l,n),c[ct]=e,et(c),l=c;break e;case"link":var g=$h("link","href",i).get(l+(n.href||""));if(g){for(var x=0;x<g.length;x++)if(c=g[x],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){g.splice(x,1);break t}}c=i.createElement(l),ot(c,l,n),i.head.appendChild(c);break;case"meta":if(g=$h("meta","content",i).get(l+(n.content||""))){for(x=0;x<g.length;x++)if(c=g[x],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){g.splice(x,1);break t}}c=i.createElement(l),ot(c,l,n),i.head.appendChild(c);break;default:throw Error(o(468,l))}c[ct]=e,et(c),l=c}e.stateNode=l}else Fh(i,e.type,e.stateNode);else e.stateNode=Ph(i,l,e.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?Fh(i,e.type,e.stateNode):Ph(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&yc(e,e.memoizedProps,n.memoizedProps)}break;case 27:At(t,e),Rt(e),l&512&&(Pe||n===null||Jt(n,n.return)),n!==null&&l&4&&yc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(At(t,e),Rt(e),l&512&&(Pe||n===null||Jt(n,n.return)),e.flags&32){i=e.stateNode;try{Xa(i,"")}catch(B){Be(e,e.return,B)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,yc(e,i,n!==null?n.memoizedProps:i)),l&1024&&(Sc=!0);break;case 6:if(At(t,e),Rt(e),l&4){if(e.stateNode===null)throw Error(o(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(B){Be(e,e.return,B)}}break;case 3:if(lo=null,i=qt,qt=no(t.containerInfo),At(t,e),qt=i,Rt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Ur(t.containerInfo)}catch(B){Be(e,e.return,B)}Sc&&(Sc=!1,nh(e));break;case 4:l=qt,qt=no(e.stateNode.containerInfo),At(t,e),Rt(e),qt=l;break;case 12:At(t,e),Rt(e);break;case 13:At(t,e),Rt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Nc=ut()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,wc(e,l)));break;case 22:i=e.memoizedState!==null;var A=n!==null&&n.memoizedState!==null,L=bn,q=Pe;if(bn=L||i,Pe=q||A,At(t,e),Pe=q,bn=L,Rt(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||A||bn||Pe||Ca(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){A=n=t;try{if(c=A.stateNode,i)g=c.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{x=A.stateNode;var Z=A.memoizedProps.style,k=Z!=null&&Z.hasOwnProperty("display")?Z.display:null;x.style.display=k==null||typeof k=="boolean"?"":(""+k).trim()}}catch(B){Be(A,A.return,B)}}}else if(t.tag===6){if(n===null){A=t;try{A.stateNode.nodeValue=i?"":A.memoizedProps}catch(B){Be(A,A.return,B)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,wc(e,n))));break;case 19:At(t,e),Rt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,wc(e,l)));break;case 30:break;case 21:break;default:At(t,e),Rt(e)}}function Rt(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(Pm(l)){n=l;break}l=l.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var i=n.stateNode,c=bc(e);Yi(e,c,i);break;case 5:var g=n.stateNode;n.flags&32&&(Xa(g,""),n.flags&=-33);var x=bc(e);Yi(e,x,g);break;case 3:case 4:var A=n.stateNode.containerInfo,L=bc(e);xc(e,L,A);break;default:throw Error(o(161))}}catch(q){Be(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function nh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;nh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Vn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Jm(e,t.alternate,t),t=t.sibling}function Ca(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Gn(4,t,t.return),Ca(t);break;case 1:Jt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Zm(t,t.return,n),Ca(t);break;case 27:Cr(t.stateNode);case 26:case 5:Jt(t,t.return),Ca(t);break;case 22:t.memoizedState===null&&Ca(t);break;case 30:Ca(t);break;default:Ca(t)}e=e.sibling}}function qn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,c=t,g=c.flags;switch(c.tag){case 0:case 11:case 15:qn(i,c,n),vr(4,c);break;case 1:if(qn(i,c,n),l=c,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(L){Be(l,l.return,L)}if(l=c,i=l.updateQueue,i!==null){var x=l.stateNode;try{var A=i.shared.hiddenCallbacks;if(A!==null)for(i.shared.hiddenCallbacks=null,i=0;i<A.length;i++)_d(A[i],x)}catch(L){Be(l,l.return,L)}}n&&g&64&&Qm(c),yr(c,c.return);break;case 27:$m(c);case 26:case 5:qn(i,c,n),n&&l===null&&g&4&&Km(c),yr(c,c.return);break;case 12:qn(i,c,n);break;case 13:qn(i,c,n),n&&g&4&&eh(i,c);break;case 22:c.memoizedState===null&&qn(i,c,n),yr(c,c.return);break;case 30:break;default:qn(i,c,n)}t=t.sibling}}function Ec(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&ar(n))}function Tc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ar(e))}function Wt(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ah(e,t,n,l),t=t.sibling}function ah(e,t,n,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Wt(e,t,n,l),i&2048&&vr(9,t);break;case 1:Wt(e,t,n,l);break;case 3:Wt(e,t,n,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&ar(e)));break;case 12:if(i&2048){Wt(e,t,n,l),e=t.stateNode;try{var c=t.memoizedProps,g=c.id,x=c.onPostCommit;typeof x=="function"&&x(g,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(A){Be(t,t.return,A)}}else Wt(e,t,n,l);break;case 13:Wt(e,t,n,l);break;case 23:break;case 22:c=t.stateNode,g=t.alternate,t.memoizedState!==null?c._visibility&2?Wt(e,t,n,l):br(e,t):c._visibility&2?Wt(e,t,n,l):(c._visibility|=2,cl(e,t,n,l,(t.subtreeFlags&10256)!==0)),i&2048&&Ec(g,t);break;case 24:Wt(e,t,n,l),i&2048&&Tc(t.alternate,t);break;default:Wt(e,t,n,l)}}function cl(e,t,n,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,g=t,x=n,A=l,L=g.flags;switch(g.tag){case 0:case 11:case 15:cl(c,g,x,A,i),vr(8,g);break;case 23:break;case 22:var q=g.stateNode;g.memoizedState!==null?q._visibility&2?cl(c,g,x,A,i):br(c,g):(q._visibility|=2,cl(c,g,x,A,i)),i&&L&2048&&Ec(g.alternate,g);break;case 24:cl(c,g,x,A,i),i&&L&2048&&Tc(g.alternate,g);break;default:cl(c,g,x,A,i)}t=t.sibling}}function br(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,i=l.flags;switch(l.tag){case 22:br(n,l),i&2048&&Ec(l.alternate,l);break;case 24:br(n,l),i&2048&&Tc(l.alternate,l);break;default:br(n,l)}t=t.sibling}}var xr=8192;function sl(e){if(e.subtreeFlags&xr)for(e=e.child;e!==null;)lh(e),e=e.sibling}function lh(e){switch(e.tag){case 26:sl(e),e.flags&xr&&e.memoizedState!==null&&lx(qt,e.memoizedState,e.memoizedProps);break;case 5:sl(e);break;case 3:case 4:var t=qt;qt=no(e.stateNode.containerInfo),sl(e),qt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=xr,xr=16777216,sl(e),xr=t):sl(e));break;default:sl(e)}}function rh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Sr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];nt=l,oh(l,e)}rh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ih(e),e=e.sibling}function ih(e){switch(e.tag){case 0:case 11:case 15:Sr(e),e.flags&2048&&Gn(9,e,e.return);break;case 3:Sr(e);break;case 12:Sr(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Xi(e)):Sr(e);break;default:Sr(e)}}function Xi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];nt=l,oh(l,e)}rh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Gn(8,t,t.return),Xi(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Xi(t));break;default:Xi(t)}e=e.sibling}}function oh(e,t){for(;nt!==null;){var n=nt;switch(n.tag){case 0:case 11:case 15:Gn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ar(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,nt=l;else e:for(n=e;nt!==null;){l=nt;var i=l.sibling,c=l.return;if(Wm(l),l===n){nt=null;break e}if(i!==null){i.return=c,nt=i;break e}nt=c}}}var xb={getCacheForType:function(e){var t=st(Je),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Sb=typeof WeakMap=="function"?WeakMap:Map,ze=0,He=null,Te=null,Oe=0,je=0,Ct=null,Yn=!1,fl=!1,Ac=!1,Sn=0,Ze=0,Xn=0,Na=0,Rc=0,Ht=0,dl=0,wr=null,xt=null,Cc=!1,Nc=0,Qi=1/0,Zi=null,Qn=null,it=0,Zn=null,ml=null,hl=0,Oc=0,Mc=null,uh=null,Er=0,_c=null;function Nt(){if((ze&2)!==0&&Oe!==0)return Oe&-Oe;if(j.T!==null){var e=tl;return e!==0?e:Bc()}return Tf()}function ch(){Ht===0&&(Ht=(Oe&536870912)===0||_e?xf():536870912);var e=Bt.current;return e!==null&&(e.flags|=32),Ht}function Ot(e,t,n){(e===He&&(je===2||je===9)||e.cancelPendingCommit!==null)&&(pl(e,0),Kn(e,Oe,Ht,!1)),Vl(e,n),((ze&2)===0||e!==He)&&(e===He&&((ze&2)===0&&(Na|=n),Ze===4&&Kn(e,Oe,Ht,!1)),It(e))}function sh(e,t,n){if((ze&6)!==0)throw Error(o(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Gl(e,t),i=l?Tb(e,t):jc(e,t,!0),c=l;do{if(i===0){fl&&!l&&Kn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!wb(n)){i=jc(e,t,!1),c=!1;continue}if(i===2){if(c=t,e.errorRecoveryDisabledLanes&c)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){t=g;e:{var x=e;i=wr;var A=x.current.memoizedState.isDehydrated;if(A&&(pl(x,g).flags|=256),g=jc(x,g,!1),g!==2){if(Ac&&!A){x.errorRecoveryDisabledLanes|=c,Na|=c,i=4;break e}c=xt,xt=i,c!==null&&(xt===null?xt=c:xt.push.apply(xt,c))}i=g}if(c=!1,i!==2)continue}}if(i===1){pl(e,0),Kn(e,t,0,!0);break}e:{switch(l=e,c=i,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Kn(l,t,Ht,!Yn);break e;case 2:xt=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(i=Nc+300-ut(),10<i)){if(Kn(l,t,Ht,!Yn),ai(l,0,!0)!==0)break e;l.timeoutHandle=Hh(fh.bind(null,l,n,xt,Zi,Cc,t,Ht,Na,dl,Yn,c,2,-0,0),i);break e}fh(l,n,xt,Zi,Cc,t,Ht,Na,dl,Yn,c,0,-0,0)}}break}while(!0);It(e)}function fh(e,t,n,l,i,c,g,x,A,L,q,Z,k,B){if(e.timeoutHandle=-1,Z=t.subtreeFlags,(Z&8192||(Z&16785408)===16785408)&&(Mr={stylesheets:null,count:0,unsuspend:ax},lh(t),Z=rx(),Z!==null)){e.cancelPendingCommit=Z(yh.bind(null,e,t,c,n,l,i,g,x,A,q,1,k,B)),Kn(e,c,g,!L);return}yh(e,t,c,n,l,i,g,x,A)}function wb(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],c=i.getSnapshot;i=i.value;try{if(!Et(c(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Kn(e,t,n,l){t&=~Rc,t&=~Na,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var c=31-wt(i),g=1<<c;l[c]=-1,i&=~g}n!==0&&wf(e,n,t)}function Ki(){return(ze&6)===0?(Tr(0),!1):!0}function Dc(){if(Te!==null){if(je===0)var e=Te.return;else e=Te,mn=wa=null,$u(e),ol=null,hr=0,e=Te;for(;e!==null;)Xm(e.alternate,e),e=e.return;Te=null}}function pl(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,Gb(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Dc(),He=e,Te=n=sn(e.current,null),Oe=t,je=0,Ct=null,Yn=!1,fl=Gl(e,t),Ac=!1,dl=Ht=Rc=Na=Xn=Ze=0,xt=wr=null,Cc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-wt(l),c=1<<i;t|=e[i],l&=~c}return Sn=t,pi(),n}function dh(e,t){be=null,j.H=ji,t===rr||t===Ti?(t=Od(),je=3):t===Rd?(t=Od(),je=4):je=t===Mm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Ct=t,Te===null&&(Ze=1,Hi(e,jt(t,e.current)))}function mh(){var e=j.H;return j.H=ji,e===null?ji:e}function hh(){var e=j.A;return j.A=xb,e}function zc(){Ze=4,Yn||(Oe&4194048)!==Oe&&Bt.current!==null||(fl=!0),(Xn&134217727)===0&&(Na&134217727)===0||He===null||Kn(He,Oe,Ht,!1)}function jc(e,t,n){var l=ze;ze|=2;var i=mh(),c=hh();(He!==e||Oe!==t)&&(Zi=null,pl(e,t)),t=!1;var g=Ze;e:do try{if(je!==0&&Te!==null){var x=Te,A=Ct;switch(je){case 8:Dc(),g=6;break e;case 3:case 2:case 9:case 6:Bt.current===null&&(t=!0);var L=je;if(je=0,Ct=null,gl(e,x,A,L),n&&fl){g=0;break e}break;default:L=je,je=0,Ct=null,gl(e,x,A,L)}}Eb(),g=Ze;break}catch(q){dh(e,q)}while(!0);return t&&e.shellSuspendCounter++,mn=wa=null,ze=l,j.H=i,j.A=c,Te===null&&(He=null,Oe=0,pi()),g}function Eb(){for(;Te!==null;)ph(Te)}function Tb(e,t){var n=ze;ze|=2;var l=mh(),i=hh();He!==e||Oe!==t?(Zi=null,Qi=ut()+500,pl(e,t)):fl=Gl(e,t);e:do try{if(je!==0&&Te!==null){t=Te;var c=Ct;t:switch(je){case 1:je=0,Ct=null,gl(e,t,c,1);break;case 2:case 9:if(Cd(c)){je=0,Ct=null,gh(t);break}t=function(){je!==2&&je!==9||He!==e||(je=7),It(e)},c.then(t,t);break e;case 3:je=7;break e;case 4:je=5;break e;case 7:Cd(c)?(je=0,Ct=null,gh(t)):(je=0,Ct=null,gl(e,t,c,7));break;case 5:var g=null;switch(Te.tag){case 26:g=Te.memoizedState;case 5:case 27:var x=Te;if(!g||Jh(g)){je=0,Ct=null;var A=x.sibling;if(A!==null)Te=A;else{var L=x.return;L!==null?(Te=L,Pi(L)):Te=null}break t}}je=0,Ct=null,gl(e,t,c,5);break;case 6:je=0,Ct=null,gl(e,t,c,6);break;case 8:Dc(),Ze=6;break e;default:throw Error(o(462))}}Ab();break}catch(q){dh(e,q)}while(!0);return mn=wa=null,j.H=l,j.A=i,ze=n,Te!==null?0:(He=null,Oe=0,pi(),Ze)}function Ab(){for(;Te!==null&&!ca();)ph(Te)}function ph(e){var t=qm(e.alternate,e,Sn);e.memoizedProps=e.pendingProps,t===null?Pi(e):Te=t}function gh(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Lm(n,t,t.pendingProps,t.type,void 0,Oe);break;case 11:t=Lm(n,t,t.pendingProps,t.type.render,t.ref,Oe);break;case 5:$u(t);default:Xm(n,t),t=Te=vd(t,Sn),t=qm(n,t,Sn)}e.memoizedProps=e.pendingProps,t===null?Pi(e):Te=t}function gl(e,t,n,l){mn=wa=null,$u(t),ol=null,hr=0;var i=t.return;try{if(hb(e,i,t,n,Oe)){Ze=1,Hi(e,jt(n,e.current)),Te=null;return}}catch(c){if(i!==null)throw Te=i,c;Ze=1,Hi(e,jt(n,e.current)),Te=null;return}t.flags&32768?(_e||l===1?e=!0:fl||(Oe&536870912)!==0?e=!1:(Yn=e=!0,(l===2||l===9||l===3||l===6)&&(l=Bt.current,l!==null&&l.tag===13&&(l.flags|=16384))),vh(t,e)):Pi(t)}function Pi(e){var t=e;do{if((t.flags&32768)!==0){vh(t,Yn);return}e=t.return;var n=gb(t.alternate,t,Sn);if(n!==null){Te=n;return}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);Ze===0&&(Ze=5)}function vh(e,t){do{var n=vb(e.alternate,e);if(n!==null){n.flags&=32767,Te=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Te=e;return}Te=e=n}while(e!==null);Ze=6,Te=null}function yh(e,t,n,l,i,c,g,x,A){e.cancelPendingCommit=null;do $i();while(it!==0);if((ze&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=Tu,a0(e,n,c,g,x,A),e===He&&(Te=He=null,Oe=0),ml=t,Zn=e,hl=n,Oc=c,Mc=i,uh=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Ob(ei,function(){return Eh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=j.T,j.T=null,i=K.p,K.p=2,g=ze,ze|=4;try{yb(e,t,n)}finally{ze=g,K.p=i,j.T=l}}it=1,bh(),xh(),Sh()}}function bh(){if(it===1){it=0;var e=Zn,t=ml,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=j.T,j.T=null;var l=K.p;K.p=2;var i=ze;ze|=4;try{th(t,e);var c=Zc,g=od(e.containerInfo),x=c.focusedElem,A=c.selectionRange;if(g!==x&&x&&x.ownerDocument&&id(x.ownerDocument.documentElement,x)){if(A!==null&&bu(x)){var L=A.start,q=A.end;if(q===void 0&&(q=L),"selectionStart"in x)x.selectionStart=L,x.selectionEnd=Math.min(q,x.value.length);else{var Z=x.ownerDocument||document,k=Z&&Z.defaultView||window;if(k.getSelection){var B=k.getSelection(),me=x.textContent.length,se=Math.min(A.start,me),ke=A.end===void 0?se:Math.min(A.end,me);!B.extend&&se>ke&&(g=ke,ke=se,se=g);var z=rd(x,se),M=rd(x,ke);if(z&&M&&(B.rangeCount!==1||B.anchorNode!==z.node||B.anchorOffset!==z.offset||B.focusNode!==M.node||B.focusOffset!==M.offset)){var U=Z.createRange();U.setStart(z.node,z.offset),B.removeAllRanges(),se>ke?(B.addRange(U),B.extend(M.node,M.offset)):(U.setEnd(M.node,M.offset),B.addRange(U))}}}}for(Z=[],B=x;B=B.parentNode;)B.nodeType===1&&Z.push({element:B,left:B.scrollLeft,top:B.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<Z.length;x++){var Q=Z[x];Q.element.scrollLeft=Q.left,Q.element.scrollTop=Q.top}}oo=!!Qc,Zc=Qc=null}finally{ze=i,K.p=l,j.T=n}}e.current=t,it=2}}function xh(){if(it===2){it=0;var e=Zn,t=ml,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=j.T,j.T=null;var l=K.p;K.p=2;var i=ze;ze|=4;try{Jm(e,t.alternate,t)}finally{ze=i,K.p=l,j.T=n}}it=3}}function Sh(){if(it===4||it===3){it=0,sa();var e=Zn,t=ml,n=hl,l=uh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?it=5:(it=0,ml=Zn=null,wh(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Qn=null),Wo(n),t=t.stateNode,St&&typeof St.onCommitFiberRoot=="function")try{St.onCommitFiberRoot(Hl,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=j.T,i=K.p,K.p=2,j.T=null;try{for(var c=e.onRecoverableError,g=0;g<l.length;g++){var x=l[g];c(x.value,{componentStack:x.stack})}}finally{j.T=t,K.p=i}}(hl&3)!==0&&$i(),It(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===_c?Er++:(Er=0,_c=e):Er=0,Tr(0)}}function wh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,ar(t)))}function $i(e){return bh(),xh(),Sh(),Eh()}function Eh(){if(it!==5)return!1;var e=Zn,t=Oc;Oc=0;var n=Wo(hl),l=j.T,i=K.p;try{K.p=32>n?32:n,j.T=null,n=Mc,Mc=null;var c=Zn,g=hl;if(it=0,ml=Zn=null,hl=0,(ze&6)!==0)throw Error(o(331));var x=ze;if(ze|=4,ih(c.current),ah(c,c.current,g,n),ze=x,Tr(0,!1),St&&typeof St.onPostCommitFiberRoot=="function")try{St.onPostCommitFiberRoot(Hl,c)}catch{}return!0}finally{K.p=i,j.T=l,wh(e,t)}}function Th(e,t,n){t=jt(n,t),t=cc(e.stateNode,t,2),e=Ln(e,t,2),e!==null&&(Vl(e,2),It(e))}function Be(e,t,n){if(e.tag===3)Th(e,e,n);else for(;t!==null;){if(t.tag===3){Th(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Qn===null||!Qn.has(l))){e=jt(n,e),n=Nm(2),l=Ln(t,n,2),l!==null&&(Om(n,l,t,e),Vl(l,2),It(l));break}}t=t.return}}function Uc(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new Sb;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(n)||(Ac=!0,i.add(n),e=Rb.bind(null,e,t,n),t.then(e,e))}function Rb(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,He===e&&(Oe&n)===n&&(Ze===4||Ze===3&&(Oe&62914560)===Oe&&300>ut()-Nc?(ze&2)===0&&pl(e,0):Rc|=n,dl===Oe&&(dl=0)),It(e)}function Ah(e,t){t===0&&(t=Sf()),e=Ja(e,t),e!==null&&(Vl(e,t),It(e))}function Cb(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ah(e,n)}function Nb(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(t),Ah(e,n)}function Ob(e,t){return lt(e,t)}var Fi=null,vl=null,Lc=!1,Ji=!1,kc=!1,Oa=0;function It(e){e!==vl&&e.next===null&&(vl===null?Fi=vl=e:vl=vl.next=e),Ji=!0,Lc||(Lc=!0,_b())}function Tr(e,t){if(!kc&&Ji){kc=!0;do for(var n=!1,l=Fi;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var c=0;else{var g=l.suspendedLanes,x=l.pingedLanes;c=(1<<31-wt(42|e)+1)-1,c&=i&~(g&~x),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,Oh(l,c))}else c=Oe,c=ai(l,l===He?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Gl(l,c)||(n=!0,Oh(l,c));l=l.next}while(n);kc=!1}}function Mb(){Rh()}function Rh(){Ji=Lc=!1;var e=0;Oa!==0&&(Hb()&&(e=Oa),Oa=0);for(var t=ut(),n=null,l=Fi;l!==null;){var i=l.next,c=Ch(l,t);c===0?(l.next=null,n===null?Fi=i:n.next=i,i===null&&(vl=n)):(n=l,(e!==0||(c&3)!==0)&&(Ji=!0)),l=i}Tr(e)}function Ch(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var g=31-wt(c),x=1<<g,A=i[g];A===-1?((x&n)===0||(x&l)!==0)&&(i[g]=n0(x,t)):A<=t&&(e.expiredLanes|=x),c&=~x}if(t=He,n=Oe,n=ai(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(je===2||je===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&pt(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Gl(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&pt(l),Wo(n)){case 2:case 8:n=yf;break;case 32:n=ei;break;case 268435456:n=bf;break;default:n=ei}return l=Nh.bind(null,e),n=lt(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&pt(l),e.callbackPriority=2,e.callbackNode=null,2}function Nh(e,t){if(it!==0&&it!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if($i()&&e.callbackNode!==n)return null;var l=Oe;return l=ai(e,e===He?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(sh(e,l,t),Ch(e,ut()),e.callbackNode!=null&&e.callbackNode===n?Nh.bind(null,e):null)}function Oh(e,t){if($i())return null;sh(e,t,!0)}function _b(){Vb(function(){(ze&6)!==0?lt(fa,Mb):Rh()})}function Bc(){return Oa===0&&(Oa=xf()),Oa}function Mh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ui(""+e)}function _h(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function Db(e,t,n,l,i){if(t==="submit"&&n&&n.stateNode===i){var c=Mh((i[gt]||null).action),g=l.submitter;g&&(t=(t=g[gt]||null)?Mh(t.formAction):g.getAttribute("formAction"),t!==null&&(c=t,g=null));var x=new di("action","action",null,l,i);e.push({event:x,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Oa!==0){var A=g?_h(i,g):new FormData(i);lc(n,{pending:!0,data:A,method:i.method,action:c},null,A)}}else typeof c=="function"&&(x.preventDefault(),A=g?_h(i,g):new FormData(i),lc(n,{pending:!0,data:A,method:i.method,action:c},c,A))},currentTarget:i}]})}}for(var Hc=0;Hc<Eu.length;Hc++){var Gc=Eu[Hc],zb=Gc.toLowerCase(),jb=Gc[0].toUpperCase()+Gc.slice(1);Vt(zb,"on"+jb)}Vt(sd,"onAnimationEnd"),Vt(fd,"onAnimationIteration"),Vt(dd,"onAnimationStart"),Vt("dblclick","onDoubleClick"),Vt("focusin","onFocus"),Vt("focusout","onBlur"),Vt(J0,"onTransitionRun"),Vt(W0,"onTransitionStart"),Vt(I0,"onTransitionCancel"),Vt(md,"onTransitionEnd"),Va("onMouseEnter",["mouseout","mouseover"]),Va("onMouseLeave",["mouseout","mouseover"]),Va("onPointerEnter",["pointerout","pointerover"]),Va("onPointerLeave",["pointerout","pointerover"]),ma("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ma("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ma("onBeforeInput",["compositionend","keypress","textInput","paste"]),ma("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ma("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ma("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ar="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ub=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ar));function Dh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],i=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var g=l.length-1;0<=g;g--){var x=l[g],A=x.instance,L=x.currentTarget;if(x=x.listener,A!==c&&i.isPropagationStopped())break e;c=x,i.currentTarget=L;try{c(i)}catch(q){Bi(q)}i.currentTarget=null,c=A}else for(g=0;g<l.length;g++){if(x=l[g],A=x.instance,L=x.currentTarget,x=x.listener,A!==c&&i.isPropagationStopped())break e;c=x,i.currentTarget=L;try{c(i)}catch(q){Bi(q)}i.currentTarget=null,c=A}}}}function Ae(e,t){var n=t[Io];n===void 0&&(n=t[Io]=new Set);var l=e+"__bubble";n.has(l)||(zh(t,e,2,!1),n.add(l))}function Vc(e,t,n){var l=0;t&&(l|=4),zh(n,e,l,t)}var Wi="_reactListening"+Math.random().toString(36).slice(2);function qc(e){if(!e[Wi]){e[Wi]=!0,Rf.forEach(function(n){n!=="selectionchange"&&(Ub.has(n)||Vc(n,!1,e),Vc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Wi]||(t[Wi]=!0,Vc("selectionchange",!1,t))}}function zh(e,t,n,l){switch(ap(t)){case 2:var i=ux;break;case 8:i=cx;break;default:i=ns}n=i.bind(null,t,n,e),i=void 0,!su||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Yc(e,t,n,l,i){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var g=l.tag;if(g===3||g===4){var x=l.stateNode.containerInfo;if(x===i)break;if(g===4)for(g=l.return;g!==null;){var A=g.tag;if((A===3||A===4)&&g.stateNode.containerInfo===i)return;g=g.return}for(;x!==null;){if(g=Ba(x),g===null)return;if(A=g.tag,A===5||A===6||A===26||A===27){l=c=g;continue e}x=x.parentNode}}l=l.return}Gf(function(){var L=c,q=uu(n),Z=[];e:{var k=hd.get(e);if(k!==void 0){var B=di,me=e;switch(e){case"keypress":if(si(n)===0)break e;case"keydown":case"keyup":B=O0;break;case"focusin":me="focus",B=hu;break;case"focusout":me="blur",B=hu;break;case"beforeblur":case"afterblur":B=hu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":B=Yf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":B=v0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":B=D0;break;case sd:case fd:case dd:B=x0;break;case md:B=j0;break;case"scroll":case"scrollend":B=p0;break;case"wheel":B=L0;break;case"copy":case"cut":case"paste":B=w0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":B=Qf;break;case"toggle":case"beforetoggle":B=B0}var se=(t&4)!==0,ke=!se&&(e==="scroll"||e==="scrollend"),z=se?k!==null?k+"Capture":null:k;se=[];for(var M=L,U;M!==null;){var Q=M;if(U=Q.stateNode,Q=Q.tag,Q!==5&&Q!==26&&Q!==27||U===null||z===null||(Q=Xl(M,z),Q!=null&&se.push(Rr(M,Q,U))),ke)break;M=M.return}0<se.length&&(k=new B(k,me,null,n,q),Z.push({event:k,listeners:se}))}}if((t&7)===0){e:{if(k=e==="mouseover"||e==="pointerover",B=e==="mouseout"||e==="pointerout",k&&n!==ou&&(me=n.relatedTarget||n.fromElement)&&(Ba(me)||me[ka]))break e;if((B||k)&&(k=q.window===q?q:(k=q.ownerDocument)?k.defaultView||k.parentWindow:window,B?(me=n.relatedTarget||n.toElement,B=L,me=me?Ba(me):null,me!==null&&(ke=f(me),se=me.tag,me!==ke||se!==5&&se!==27&&se!==6)&&(me=null)):(B=null,me=L),B!==me)){if(se=Yf,Q="onMouseLeave",z="onMouseEnter",M="mouse",(e==="pointerout"||e==="pointerover")&&(se=Qf,Q="onPointerLeave",z="onPointerEnter",M="pointer"),ke=B==null?k:Yl(B),U=me==null?k:Yl(me),k=new se(Q,M+"leave",B,n,q),k.target=ke,k.relatedTarget=U,Q=null,Ba(q)===L&&(se=new se(z,M+"enter",me,n,q),se.target=U,se.relatedTarget=ke,Q=se),ke=Q,B&&me)t:{for(se=B,z=me,M=0,U=se;U;U=yl(U))M++;for(U=0,Q=z;Q;Q=yl(Q))U++;for(;0<M-U;)se=yl(se),M--;for(;0<U-M;)z=yl(z),U--;for(;M--;){if(se===z||z!==null&&se===z.alternate)break t;se=yl(se),z=yl(z)}se=null}else se=null;B!==null&&jh(Z,k,B,se,!1),me!==null&&ke!==null&&jh(Z,ke,me,se,!0)}}e:{if(k=L?Yl(L):window,B=k.nodeName&&k.nodeName.toLowerCase(),B==="select"||B==="input"&&k.type==="file")var te=If;else if(Jf(k))if(ed)te=P0;else{te=Z0;var Se=Q0}else B=k.nodeName,!B||B.toLowerCase()!=="input"||k.type!=="checkbox"&&k.type!=="radio"?L&&iu(L.elementType)&&(te=If):te=K0;if(te&&(te=te(e,L))){Wf(Z,te,n,q);break e}Se&&Se(e,k,L),e==="focusout"&&L&&k.type==="number"&&L.memoizedProps.value!=null&&ru(k,"number",k.value)}switch(Se=L?Yl(L):window,e){case"focusin":(Jf(Se)||Se.contentEditable==="true")&&(Pa=Se,xu=L,Wl=null);break;case"focusout":Wl=xu=Pa=null;break;case"mousedown":Su=!0;break;case"contextmenu":case"mouseup":case"dragend":Su=!1,ud(Z,n,q);break;case"selectionchange":if(F0)break;case"keydown":case"keyup":ud(Z,n,q)}var re;if(gu)e:{switch(e){case"compositionstart":var fe="onCompositionStart";break e;case"compositionend":fe="onCompositionEnd";break e;case"compositionupdate":fe="onCompositionUpdate";break e}fe=void 0}else Ka?$f(e,n)&&(fe="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(fe="onCompositionStart");fe&&(Zf&&n.locale!=="ko"&&(Ka||fe!=="onCompositionStart"?fe==="onCompositionEnd"&&Ka&&(re=Vf()):(Dn=q,fu="value"in Dn?Dn.value:Dn.textContent,Ka=!0)),Se=Ii(L,fe),0<Se.length&&(fe=new Xf(fe,e,null,n,q),Z.push({event:fe,listeners:Se}),re?fe.data=re:(re=Ff(n),re!==null&&(fe.data=re)))),(re=G0?V0(e,n):q0(e,n))&&(fe=Ii(L,"onBeforeInput"),0<fe.length&&(Se=new Xf("onBeforeInput","beforeinput",null,n,q),Z.push({event:Se,listeners:fe}),Se.data=re)),Db(Z,e,L,n,q)}Dh(Z,t)})}function Rr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ii(e,t){for(var n=t+"Capture",l=[];e!==null;){var i=e,c=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||c===null||(i=Xl(e,n),i!=null&&l.unshift(Rr(e,i,c)),i=Xl(e,t),i!=null&&l.push(Rr(e,i,c))),e.tag===3)return l;e=e.return}return[]}function yl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function jh(e,t,n,l,i){for(var c=t._reactName,g=[];n!==null&&n!==l;){var x=n,A=x.alternate,L=x.stateNode;if(x=x.tag,A!==null&&A===l)break;x!==5&&x!==26&&x!==27||L===null||(A=L,i?(L=Xl(n,c),L!=null&&g.unshift(Rr(n,L,A))):i||(L=Xl(n,c),L!=null&&g.push(Rr(n,L,A)))),n=n.return}g.length!==0&&e.push({event:t,listeners:g})}var Lb=/\r\n?/g,kb=/\u0000|\uFFFD/g;function Uh(e){return(typeof e=="string"?e:""+e).replace(Lb,`
`).replace(kb,"")}function Lh(e,t){return t=Uh(t),Uh(e)===t}function eo(){}function Le(e,t,n,l,i,c){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Xa(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Xa(e,""+l);break;case"className":ri(e,"class",l);break;case"tabIndex":ri(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ri(e,n,l);break;case"style":Bf(e,l,c);break;case"data":if(t!=="object"){ri(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ui(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Le(e,t,"name",i.name,i,null),Le(e,t,"formEncType",i.formEncType,i,null),Le(e,t,"formMethod",i.formMethod,i,null),Le(e,t,"formTarget",i.formTarget,i,null)):(Le(e,t,"encType",i.encType,i,null),Le(e,t,"method",i.method,i,null),Le(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=ui(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=eo);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=ui(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Ae("beforetoggle",e),Ae("toggle",e),li(e,"popover",l);break;case"xlinkActuate":un(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":un(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":un(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":un(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":un(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":un(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":un(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":un(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":un(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":li(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=m0.get(n)||n,li(e,n,l))}}function Xc(e,t,n,l,i,c){switch(n){case"style":Bf(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"children":typeof l=="string"?Xa(e,l):(typeof l=="number"||typeof l=="bigint")&&Xa(e,""+l);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"onClick":l!=null&&(e.onclick=eo);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Cf.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),c=e[gt]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,i),typeof l=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,i);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):li(e,n,l)}}}function ot(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ae("error",e),Ae("load",e);var l=!1,i=!1,c;for(c in n)if(n.hasOwnProperty(c)){var g=n[c];if(g!=null)switch(c){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Le(e,t,c,g,n,null)}}i&&Le(e,t,"srcSet",n.srcSet,n,null),l&&Le(e,t,"src",n.src,n,null);return;case"input":Ae("invalid",e);var x=c=g=i=null,A=null,L=null;for(l in n)if(n.hasOwnProperty(l)){var q=n[l];if(q!=null)switch(l){case"name":i=q;break;case"type":g=q;break;case"checked":A=q;break;case"defaultChecked":L=q;break;case"value":c=q;break;case"defaultValue":x=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(o(137,t));break;default:Le(e,t,l,q,n,null)}}jf(e,c,x,A,L,g,i,!1),ii(e);return;case"select":Ae("invalid",e),l=g=c=null;for(i in n)if(n.hasOwnProperty(i)&&(x=n[i],x!=null))switch(i){case"value":c=x;break;case"defaultValue":g=x;break;case"multiple":l=x;default:Le(e,t,i,x,n,null)}t=c,n=g,e.multiple=!!l,t!=null?Ya(e,!!l,t,!1):n!=null&&Ya(e,!!l,n,!0);return;case"textarea":Ae("invalid",e),c=i=l=null;for(g in n)if(n.hasOwnProperty(g)&&(x=n[g],x!=null))switch(g){case"value":l=x;break;case"defaultValue":i=x;break;case"children":c=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(o(91));break;default:Le(e,t,g,x,n,null)}Lf(e,l,i,c),ii(e);return;case"option":for(A in n)if(n.hasOwnProperty(A)&&(l=n[A],l!=null))switch(A){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Le(e,t,A,l,n,null)}return;case"dialog":Ae("beforetoggle",e),Ae("toggle",e),Ae("cancel",e),Ae("close",e);break;case"iframe":case"object":Ae("load",e);break;case"video":case"audio":for(l=0;l<Ar.length;l++)Ae(Ar[l],e);break;case"image":Ae("error",e),Ae("load",e);break;case"details":Ae("toggle",e);break;case"embed":case"source":case"link":Ae("error",e),Ae("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(L in n)if(n.hasOwnProperty(L)&&(l=n[L],l!=null))switch(L){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Le(e,t,L,l,n,null)}return;default:if(iu(t)){for(q in n)n.hasOwnProperty(q)&&(l=n[q],l!==void 0&&Xc(e,t,q,l,n,void 0));return}}for(x in n)n.hasOwnProperty(x)&&(l=n[x],l!=null&&Le(e,t,x,l,n,null))}function Bb(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,c=null,g=null,x=null,A=null,L=null,q=null;for(B in n){var Z=n[B];if(n.hasOwnProperty(B)&&Z!=null)switch(B){case"checked":break;case"value":break;case"defaultValue":A=Z;default:l.hasOwnProperty(B)||Le(e,t,B,null,l,Z)}}for(var k in l){var B=l[k];if(Z=n[k],l.hasOwnProperty(k)&&(B!=null||Z!=null))switch(k){case"type":c=B;break;case"name":i=B;break;case"checked":L=B;break;case"defaultChecked":q=B;break;case"value":g=B;break;case"defaultValue":x=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(o(137,t));break;default:B!==Z&&Le(e,t,k,B,l,Z)}}lu(e,g,x,A,L,q,c,i);return;case"select":B=g=x=k=null;for(c in n)if(A=n[c],n.hasOwnProperty(c)&&A!=null)switch(c){case"value":break;case"multiple":B=A;default:l.hasOwnProperty(c)||Le(e,t,c,null,l,A)}for(i in l)if(c=l[i],A=n[i],l.hasOwnProperty(i)&&(c!=null||A!=null))switch(i){case"value":k=c;break;case"defaultValue":x=c;break;case"multiple":g=c;default:c!==A&&Le(e,t,i,c,l,A)}t=x,n=g,l=B,k!=null?Ya(e,!!n,k,!1):!!l!=!!n&&(t!=null?Ya(e,!!n,t,!0):Ya(e,!!n,n?[]:"",!1));return;case"textarea":B=k=null;for(x in n)if(i=n[x],n.hasOwnProperty(x)&&i!=null&&!l.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:Le(e,t,x,null,l,i)}for(g in l)if(i=l[g],c=n[g],l.hasOwnProperty(g)&&(i!=null||c!=null))switch(g){case"value":k=i;break;case"defaultValue":B=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(o(91));break;default:i!==c&&Le(e,t,g,i,l,c)}Uf(e,k,B);return;case"option":for(var me in n)if(k=n[me],n.hasOwnProperty(me)&&k!=null&&!l.hasOwnProperty(me))switch(me){case"selected":e.selected=!1;break;default:Le(e,t,me,null,l,k)}for(A in l)if(k=l[A],B=n[A],l.hasOwnProperty(A)&&k!==B&&(k!=null||B!=null))switch(A){case"selected":e.selected=k&&typeof k!="function"&&typeof k!="symbol";break;default:Le(e,t,A,k,l,B)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var se in n)k=n[se],n.hasOwnProperty(se)&&k!=null&&!l.hasOwnProperty(se)&&Le(e,t,se,null,l,k);for(L in l)if(k=l[L],B=n[L],l.hasOwnProperty(L)&&k!==B&&(k!=null||B!=null))switch(L){case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(o(137,t));break;default:Le(e,t,L,k,l,B)}return;default:if(iu(t)){for(var ke in n)k=n[ke],n.hasOwnProperty(ke)&&k!==void 0&&!l.hasOwnProperty(ke)&&Xc(e,t,ke,void 0,l,k);for(q in l)k=l[q],B=n[q],!l.hasOwnProperty(q)||k===B||k===void 0&&B===void 0||Xc(e,t,q,k,l,B);return}}for(var z in n)k=n[z],n.hasOwnProperty(z)&&k!=null&&!l.hasOwnProperty(z)&&Le(e,t,z,null,l,k);for(Z in l)k=l[Z],B=n[Z],!l.hasOwnProperty(Z)||k===B||k==null&&B==null||Le(e,t,Z,k,l,B)}var Qc=null,Zc=null;function to(e){return e.nodeType===9?e:e.ownerDocument}function kh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Bh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Kc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Pc=null;function Hb(){var e=window.event;return e&&e.type==="popstate"?e===Pc?!1:(Pc=e,!0):(Pc=null,!1)}var Hh=typeof setTimeout=="function"?setTimeout:void 0,Gb=typeof clearTimeout=="function"?clearTimeout:void 0,Gh=typeof Promise=="function"?Promise:void 0,Vb=typeof queueMicrotask=="function"?queueMicrotask:typeof Gh<"u"?function(e){return Gh.resolve(null).then(e).catch(qb)}:Hh;function qb(e){setTimeout(function(){throw e})}function Pn(e){return e==="head"}function Vh(e,t){var n=t,l=0,i=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var g=e.ownerDocument;if(n&1&&Cr(g.documentElement),n&2&&Cr(g.body),n&4)for(n=g.head,Cr(n),g=n.firstChild;g;){var x=g.nextSibling,A=g.nodeName;g[ql]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&g.rel.toLowerCase()==="stylesheet"||n.removeChild(g),g=x}}if(i===0){e.removeChild(c),Ur(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);Ur(t)}function $c(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":$c(n),eu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function Yb(e,t,n,l){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[ql])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Yt(e.nextSibling),e===null)break}return null}function Xb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Yt(e.nextSibling),e===null))return null;return e}function Fc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Qb(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Yt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Jc=null;function qh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Yh(e,t,n){switch(t=to(n),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Cr(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);eu(e)}var Gt=new Map,Xh=new Set;function no(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var wn=K.d;K.d={f:Zb,r:Kb,D:Pb,C:$b,L:Fb,m:Jb,X:Ib,S:Wb,M:ex};function Zb(){var e=wn.f(),t=Ki();return e||t}function Kb(e){var t=Ha(e);t!==null&&t.tag===5&&t.type==="form"?sm(t):wn.r(e)}var bl=typeof document>"u"?null:document;function Qh(e,t,n){var l=bl;if(l&&typeof t=="string"&&t){var i=zt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Xh.has(i)||(Xh.add(i),e={rel:e,crossOrigin:n,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),ot(t,"link",e),et(t),l.head.appendChild(t)))}}function Pb(e){wn.D(e),Qh("dns-prefetch",e,null)}function $b(e,t){wn.C(e,t),Qh("preconnect",e,t)}function Fb(e,t,n){wn.L(e,t,n);var l=bl;if(l&&e&&t){var i='link[rel="preload"][as="'+zt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+zt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+zt(n.imageSizes)+'"]')):i+='[href="'+zt(e)+'"]';var c=i;switch(t){case"style":c=xl(e);break;case"script":c=Sl(e)}Gt.has(c)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Gt.set(c,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(Nr(c))||t==="script"&&l.querySelector(Or(c))||(t=l.createElement("link"),ot(t,"link",e),et(t),l.head.appendChild(t)))}}function Jb(e,t){wn.m(e,t);var n=bl;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+zt(l)+'"][href="'+zt(e)+'"]',c=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Sl(e)}if(!Gt.has(c)&&(e=y({rel:"modulepreload",href:e},t),Gt.set(c,e),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Or(c)))return}l=n.createElement("link"),ot(l,"link",e),et(l),n.head.appendChild(l)}}}function Wb(e,t,n){wn.S(e,t,n);var l=bl;if(l&&e){var i=Ga(l).hoistableStyles,c=xl(e);t=t||"default";var g=i.get(c);if(!g){var x={loading:0,preload:null};if(g=l.querySelector(Nr(c)))x.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Gt.get(c))&&Wc(e,n);var A=g=l.createElement("link");et(A),ot(A,"link",e),A._p=new Promise(function(L,q){A.onload=L,A.onerror=q}),A.addEventListener("load",function(){x.loading|=1}),A.addEventListener("error",function(){x.loading|=2}),x.loading|=4,ao(g,t,l)}g={type:"stylesheet",instance:g,count:1,state:x},i.set(c,g)}}}function Ib(e,t){wn.X(e,t);var n=bl;if(n&&e){var l=Ga(n).hoistableScripts,i=Sl(e),c=l.get(i);c||(c=n.querySelector(Or(i)),c||(e=y({src:e,async:!0},t),(t=Gt.get(i))&&Ic(e,t),c=n.createElement("script"),et(c),ot(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function ex(e,t){wn.M(e,t);var n=bl;if(n&&e){var l=Ga(n).hoistableScripts,i=Sl(e),c=l.get(i);c||(c=n.querySelector(Or(i)),c||(e=y({src:e,async:!0,type:"module"},t),(t=Gt.get(i))&&Ic(e,t),c=n.createElement("script"),et(c),ot(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function Zh(e,t,n,l){var i=(i=ie.current)?no(i):null;if(!i)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=xl(n.href),n=Ga(i).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=xl(n.href);var c=Ga(i).hoistableStyles,g=c.get(e);if(g||(i=i.ownerDocument||i,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,g),(c=i.querySelector(Nr(e)))&&!c._p&&(g.instance=c,g.state.loading=5),Gt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Gt.set(e,n),c||tx(i,e,n,g.state))),t&&l===null)throw Error(o(528,""));return g}if(t&&l!==null)throw Error(o(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Sl(n),n=Ga(i).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function xl(e){return'href="'+zt(e)+'"'}function Nr(e){return'link[rel="stylesheet"]['+e+"]"}function Kh(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function tx(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),ot(t,"link",n),et(t),e.head.appendChild(t))}function Sl(e){return'[src="'+zt(e)+'"]'}function Or(e){return"script[async]"+e}function Ph(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+zt(n.href)+'"]');if(l)return t.instance=l,et(l),l;var i=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),et(l),ot(l,"style",i),ao(l,n.precedence,e),t.instance=l;case"stylesheet":i=xl(n.href);var c=e.querySelector(Nr(i));if(c)return t.state.loading|=4,t.instance=c,et(c),c;l=Kh(n),(i=Gt.get(i))&&Wc(l,i),c=(e.ownerDocument||e).createElement("link"),et(c);var g=c;return g._p=new Promise(function(x,A){g.onload=x,g.onerror=A}),ot(c,"link",l),t.state.loading|=4,ao(c,n.precedence,e),t.instance=c;case"script":return c=Sl(n.src),(i=e.querySelector(Or(c)))?(t.instance=i,et(i),i):(l=n,(i=Gt.get(c))&&(l=y({},n),Ic(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),et(i),ot(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,ao(l,n.precedence,e));return t.instance}function ao(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,c=i,g=0;g<l.length;g++){var x=l[g];if(x.dataset.precedence===t)c=x;else if(c!==i)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Wc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Ic(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var lo=null;function $h(e,t,n){if(lo===null){var l=new Map,i=lo=new Map;i.set(n,l)}else i=lo,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var c=n[i];if(!(c[ql]||c[ct]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var g=c.getAttribute(t)||"";g=e+g;var x=l.get(g);x?x.push(c):l.set(g,[c])}}return l}function Fh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function nx(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Jh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Mr=null;function ax(){}function lx(e,t,n){if(Mr===null)throw Error(o(475));var l=Mr;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=xl(n.href),c=e.querySelector(Nr(i));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=ro.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,et(c);return}c=e.ownerDocument||e,n=Kh(n),(i=Gt.get(i))&&Wc(n,i),c=c.createElement("link"),et(c);var g=c;g._p=new Promise(function(x,A){g.onload=x,g.onerror=A}),ot(c,"link",n),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=ro.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function rx(){if(Mr===null)throw Error(o(475));var e=Mr;return e.stylesheets&&e.count===0&&es(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&es(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function ro(){if(this.count--,this.count===0){if(this.stylesheets)es(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var io=null;function es(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,io=new Map,t.forEach(ix,e),io=null,ro.call(e))}function ix(e,t){if(!(t.state.loading&4)){var n=io.get(e);if(n)var l=n.get(null);else{n=new Map,io.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<i.length;c++){var g=i[c];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(n.set(g.dataset.precedence,g),l=g)}l&&n.set(null,l)}i=t.instance,g=i.getAttribute("data-precedence"),c=n.get(g)||l,c===l&&n.set(null,i),n.set(g,i),this.count++,l=ro.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),c?c.parentNode.insertBefore(i,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var _r={$$typeof:D,Provider:null,Consumer:null,_currentValue:H,_currentValue2:H,_threadCount:0};function ox(e,t,n,l,i,c,g,x){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Fo(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fo(0),this.hiddenUpdates=Fo(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=c,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function Wh(e,t,n,l,i,c,g,x,A,L,q,Z){return e=new ox(e,t,n,g,x,A,L,Z),t=1,c===!0&&(t|=24),c=Tt(3,null,null,t),e.current=c,c.stateNode=e,t=Uu(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:t},Hu(c),e}function Ih(e){return e?(e=Wa,e):Wa}function ep(e,t,n,l,i,c){i=Ih(i),l.context===null?l.context=i:l.pendingContext=i,l=Un(t),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=Ln(e,l,t),n!==null&&(Ot(n,e,t),or(n,e,t))}function tp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ts(e,t){tp(e,t),(e=e.alternate)&&tp(e,t)}function np(e){if(e.tag===13){var t=Ja(e,67108864);t!==null&&Ot(t,e,67108864),ts(e,67108864)}}var oo=!0;function ux(e,t,n,l){var i=j.T;j.T=null;var c=K.p;try{K.p=2,ns(e,t,n,l)}finally{K.p=c,j.T=i}}function cx(e,t,n,l){var i=j.T;j.T=null;var c=K.p;try{K.p=8,ns(e,t,n,l)}finally{K.p=c,j.T=i}}function ns(e,t,n,l){if(oo){var i=as(l);if(i===null)Yc(e,t,l,uo,n),lp(e,l);else if(fx(i,e,t,n,l))l.stopPropagation();else if(lp(e,l),t&4&&-1<sx.indexOf(e)){for(;i!==null;){var c=Ha(i);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var g=da(c.pendingLanes);if(g!==0){var x=c;for(x.pendingLanes|=2,x.entangledLanes|=2;g;){var A=1<<31-wt(g);x.entanglements[1]|=A,g&=~A}It(c),(ze&6)===0&&(Qi=ut()+500,Tr(0))}}break;case 13:x=Ja(c,2),x!==null&&Ot(x,c,2),Ki(),ts(c,2)}if(c=as(l),c===null&&Yc(e,t,l,uo,n),c===i)break;i=c}i!==null&&l.stopPropagation()}else Yc(e,t,l,null,n)}}function as(e){return e=uu(e),ls(e)}var uo=null;function ls(e){if(uo=null,e=Ba(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return uo=e,null}function ap(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch($o()){case fa:return 2;case yf:return 8;case ei:case Fy:return 32;case bf:return 268435456;default:return 32}default:return 32}}var rs=!1,$n=null,Fn=null,Jn=null,Dr=new Map,zr=new Map,Wn=[],sx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lp(e,t){switch(e){case"focusin":case"focusout":$n=null;break;case"dragenter":case"dragleave":Fn=null;break;case"mouseover":case"mouseout":Jn=null;break;case"pointerover":case"pointerout":Dr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zr.delete(t.pointerId)}}function jr(e,t,n,l,i,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[i]},t!==null&&(t=Ha(t),t!==null&&np(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function fx(e,t,n,l,i){switch(t){case"focusin":return $n=jr($n,e,t,n,l,i),!0;case"dragenter":return Fn=jr(Fn,e,t,n,l,i),!0;case"mouseover":return Jn=jr(Jn,e,t,n,l,i),!0;case"pointerover":var c=i.pointerId;return Dr.set(c,jr(Dr.get(c)||null,e,t,n,l,i)),!0;case"gotpointercapture":return c=i.pointerId,zr.set(c,jr(zr.get(c)||null,e,t,n,l,i)),!0}return!1}function rp(e){var t=Ba(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,l0(e.priority,function(){if(n.tag===13){var l=Nt();l=Jo(l);var i=Ja(n,l);i!==null&&Ot(i,n,l),ts(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function co(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=as(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);ou=l,n.target.dispatchEvent(l),ou=null}else return t=Ha(n),t!==null&&np(t),e.blockedOn=n,!1;t.shift()}return!0}function ip(e,t,n){co(e)&&n.delete(t)}function dx(){rs=!1,$n!==null&&co($n)&&($n=null),Fn!==null&&co(Fn)&&(Fn=null),Jn!==null&&co(Jn)&&(Jn=null),Dr.forEach(ip),zr.forEach(ip)}function so(e,t){e.blockedOn===t&&(e.blockedOn=null,rs||(rs=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,dx)))}var fo=null;function op(e){fo!==e&&(fo=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){fo===e&&(fo=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(ls(l||n)===null)continue;break}var c=Ha(n);c!==null&&(e.splice(t,3),t-=3,lc(c,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Ur(e){function t(A){return so(A,e)}$n!==null&&so($n,e),Fn!==null&&so(Fn,e),Jn!==null&&so(Jn,e),Dr.forEach(t),zr.forEach(t);for(var n=0;n<Wn.length;n++){var l=Wn[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Wn.length&&(n=Wn[0],n.blockedOn===null);)rp(n),n.blockedOn===null&&Wn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],c=n[l+1],g=i[gt]||null;if(typeof c=="function")g||op(n);else if(g){var x=null;if(c&&c.hasAttribute("formAction")){if(i=c,g=c[gt]||null)x=g.formAction;else if(ls(i)!==null)continue}else x=g.action;typeof x=="function"?n[l+1]=x:(n.splice(l,3),l-=3),op(n)}}}function is(e){this._internalRoot=e}mo.prototype.render=is.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var n=t.current,l=Nt();ep(n,l,e,t,null,null)},mo.prototype.unmount=is.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ep(e.current,2,null,e,null,null),Ki(),t[ka]=null}};function mo(e){this._internalRoot=e}mo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Tf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Wn.length&&t!==0&&t<Wn[n].priority;n++);Wn.splice(n,0,e),n===0&&rp(e)}};var up=r.version;if(up!=="19.1.0")throw Error(o(527,up,"19.1.0"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var mx={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ho=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ho.isDisabled&&ho.supportsFiber)try{Hl=ho.inject(mx),St=ho}catch{}}return kr.createRoot=function(e,t){if(!s(e))throw Error(o(299));var n=!1,l="",i=Tm,c=Am,g=Rm,x=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(g=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(x=t.unstable_transitionCallbacks)),t=Wh(e,1,!1,null,null,n,l,i,c,g,x,null),e[ka]=t.current,qc(e),new is(t)},kr.hydrateRoot=function(e,t,n){if(!s(e))throw Error(o(299));var l=!1,i="",c=Tm,g=Am,x=Rm,A=null,L=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(g=n.onCaughtError),n.onRecoverableError!==void 0&&(x=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(A=n.unstable_transitionCallbacks),n.formState!==void 0&&(L=n.formState)),t=Wh(e,1,!0,t,n??null,l,i,c,g,x,A,L),t.context=Ih(null),n=t.current,l=Nt(),l=Jo(l),i=Un(l),i.callback=null,Ln(n,i,l),n=l,t.current.lanes=n,Vl(t,n),It(t),e[ka]=t.current,qc(e),new mo(t)},kr.version="19.1.0",kr}var yp;function Ex(){if(yp)return cs.exports;yp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),cs.exports=wx(),cs.exports}var Tx=Ex(),Br={},bp;function Ax(){if(bp)return Br;bp=1,Object.defineProperty(Br,"__esModule",{value:!0}),Br.parse=d,Br.serialize=m;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,u=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,s=Object.prototype.toString,f=(()=>{const S=function(){};return S.prototype=Object.create(null),S})();function d(S,R){const T=new f,E=S.length;if(E<2)return T;const N=(R==null?void 0:R.decode)||y;let O=0;do{const _=S.indexOf("=",O);if(_===-1)break;const D=S.indexOf(";",O),G=D===-1?E:D;if(_>G){O=S.lastIndexOf(";",_-1)+1;continue}const V=h(S,O,_),F=p(S,_,V),P=S.slice(V,F);if(T[P]===void 0){let Y=h(S,_+1,G),ee=p(S,G,Y);const ce=N(S.slice(Y,ee));T[P]=ce}O=G+1}while(O<E);return T}function h(S,R,T){do{const E=S.charCodeAt(R);if(E!==32&&E!==9)return R}while(++R<T);return T}function p(S,R,T){for(;R>T;){const E=S.charCodeAt(--R);if(E!==32&&E!==9)return R+1}return T}function m(S,R,T){const E=(T==null?void 0:T.encode)||encodeURIComponent;if(!a.test(S))throw new TypeError(`argument name is invalid: ${S}`);const N=E(R);if(!r.test(N))throw new TypeError(`argument val is invalid: ${R}`);let O=S+"="+N;if(!T)return O;if(T.maxAge!==void 0){if(!Number.isInteger(T.maxAge))throw new TypeError(`option maxAge is invalid: ${T.maxAge}`);O+="; Max-Age="+T.maxAge}if(T.domain){if(!u.test(T.domain))throw new TypeError(`option domain is invalid: ${T.domain}`);O+="; Domain="+T.domain}if(T.path){if(!o.test(T.path))throw new TypeError(`option path is invalid: ${T.path}`);O+="; Path="+T.path}if(T.expires){if(!w(T.expires)||!Number.isFinite(T.expires.valueOf()))throw new TypeError(`option expires is invalid: ${T.expires}`);O+="; Expires="+T.expires.toUTCString()}if(T.httpOnly&&(O+="; HttpOnly"),T.secure&&(O+="; Secure"),T.partitioned&&(O+="; Partitioned"),T.priority)switch(typeof T.priority=="string"?T.priority.toLowerCase():void 0){case"low":O+="; Priority=Low";break;case"medium":O+="; Priority=Medium";break;case"high":O+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${T.priority}`)}if(T.sameSite)switch(typeof T.sameSite=="string"?T.sameSite.toLowerCase():T.sameSite){case!0:case"strict":O+="; SameSite=Strict";break;case"lax":O+="; SameSite=Lax";break;case"none":O+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${T.sameSite}`)}return O}function y(S){if(S.indexOf("%")===-1)return S;try{return decodeURIComponent(S)}catch{return S}}function w(S){return s.call(S)==="[object Date]"}return Br}Ax();var xp="popstate";function Rx(a={}){function r(o,s){let{pathname:f,search:d,hash:h}=o.location;return Cs("",{pathname:f,search:d,hash:h},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function u(o,s){return typeof s=="string"?s:Xr(s)}return Nx(r,u,null,a)}function Xe(a,r){if(a===!1||a===null||typeof a>"u")throw new Error(r)}function an(a,r){if(!a){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Cx(){return Math.random().toString(36).substring(2,10)}function Sp(a,r){return{usr:a.state,key:a.key,idx:r}}function Cs(a,r,u=null,o){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof r=="string"?_l(r):r,state:u,key:r&&r.key||o||Cx()}}function Xr({pathname:a="/",search:r="",hash:u=""}){return r&&r!=="?"&&(a+=r.charAt(0)==="?"?r:"?"+r),u&&u!=="#"&&(a+=u.charAt(0)==="#"?u:"#"+u),a}function _l(a){let r={};if(a){let u=a.indexOf("#");u>=0&&(r.hash=a.substring(u),a=a.substring(0,u));let o=a.indexOf("?");o>=0&&(r.search=a.substring(o),a=a.substring(0,o)),a&&(r.pathname=a)}return r}function Nx(a,r,u,o={}){let{window:s=document.defaultView,v5Compat:f=!1}=o,d=s.history,h="POP",p=null,m=y();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function y(){return(d.state||{idx:null}).idx}function w(){h="POP";let N=y(),O=N==null?null:N-m;m=N,p&&p({action:h,location:E.location,delta:O})}function S(N,O){h="PUSH";let _=Cs(E.location,N,O);m=y()+1;let D=Sp(_,m),G=E.createHref(_);try{d.pushState(D,"",G)}catch(V){if(V instanceof DOMException&&V.name==="DataCloneError")throw V;s.location.assign(G)}f&&p&&p({action:h,location:E.location,delta:1})}function R(N,O){h="REPLACE";let _=Cs(E.location,N,O);m=y();let D=Sp(_,m),G=E.createHref(_);d.replaceState(D,"",G),f&&p&&p({action:h,location:E.location,delta:0})}function T(N){return Ox(N)}let E={get action(){return h},get location(){return a(s,d)},listen(N){if(p)throw new Error("A history only accepts one active listener");return s.addEventListener(xp,w),p=N,()=>{s.removeEventListener(xp,w),p=null}},createHref(N){return r(s,N)},createURL:T,encodeLocation(N){let O=T(N);return{pathname:O.pathname,search:O.search,hash:O.hash}},push:S,replace:R,go(N){return d.go(N)}};return E}function Ox(a,r=!1){let u="http://localhost";typeof window<"u"&&(u=window.location.origin!=="null"?window.location.origin:window.location.href),Xe(u,"No window.location.(origin|href) available to create URL");let o=typeof a=="string"?a:Xr(a);return o=o.replace(/ $/,"%20"),!r&&o.startsWith("//")&&(o=u+o),new URL(o,u)}function gg(a,r,u="/"){return Mx(a,r,u,!1)}function Mx(a,r,u,o){let s=typeof r=="string"?_l(r):r,f=Rn(s.pathname||"/",u);if(f==null)return null;let d=vg(a);_x(d);let h=null;for(let p=0;h==null&&p<d.length;++p){let m=qx(f);h=Gx(d[p],m,o)}return h}function vg(a,r=[],u=[],o=""){let s=(f,d,h)=>{let p={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};p.relativePath.startsWith("/")&&(Xe(p.relativePath.startsWith(o),`Absolute route path "${p.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(o.length));let m=Tn([o,p.relativePath]),y=u.concat(p);f.children&&f.children.length>0&&(Xe(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),vg(f.children,r,y,m)),!(f.path==null&&!f.index)&&r.push({path:m,score:Bx(m,f.index),routesMeta:y})};return a.forEach((f,d)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))s(f,d);else for(let p of yg(f.path))s(f,d,p)}),r}function yg(a){let r=a.split("/");if(r.length===0)return[];let[u,...o]=r,s=u.endsWith("?"),f=u.replace(/\?$/,"");if(o.length===0)return s?[f,""]:[f];let d=yg(o.join("/")),h=[];return h.push(...d.map(p=>p===""?f:[f,p].join("/"))),s&&h.push(...d),h.map(p=>a.startsWith("/")&&p===""?"/":p)}function _x(a){a.sort((r,u)=>r.score!==u.score?u.score-r.score:Hx(r.routesMeta.map(o=>o.childrenIndex),u.routesMeta.map(o=>o.childrenIndex)))}var Dx=/^:[\w-]+$/,zx=3,jx=2,Ux=1,Lx=10,kx=-2,wp=a=>a==="*";function Bx(a,r){let u=a.split("/"),o=u.length;return u.some(wp)&&(o+=kx),r&&(o+=jx),u.filter(s=>!wp(s)).reduce((s,f)=>s+(Dx.test(f)?zx:f===""?Ux:Lx),o)}function Hx(a,r){return a.length===r.length&&a.slice(0,-1).every((o,s)=>o===r[s])?a[a.length-1]-r[r.length-1]:0}function Gx(a,r,u=!1){let{routesMeta:o}=a,s={},f="/",d=[];for(let h=0;h<o.length;++h){let p=o[h],m=h===o.length-1,y=f==="/"?r:r.slice(f.length)||"/",w=Oo({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},y),S=p.route;if(!w&&m&&u&&!o[o.length-1].route.index&&(w=Oo({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},y)),!w)return null;Object.assign(s,w.params),d.push({params:s,pathname:Tn([f,w.pathname]),pathnameBase:Zx(Tn([f,w.pathnameBase])),route:S}),w.pathnameBase!=="/"&&(f=Tn([f,w.pathnameBase]))}return d}function Oo(a,r){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[u,o]=Vx(a.path,a.caseSensitive,a.end),s=r.match(u);if(!s)return null;let f=s[0],d=f.replace(/(.)\/+$/,"$1"),h=s.slice(1);return{params:o.reduce((m,{paramName:y,isOptional:w},S)=>{if(y==="*"){let T=h[S]||"";d=f.slice(0,f.length-T.length).replace(/(.)\/+$/,"$1")}const R=h[S];return w&&!R?m[y]=void 0:m[y]=(R||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:d,pattern:a}}function Vx(a,r=!1,u=!0){an(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let o=[],s="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,h,p)=>(o.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(o.push({paramName:"*"}),s+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):u?s+="\\/*$":a!==""&&a!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,r?void 0:"i"),o]}function qx(a){try{return a.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return an(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),a}}function Rn(a,r){if(r==="/")return a;if(!a.toLowerCase().startsWith(r.toLowerCase()))return null;let u=r.endsWith("/")?r.length-1:r.length,o=a.charAt(u);return o&&o!=="/"?null:a.slice(u)||"/"}function Yx(a,r="/"){let{pathname:u,search:o="",hash:s=""}=typeof a=="string"?_l(a):a;return{pathname:u?u.startsWith("/")?u:Xx(u,r):r,search:Kx(o),hash:Px(s)}}function Xx(a,r){let u=r.replace(/\/+$/,"").split("/");return a.split("/").forEach(s=>{s===".."?u.length>1&&u.pop():s!=="."&&u.push(s)}),u.length>1?u.join("/"):"/"}function ms(a,r,u,o){return`Cannot include a '${a}' character in a manually specified \`to.${r}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${u}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Qx(a){return a.filter((r,u)=>u===0||r.route.path&&r.route.path.length>0)}function bg(a){let r=Qx(a);return r.map((u,o)=>o===r.length-1?u.pathname:u.pathnameBase)}function xg(a,r,u,o=!1){let s;typeof a=="string"?s=_l(a):(s={...a},Xe(!s.pathname||!s.pathname.includes("?"),ms("?","pathname","search",s)),Xe(!s.pathname||!s.pathname.includes("#"),ms("#","pathname","hash",s)),Xe(!s.search||!s.search.includes("#"),ms("#","search","hash",s)));let f=a===""||s.pathname==="",d=f?"/":s.pathname,h;if(d==null)h=u;else{let w=r.length-1;if(!o&&d.startsWith("..")){let S=d.split("/");for(;S[0]==="..";)S.shift(),w-=1;s.pathname=S.join("/")}h=w>=0?r[w]:"/"}let p=Yx(s,h),m=d&&d!=="/"&&d.endsWith("/"),y=(f||d===".")&&u.endsWith("/");return!p.pathname.endsWith("/")&&(m||y)&&(p.pathname+="/"),p}var Tn=a=>a.join("/").replace(/\/\/+/g,"/"),Zx=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),Kx=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,Px=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function $x(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Sg=["POST","PUT","PATCH","DELETE"];new Set(Sg);var Fx=["GET",...Sg];new Set(Fx);var Dl=v.createContext(null);Dl.displayName="DataRouter";var ko=v.createContext(null);ko.displayName="DataRouterState";var wg=v.createContext({isTransitioning:!1});wg.displayName="ViewTransition";var Jx=v.createContext(new Map);Jx.displayName="Fetchers";var Wx=v.createContext(null);Wx.displayName="Await";var rn=v.createContext(null);rn.displayName="Navigation";var Kr=v.createContext(null);Kr.displayName="Location";var On=v.createContext({outlet:null,matches:[],isDataRoute:!1});On.displayName="Route";var Xs=v.createContext(null);Xs.displayName="RouteError";function Ix(a,{relative:r}={}){Xe(Pr(),"useHref() may be used only in the context of a <Router> component.");let{basename:u,navigator:o}=v.useContext(rn),{hash:s,pathname:f,search:d}=$r(a,{relative:r}),h=f;return u!=="/"&&(h=f==="/"?u:Tn([u,f])),o.createHref({pathname:h,search:d,hash:s})}function Pr(){return v.useContext(Kr)!=null}function ia(){return Xe(Pr(),"useLocation() may be used only in the context of a <Router> component."),v.useContext(Kr).location}var Eg="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Tg(a){v.useContext(rn).static||v.useLayoutEffect(a)}function e1(){let{isDataRoute:a}=v.useContext(On);return a?m1():t1()}function t1(){Xe(Pr(),"useNavigate() may be used only in the context of a <Router> component.");let a=v.useContext(Dl),{basename:r,navigator:u}=v.useContext(rn),{matches:o}=v.useContext(On),{pathname:s}=ia(),f=JSON.stringify(bg(o)),d=v.useRef(!1);return Tg(()=>{d.current=!0}),v.useCallback((p,m={})=>{if(an(d.current,Eg),!d.current)return;if(typeof p=="number"){u.go(p);return}let y=xg(p,JSON.parse(f),s,m.relative==="path");a==null&&r!=="/"&&(y.pathname=y.pathname==="/"?r:Tn([r,y.pathname])),(m.replace?u.replace:u.push)(y,m.state,m)},[r,u,f,s,a])}v.createContext(null);function $r(a,{relative:r}={}){let{matches:u}=v.useContext(On),{pathname:o}=ia(),s=JSON.stringify(bg(u));return v.useMemo(()=>xg(a,JSON.parse(s),o,r==="path"),[a,s,o,r])}function n1(a,r){return Ag(a,r)}function Ag(a,r,u,o){var O;Xe(Pr(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=v.useContext(rn),{matches:f}=v.useContext(On),d=f[f.length-1],h=d?d.params:{},p=d?d.pathname:"/",m=d?d.pathnameBase:"/",y=d&&d.route;{let _=y&&y.path||"";Rg(p,!y||_.endsWith("*")||_.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${_}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${_}"> to <Route path="${_==="/"?"*":`${_}/*`}">.`)}let w=ia(),S;if(r){let _=typeof r=="string"?_l(r):r;Xe(m==="/"||((O=_.pathname)==null?void 0:O.startsWith(m)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${_.pathname}" was given in the \`location\` prop.`),S=_}else S=w;let R=S.pathname||"/",T=R;if(m!=="/"){let _=m.replace(/^\//,"").split("/");T="/"+R.replace(/^\//,"").split("/").slice(_.length).join("/")}let E=gg(a,{pathname:T});an(y||E!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),an(E==null||E[E.length-1].route.element!==void 0||E[E.length-1].route.Component!==void 0||E[E.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let N=o1(E&&E.map(_=>Object.assign({},_,{params:Object.assign({},h,_.params),pathname:Tn([m,s.encodeLocation?s.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?m:Tn([m,s.encodeLocation?s.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),f,u,o);return r&&N?v.createElement(Kr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...S},navigationType:"POP"}},N):N}function a1(){let a=d1(),r=$x(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),u=a instanceof Error?a.stack:null,o="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:o},f={padding:"2px 4px",backgroundColor:o},d=null;return console.error("Error handled by React Router default ErrorBoundary:",a),d=v.createElement(v.Fragment,null,v.createElement("p",null,"💿 Hey developer 👋"),v.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",v.createElement("code",{style:f},"ErrorBoundary")," or"," ",v.createElement("code",{style:f},"errorElement")," prop on your route.")),v.createElement(v.Fragment,null,v.createElement("h2",null,"Unexpected Application Error!"),v.createElement("h3",{style:{fontStyle:"italic"}},r),u?v.createElement("pre",{style:s},u):null,d)}var l1=v.createElement(a1,null),r1=class extends v.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,r){return r.location!==a.location||r.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:r.error,location:r.location,revalidation:a.revalidation||r.revalidation}}componentDidCatch(a,r){console.error("React Router caught the following error during render",a,r)}render(){return this.state.error!==void 0?v.createElement(On.Provider,{value:this.props.routeContext},v.createElement(Xs.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function i1({routeContext:a,match:r,children:u}){let o=v.useContext(Dl);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),v.createElement(On.Provider,{value:a},u)}function o1(a,r=[],u=null,o=null){if(a==null){if(!u)return null;if(u.errors)a=u.matches;else if(r.length===0&&!u.initialized&&u.matches.length>0)a=u.matches;else return null}let s=a,f=u==null?void 0:u.errors;if(f!=null){let p=s.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);Xe(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),s=s.slice(0,Math.min(s.length,p+1))}let d=!1,h=-1;if(u)for(let p=0;p<s.length;p++){let m=s[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:y,errors:w}=u,S=m.route.loader&&!y.hasOwnProperty(m.route.id)&&(!w||w[m.route.id]===void 0);if(m.route.lazy||S){d=!0,h>=0?s=s.slice(0,h+1):s=[s[0]];break}}}return s.reduceRight((p,m,y)=>{let w,S=!1,R=null,T=null;u&&(w=f&&m.route.id?f[m.route.id]:void 0,R=m.route.errorElement||l1,d&&(h<0&&y===0?(Rg("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,T=null):h===y&&(S=!0,T=m.route.hydrateFallbackElement||null)));let E=r.concat(s.slice(0,y+1)),N=()=>{let O;return w?O=R:S?O=T:m.route.Component?O=v.createElement(m.route.Component,null):m.route.element?O=m.route.element:O=p,v.createElement(i1,{match:m,routeContext:{outlet:p,matches:E,isDataRoute:u!=null},children:O})};return u&&(m.route.ErrorBoundary||m.route.errorElement||y===0)?v.createElement(r1,{location:u.location,revalidation:u.revalidation,component:R,error:w,children:N(),routeContext:{outlet:null,matches:E,isDataRoute:!0}}):N()},null)}function Qs(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function u1(a){let r=v.useContext(Dl);return Xe(r,Qs(a)),r}function c1(a){let r=v.useContext(ko);return Xe(r,Qs(a)),r}function s1(a){let r=v.useContext(On);return Xe(r,Qs(a)),r}function Zs(a){let r=s1(a),u=r.matches[r.matches.length-1];return Xe(u.route.id,`${a} can only be used on routes that contain a unique "id"`),u.route.id}function f1(){return Zs("useRouteId")}function d1(){var o;let a=v.useContext(Xs),r=c1("useRouteError"),u=Zs("useRouteError");return a!==void 0?a:(o=r.errors)==null?void 0:o[u]}function m1(){let{router:a}=u1("useNavigate"),r=Zs("useNavigate"),u=v.useRef(!1);return Tg(()=>{u.current=!0}),v.useCallback(async(s,f={})=>{an(u.current,Eg),u.current&&(typeof s=="number"?a.navigate(s):await a.navigate(s,{fromRouteId:r,...f}))},[a,r])}var Ep={};function Rg(a,r,u){!r&&!Ep[a]&&(Ep[a]=!0,an(!1,u))}v.memo(h1);function h1({routes:a,future:r,state:u}){return Ag(a,void 0,u,r)}function qr(a){Xe(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function p1({basename:a="/",children:r=null,location:u,navigationType:o="POP",navigator:s,static:f=!1}){Xe(!Pr(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=a.replace(/^\/*/,"/"),h=v.useMemo(()=>({basename:d,navigator:s,static:f,future:{}}),[d,s,f]);typeof u=="string"&&(u=_l(u));let{pathname:p="/",search:m="",hash:y="",state:w=null,key:S="default"}=u,R=v.useMemo(()=>{let T=Rn(p,d);return T==null?null:{location:{pathname:T,search:m,hash:y,state:w,key:S},navigationType:o}},[d,p,m,y,w,S,o]);return an(R!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${y}" because it does not start with the basename, so the <Router> won't render anything.`),R==null?null:v.createElement(rn.Provider,{value:h},v.createElement(Kr.Provider,{children:r,value:R}))}function g1({children:a,location:r}){return n1(Ns(a),r)}function Ns(a,r=[]){let u=[];return v.Children.forEach(a,(o,s)=>{if(!v.isValidElement(o))return;let f=[...r,s];if(o.type===v.Fragment){u.push.apply(u,Ns(o.props.children,f));return}Xe(o.type===qr,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Xe(!o.props.index||!o.props.children,"An index route cannot have child routes.");let d={id:o.props.id||f.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(d.children=Ns(o.props.children,f)),u.push(d)}),u}var To="get",Ao="application/x-www-form-urlencoded";function Bo(a){return a!=null&&typeof a.tagName=="string"}function v1(a){return Bo(a)&&a.tagName.toLowerCase()==="button"}function y1(a){return Bo(a)&&a.tagName.toLowerCase()==="form"}function b1(a){return Bo(a)&&a.tagName.toLowerCase()==="input"}function x1(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function S1(a,r){return a.button===0&&(!r||r==="_self")&&!x1(a)}var po=null;function w1(){if(po===null)try{new FormData(document.createElement("form"),0),po=!1}catch{po=!0}return po}var E1=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function hs(a){return a!=null&&!E1.has(a)?(an(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ao}"`),null):a}function T1(a,r){let u,o,s,f,d;if(y1(a)){let h=a.getAttribute("action");o=h?Rn(h,r):null,u=a.getAttribute("method")||To,s=hs(a.getAttribute("enctype"))||Ao,f=new FormData(a)}else if(v1(a)||b1(a)&&(a.type==="submit"||a.type==="image")){let h=a.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=a.getAttribute("formaction")||h.getAttribute("action");if(o=p?Rn(p,r):null,u=a.getAttribute("formmethod")||h.getAttribute("method")||To,s=hs(a.getAttribute("formenctype"))||hs(h.getAttribute("enctype"))||Ao,f=new FormData(h,a),!w1()){let{name:m,type:y,value:w}=a;if(y==="image"){let S=m?`${m}.`:"";f.append(`${S}x`,"0"),f.append(`${S}y`,"0")}else m&&f.append(m,w)}}else{if(Bo(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');u=To,o=null,s=Ao,d=a}return f&&s==="text/plain"&&(d=f,f=void 0),{action:o,method:u.toLowerCase(),encType:s,formData:f,body:d}}function Ks(a,r){if(a===!1||a===null||typeof a>"u")throw new Error(r)}async function A1(a,r){if(a.id in r)return r[a.id];try{let u=await import(a.module);return r[a.id]=u,u}catch(u){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(u),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function R1(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function C1(a,r,u){let o=await Promise.all(a.map(async s=>{let f=r.routes[s.route.id];if(f){let d=await A1(f,u);return d.links?d.links():[]}return[]}));return _1(o.flat(1).filter(R1).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function Tp(a,r,u,o,s,f){let d=(p,m)=>u[m]?p.route.id!==u[m].route.id:!0,h=(p,m)=>{var y;return u[m].pathname!==p.pathname||((y=u[m].route.path)==null?void 0:y.endsWith("*"))&&u[m].params["*"]!==p.params["*"]};return f==="assets"?r.filter((p,m)=>d(p,m)||h(p,m)):f==="data"?r.filter((p,m)=>{var w;let y=o.routes[p.route.id];if(!y||!y.hasLoader)return!1;if(d(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let S=p.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:((w=u[0])==null?void 0:w.params)||{},nextUrl:new URL(a,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function N1(a,r,{includeHydrateFallback:u}={}){return O1(a.map(o=>{let s=r.routes[o.route.id];if(!s)return[];let f=[s.module];return s.clientActionModule&&(f=f.concat(s.clientActionModule)),s.clientLoaderModule&&(f=f.concat(s.clientLoaderModule)),u&&s.hydrateFallbackModule&&(f=f.concat(s.hydrateFallbackModule)),s.imports&&(f=f.concat(s.imports)),f}).flat(1))}function O1(a){return[...new Set(a)]}function M1(a){let r={},u=Object.keys(a).sort();for(let o of u)r[o]=a[o];return r}function _1(a,r){let u=new Set;return new Set(r),a.reduce((o,s)=>{let f=JSON.stringify(M1(s));return u.has(f)||(u.add(f),o.push({key:f,link:s})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var D1=new Set([100,101,204,205]);function z1(a,r){let u=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return u.pathname==="/"?u.pathname="_root.data":r&&Rn(u.pathname,r)==="/"?u.pathname=`${r.replace(/\/$/,"")}/_root.data`:u.pathname=`${u.pathname.replace(/\/$/,"")}.data`,u}function Cg(){let a=v.useContext(Dl);return Ks(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function j1(){let a=v.useContext(ko);return Ks(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Ps=v.createContext(void 0);Ps.displayName="FrameworkContext";function Ng(){let a=v.useContext(Ps);return Ks(a,"You must render this element inside a <HydratedRouter> element"),a}function U1(a,r){let u=v.useContext(Ps),[o,s]=v.useState(!1),[f,d]=v.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:y,onTouchStart:w}=r,S=v.useRef(null);v.useEffect(()=>{if(a==="render"&&d(!0),a==="viewport"){let E=O=>{O.forEach(_=>{d(_.isIntersecting)})},N=new IntersectionObserver(E,{threshold:.5});return S.current&&N.observe(S.current),()=>{N.disconnect()}}},[a]),v.useEffect(()=>{if(o){let E=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(E)}}},[o]);let R=()=>{s(!0)},T=()=>{s(!1),d(!1)};return u?a!=="intent"?[f,S,{}]:[f,S,{onFocus:Hr(h,R),onBlur:Hr(p,T),onMouseEnter:Hr(m,R),onMouseLeave:Hr(y,T),onTouchStart:Hr(w,R)}]:[!1,S,{}]}function Hr(a,r){return u=>{a&&a(u),u.defaultPrevented||r(u)}}function L1({page:a,...r}){let{router:u}=Cg(),o=v.useMemo(()=>gg(u.routes,a,u.basename),[u.routes,a,u.basename]);return o?v.createElement(B1,{page:a,matches:o,...r}):null}function k1(a){let{manifest:r,routeModules:u}=Ng(),[o,s]=v.useState([]);return v.useEffect(()=>{let f=!1;return C1(a,r,u).then(d=>{f||s(d)}),()=>{f=!0}},[a,r,u]),o}function B1({page:a,matches:r,...u}){let o=ia(),{manifest:s,routeModules:f}=Ng(),{basename:d}=Cg(),{loaderData:h,matches:p}=j1(),m=v.useMemo(()=>Tp(a,r,p,s,o,"data"),[a,r,p,s,o]),y=v.useMemo(()=>Tp(a,r,p,s,o,"assets"),[a,r,p,s,o]),w=v.useMemo(()=>{if(a===o.pathname+o.search+o.hash)return[];let T=new Set,E=!1;if(r.forEach(O=>{var D;let _=s.routes[O.route.id];!_||!_.hasLoader||(!m.some(G=>G.route.id===O.route.id)&&O.route.id in h&&((D=f[O.route.id])!=null&&D.shouldRevalidate)||_.hasClientLoader?E=!0:T.add(O.route.id))}),T.size===0)return[];let N=z1(a,d);return E&&T.size>0&&N.searchParams.set("_routes",r.filter(O=>T.has(O.route.id)).map(O=>O.route.id).join(",")),[N.pathname+N.search]},[d,h,o,s,m,r,a,f]),S=v.useMemo(()=>N1(y,s),[y,s]),R=k1(y);return v.createElement(v.Fragment,null,w.map(T=>v.createElement("link",{key:T,rel:"prefetch",as:"fetch",href:T,...u})),S.map(T=>v.createElement("link",{key:T,rel:"modulepreload",href:T,...u})),R.map(({key:T,link:E})=>v.createElement("link",{key:T,...E})))}function H1(...a){return r=>{a.forEach(u=>{typeof u=="function"?u(r):u!=null&&(u.current=r)})}}var Og=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Og&&(window.__reactRouterVersion="7.6.1")}catch{}function G1({basename:a,children:r,window:u}){let o=v.useRef();o.current==null&&(o.current=Rx({window:u,v5Compat:!0}));let s=o.current,[f,d]=v.useState({action:s.action,location:s.location}),h=v.useCallback(p=>{v.startTransition(()=>d(p))},[d]);return v.useLayoutEffect(()=>s.listen(h),[s,h]),v.createElement(p1,{basename:a,children:r,location:f.location,navigationType:f.action,navigator:s})}var Mg=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,$s=v.forwardRef(function({onClick:r,discover:u="render",prefetch:o="none",relative:s,reloadDocument:f,replace:d,state:h,target:p,to:m,preventScrollReset:y,viewTransition:w,...S},R){let{basename:T}=v.useContext(rn),E=typeof m=="string"&&Mg.test(m),N,O=!1;if(typeof m=="string"&&E&&(N=m,Og))try{let ee=new URL(window.location.href),ce=m.startsWith("//")?new URL(ee.protocol+m):new URL(m),he=Rn(ce.pathname,T);ce.origin===ee.origin&&he!=null?m=he+ce.search+ce.hash:O=!0}catch{an(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let _=Ix(m,{relative:s}),[D,G,V]=U1(o,S),F=X1(m,{replace:d,state:h,target:p,preventScrollReset:y,relative:s,viewTransition:w});function P(ee){r&&r(ee),ee.defaultPrevented||F(ee)}let Y=v.createElement("a",{...S,...V,href:N||_,onClick:O||f?r:P,ref:H1(R,G),target:p,"data-discover":!E&&u==="render"?"true":void 0});return D&&!E?v.createElement(v.Fragment,null,Y,v.createElement(L1,{page:_})):Y});$s.displayName="Link";var V1=v.forwardRef(function({"aria-current":r="page",caseSensitive:u=!1,className:o="",end:s=!1,style:f,to:d,viewTransition:h,children:p,...m},y){let w=$r(d,{relative:m.relative}),S=ia(),R=v.useContext(ko),{navigator:T,basename:E}=v.useContext(rn),N=R!=null&&$1(w)&&h===!0,O=T.encodeLocation?T.encodeLocation(w).pathname:w.pathname,_=S.pathname,D=R&&R.navigation&&R.navigation.location?R.navigation.location.pathname:null;u||(_=_.toLowerCase(),D=D?D.toLowerCase():null,O=O.toLowerCase()),D&&E&&(D=Rn(D,E)||D);const G=O!=="/"&&O.endsWith("/")?O.length-1:O.length;let V=_===O||!s&&_.startsWith(O)&&_.charAt(G)==="/",F=D!=null&&(D===O||!s&&D.startsWith(O)&&D.charAt(O.length)==="/"),P={isActive:V,isPending:F,isTransitioning:N},Y=V?r:void 0,ee;typeof o=="function"?ee=o(P):ee=[o,V?"active":null,F?"pending":null,N?"transitioning":null].filter(Boolean).join(" ");let ce=typeof f=="function"?f(P):f;return v.createElement($s,{...m,"aria-current":Y,className:ee,ref:y,style:ce,to:d,viewTransition:h},typeof p=="function"?p(P):p)});V1.displayName="NavLink";var q1=v.forwardRef(({discover:a="render",fetcherKey:r,navigate:u,reloadDocument:o,replace:s,state:f,method:d=To,action:h,onSubmit:p,relative:m,preventScrollReset:y,viewTransition:w,...S},R)=>{let T=K1(),E=P1(h,{relative:m}),N=d.toLowerCase()==="get"?"get":"post",O=typeof h=="string"&&Mg.test(h),_=D=>{if(p&&p(D),D.defaultPrevented)return;D.preventDefault();let G=D.nativeEvent.submitter,V=(G==null?void 0:G.getAttribute("formmethod"))||d;T(G||D.currentTarget,{fetcherKey:r,method:V,navigate:u,replace:s,state:f,relative:m,preventScrollReset:y,viewTransition:w})};return v.createElement("form",{ref:R,method:N,action:E,onSubmit:o?p:_,...S,"data-discover":!O&&a==="render"?"true":void 0})});q1.displayName="Form";function Y1(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function _g(a){let r=v.useContext(Dl);return Xe(r,Y1(a)),r}function X1(a,{target:r,replace:u,state:o,preventScrollReset:s,relative:f,viewTransition:d}={}){let h=e1(),p=ia(),m=$r(a,{relative:f});return v.useCallback(y=>{if(S1(y,r)){y.preventDefault();let w=u!==void 0?u:Xr(p)===Xr(m);h(a,{replace:w,state:o,preventScrollReset:s,relative:f,viewTransition:d})}},[p,h,m,u,o,r,a,s,f,d])}var Q1=0,Z1=()=>`__${String(++Q1)}__`;function K1(){let{router:a}=_g("useSubmit"),{basename:r}=v.useContext(rn),u=f1();return v.useCallback(async(o,s={})=>{let{action:f,method:d,encType:h,formData:p,body:m}=T1(o,r);if(s.navigate===!1){let y=s.fetcherKey||Z1();await a.fetch(y,u,s.action||f,{preventScrollReset:s.preventScrollReset,formData:p,body:m,formMethod:s.method||d,formEncType:s.encType||h,flushSync:s.flushSync})}else await a.navigate(s.action||f,{preventScrollReset:s.preventScrollReset,formData:p,body:m,formMethod:s.method||d,formEncType:s.encType||h,replace:s.replace,state:s.state,fromRouteId:u,flushSync:s.flushSync,viewTransition:s.viewTransition})},[a,r,u])}function P1(a,{relative:r}={}){let{basename:u}=v.useContext(rn),o=v.useContext(On);Xe(o,"useFormAction must be used inside a RouteContext");let[s]=o.matches.slice(-1),f={...$r(a||".",{relative:r})},d=ia();if(a==null){f.search=d.search;let h=new URLSearchParams(f.search),p=h.getAll("index");if(p.some(y=>y==="")){h.delete("index"),p.filter(w=>w).forEach(w=>h.append("index",w));let y=h.toString();f.search=y?`?${y}`:""}}return(!a||a===".")&&s.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),u!=="/"&&(f.pathname=f.pathname==="/"?u:Tn([u,f.pathname])),Xr(f)}function $1(a,r={}){let u=v.useContext(wg);Xe(u!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=_g("useViewTransitionState"),s=$r(a,{relative:r.relative});if(!u.isTransitioning)return!1;let f=Rn(u.currentLocation.pathname,o)||u.currentLocation.pathname,d=Rn(u.nextLocation.pathname,o)||u.nextLocation.pathname;return Oo(s.pathname,d)!=null||Oo(s.pathname,f)!=null}[...D1];var Fr=pg();const F1=mg(Fr),Ap=a=>{let r;const u=new Set,o=(m,y)=>{const w=typeof m=="function"?m(r):m;if(!Object.is(w,r)){const S=r;r=y??(typeof w!="object"||w===null)?w:Object.assign({},r,w),u.forEach(R=>R(r,S))}},s=()=>r,h={setState:o,getState:s,getInitialState:()=>p,subscribe:m=>(u.add(m),()=>u.delete(m))},p=r=a(o,s,h);return h},J1=a=>a?Ap(a):Ap,W1=a=>a;function I1(a,r=W1){const u=en.useSyncExternalStore(a.subscribe,()=>r(a.getState()),()=>r(a.getInitialState()));return en.useDebugValue(u),u}const Rp=a=>{const r=J1(a),u=o=>I1(r,o);return Object.assign(u,r),u},eS=a=>a?Rp(a):Rp,zl=eS((a,r)=>({theme:"light",toggleTheme:()=>a(u=>({theme:u.theme==="light"?"dark":"light"})),currentView:"chat",setCurrentView:u=>a({currentView:u}),generationSettings:{topic:"",writingStyle:"Analytical",culturalInflection:{},pages:1,humanization:!1,academicFormatting:{enabled:!1,citationStyle:"MLA",studentName:"",professorName:"",courseInfo:""}},updateGenerationSettings:u=>a(o=>({generationSettings:{...o.generationSettings,...u}})),documents:[],setDocuments:u=>a({documents:u}),addDocument:u=>a(o=>({documents:[u,...o.documents]})),deleteDocument:u=>a(o=>({documents:o.documents.filter(s=>s.id!==u)})),models:[],setModels:u=>a({models:u}),isGenerating:!1,generatedText:"",setIsGenerating:u=>a({isGenerating:u}),setGeneratedText:u=>a({generatedText:u}),appendGeneratedText:u=>a(o=>({generatedText:o.generatedText+u})),settingsTab:"document",setSettingsTab:u=>a({settingsTab:u})}));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tS=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nS=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,u,o)=>o?o.toUpperCase():u.toLowerCase()),Cp=a=>{const r=nS(a);return r.charAt(0).toUpperCase()+r.slice(1)},Dg=(...a)=>a.filter((r,u,o)=>!!r&&r.trim()!==""&&o.indexOf(r)===u).join(" ").trim(),aS=a=>{for(const r in a)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var lS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=v.forwardRef(({color:a="currentColor",size:r=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:s="",children:f,iconNode:d,...h},p)=>v.createElement("svg",{ref:p,...lS,width:r,height:r,stroke:a,strokeWidth:o?Number(u)*24/Number(r):u,className:Dg("lucide",s),...!f&&!aS(h)&&{"aria-hidden":"true"},...h},[...d.map(([m,y])=>v.createElement(m,y)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=(a,r)=>{const u=v.forwardRef(({className:o,...s},f)=>v.createElement(rS,{ref:f,iconNode:r,className:Dg(`lucide-${tS(Cp(a))}`,`lucide-${a}`,o),...s}));return u.displayName=Cp(a),u};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iS=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],oS=ht("check",iS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uS=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],zg=ht("chevron-down",uS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cS=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],sS=ht("chevron-up",cS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fS=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],dS=ht("circle-check-big",fS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mS=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],Np=ht("circle-x",mS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hS=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],pS=ht("eye",hS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gS=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Fs=ht("file-text",gS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vS=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],yS=ht("message-square",vS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bS=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],xS=ht("moon",bS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SS=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Op=ht("refresh-cw",SS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wS=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],ES=ht("send",wS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TS=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Mo=ht("settings",TS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AS=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],RS=ht("sun",AS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CS=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],NS=ht("trash-2",CS);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OS=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],MS=ht("x",OS);function Mp(a,r){if(typeof a=="function")return a(r);a!=null&&(a.current=r)}function _S(...a){return r=>{let u=!1;const o=a.map(s=>{const f=Mp(s,r);return!u&&typeof f=="function"&&(u=!0),f});if(u)return()=>{for(let s=0;s<o.length;s++){const f=o[s];typeof f=="function"?f():Mp(a[s],null)}}}}function Ge(...a){return v.useCallback(_S(...a),a)}function Nl(a){const r=DS(a),u=v.forwardRef((o,s)=>{const{children:f,...d}=o,h=v.Children.toArray(f),p=h.find(jS);if(p){const m=p.props.children,y=h.map(w=>w===p?v.Children.count(m)>1?v.Children.only(null):v.isValidElement(m)?m.props.children:null:w);return b.jsx(r,{...d,ref:s,children:v.isValidElement(m)?v.cloneElement(m,void 0,y):null})}return b.jsx(r,{...d,ref:s,children:f})});return u.displayName=`${a}.Slot`,u}var jg=Nl("Slot");function DS(a){const r=v.forwardRef((u,o)=>{const{children:s,...f}=u,d=v.isValidElement(s)?LS(s):void 0,h=Ge(d,o);if(v.isValidElement(s)){const p=US(f,s.props);return s.type!==v.Fragment&&(p.ref=h),v.cloneElement(s,p)}return v.Children.count(s)>1?v.Children.only(null):null});return r.displayName=`${a}.SlotClone`,r}var zS=Symbol("radix.slottable");function jS(a){return v.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===zS}function US(a,r){const u={...r};for(const o in r){const s=a[o],f=r[o];/^on[A-Z]/.test(o)?s&&f?u[o]=(...h)=>{const p=f(...h);return s(...h),p}:s&&(u[o]=s):o==="style"?u[o]={...s,...f}:o==="className"&&(u[o]=[s,f].filter(Boolean).join(" "))}return{...a,...u}}function LS(a){var o,s;let r=(o=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:o.get,u=r&&"isReactWarning"in r&&r.isReactWarning;return u?a.ref:(r=(s=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:s.get,u=r&&"isReactWarning"in r&&r.isReactWarning,u?a.props.ref:a.props.ref||a.ref)}function Ug(a){var r,u,o="";if(typeof a=="string"||typeof a=="number")o+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(r=0;r<s;r++)a[r]&&(u=Ug(a[r]))&&(o&&(o+=" "),o+=u)}else for(u in a)a[u]&&(o&&(o+=" "),o+=u);return o}function Lg(){for(var a,r,u=0,o="",s=arguments.length;u<s;u++)(a=arguments[u])&&(r=Ug(a))&&(o&&(o+=" "),o+=r);return o}const _p=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,Dp=Lg,kg=(a,r)=>u=>{var o;if((r==null?void 0:r.variants)==null)return Dp(a,u==null?void 0:u.class,u==null?void 0:u.className);const{variants:s,defaultVariants:f}=r,d=Object.keys(s).map(m=>{const y=u==null?void 0:u[m],w=f==null?void 0:f[m];if(y===null)return null;const S=_p(y)||_p(w);return s[m][S]}),h=u&&Object.entries(u).reduce((m,y)=>{let[w,S]=y;return S===void 0||(m[w]=S),m},{}),p=r==null||(o=r.compoundVariants)===null||o===void 0?void 0:o.reduce((m,y)=>{let{class:w,className:S,...R}=y;return Object.entries(R).every(T=>{let[E,N]=T;return Array.isArray(N)?N.includes({...f,...h}[E]):{...f,...h}[E]===N})?[...m,w,S]:m},[]);return Dp(a,d,p,u==null?void 0:u.class,u==null?void 0:u.className)},Js="-",kS=a=>{const r=HS(a),{conflictingClassGroups:u,conflictingClassGroupModifiers:o}=a;return{getClassGroupId:d=>{const h=d.split(Js);return h[0]===""&&h.length!==1&&h.shift(),Bg(h,r)||BS(d)},getConflictingClassGroupIds:(d,h)=>{const p=u[d]||[];return h&&o[d]?[...p,...o[d]]:p}}},Bg=(a,r)=>{var d;if(a.length===0)return r.classGroupId;const u=a[0],o=r.nextPart.get(u),s=o?Bg(a.slice(1),o):void 0;if(s)return s;if(r.validators.length===0)return;const f=a.join(Js);return(d=r.validators.find(({validator:h})=>h(f)))==null?void 0:d.classGroupId},zp=/^\[(.+)\]$/,BS=a=>{if(zp.test(a)){const r=zp.exec(a)[1],u=r==null?void 0:r.substring(0,r.indexOf(":"));if(u)return"arbitrary.."+u}},HS=a=>{const{theme:r,classGroups:u}=a,o={nextPart:new Map,validators:[]};for(const s in u)Os(u[s],o,s,r);return o},Os=(a,r,u,o)=>{a.forEach(s=>{if(typeof s=="string"){const f=s===""?r:jp(r,s);f.classGroupId=u;return}if(typeof s=="function"){if(GS(s)){Os(s(o),r,u,o);return}r.validators.push({validator:s,classGroupId:u});return}Object.entries(s).forEach(([f,d])=>{Os(d,jp(r,f),u,o)})})},jp=(a,r)=>{let u=a;return r.split(Js).forEach(o=>{u.nextPart.has(o)||u.nextPart.set(o,{nextPart:new Map,validators:[]}),u=u.nextPart.get(o)}),u},GS=a=>a.isThemeGetter,VS=a=>{if(a<1)return{get:()=>{},set:()=>{}};let r=0,u=new Map,o=new Map;const s=(f,d)=>{u.set(f,d),r++,r>a&&(r=0,o=u,u=new Map)};return{get(f){let d=u.get(f);if(d!==void 0)return d;if((d=o.get(f))!==void 0)return s(f,d),d},set(f,d){u.has(f)?u.set(f,d):s(f,d)}}},Ms="!",_s=":",qS=_s.length,YS=a=>{const{prefix:r,experimentalParseClassName:u}=a;let o=s=>{const f=[];let d=0,h=0,p=0,m;for(let T=0;T<s.length;T++){let E=s[T];if(d===0&&h===0){if(E===_s){f.push(s.slice(p,T)),p=T+qS;continue}if(E==="/"){m=T;continue}}E==="["?d++:E==="]"?d--:E==="("?h++:E===")"&&h--}const y=f.length===0?s:s.substring(p),w=XS(y),S=w!==y,R=m&&m>p?m-p:void 0;return{modifiers:f,hasImportantModifier:S,baseClassName:w,maybePostfixModifierPosition:R}};if(r){const s=r+_s,f=o;o=d=>d.startsWith(s)?f(d.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(u){const s=o;o=f=>u({className:f,parseClassName:s})}return o},XS=a=>a.endsWith(Ms)?a.substring(0,a.length-1):a.startsWith(Ms)?a.substring(1):a,QS=a=>{const r=Object.fromEntries(a.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const s=[];let f=[];return o.forEach(d=>{d[0]==="["||r[d]?(s.push(...f.sort(),d),f=[]):f.push(d)}),s.push(...f.sort()),s}},ZS=a=>({cache:VS(a.cacheSize),parseClassName:YS(a),sortModifiers:QS(a),...kS(a)}),KS=/\s+/,PS=(a,r)=>{const{parseClassName:u,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:f}=r,d=[],h=a.trim().split(KS);let p="";for(let m=h.length-1;m>=0;m-=1){const y=h[m],{isExternal:w,modifiers:S,hasImportantModifier:R,baseClassName:T,maybePostfixModifierPosition:E}=u(y);if(w){p=y+(p.length>0?" "+p:p);continue}let N=!!E,O=o(N?T.substring(0,E):T);if(!O){if(!N){p=y+(p.length>0?" "+p:p);continue}if(O=o(T),!O){p=y+(p.length>0?" "+p:p);continue}N=!1}const _=f(S).join(":"),D=R?_+Ms:_,G=D+O;if(d.includes(G))continue;d.push(G);const V=s(O,N);for(let F=0;F<V.length;++F){const P=V[F];d.push(D+P)}p=y+(p.length>0?" "+p:p)}return p};function $S(){let a=0,r,u,o="";for(;a<arguments.length;)(r=arguments[a++])&&(u=Hg(r))&&(o&&(o+=" "),o+=u);return o}const Hg=a=>{if(typeof a=="string")return a;let r,u="";for(let o=0;o<a.length;o++)a[o]&&(r=Hg(a[o]))&&(u&&(u+=" "),u+=r);return u};function FS(a,...r){let u,o,s,f=d;function d(p){const m=r.reduce((y,w)=>w(y),a());return u=ZS(m),o=u.cache.get,s=u.cache.set,f=h,h(p)}function h(p){const m=o(p);if(m)return m;const y=PS(p,u);return s(p,y),y}return function(){return f($S.apply(null,arguments))}}const Ie=a=>{const r=u=>u[a]||[];return r.isThemeGetter=!0,r},Gg=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Vg=/^\((?:(\w[\w-]*):)?(.+)\)$/i,JS=/^\d+\/\d+$/,WS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,IS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ew=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,tw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,nw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,wl=a=>JS.test(a),xe=a=>!!a&&!Number.isNaN(Number(a)),ea=a=>!!a&&Number.isInteger(Number(a)),ps=a=>a.endsWith("%")&&xe(a.slice(0,-1)),En=a=>WS.test(a),aw=()=>!0,lw=a=>IS.test(a)&&!ew.test(a),qg=()=>!1,rw=a=>tw.test(a),iw=a=>nw.test(a),ow=a=>!ne(a)&&!ae(a),uw=a=>jl(a,Qg,qg),ne=a=>Gg.test(a),Ma=a=>jl(a,Zg,lw),gs=a=>jl(a,mw,xe),Up=a=>jl(a,Yg,qg),cw=a=>jl(a,Xg,iw),go=a=>jl(a,Kg,rw),ae=a=>Vg.test(a),Gr=a=>Ul(a,Zg),sw=a=>Ul(a,hw),Lp=a=>Ul(a,Yg),fw=a=>Ul(a,Qg),dw=a=>Ul(a,Xg),vo=a=>Ul(a,Kg,!0),jl=(a,r,u)=>{const o=Gg.exec(a);return o?o[1]?r(o[1]):u(o[2]):!1},Ul=(a,r,u=!1)=>{const o=Vg.exec(a);return o?o[1]?r(o[1]):u:!1},Yg=a=>a==="position"||a==="percentage",Xg=a=>a==="image"||a==="url",Qg=a=>a==="length"||a==="size"||a==="bg-size",Zg=a=>a==="length",mw=a=>a==="number",hw=a=>a==="family-name",Kg=a=>a==="shadow",pw=()=>{const a=Ie("color"),r=Ie("font"),u=Ie("text"),o=Ie("font-weight"),s=Ie("tracking"),f=Ie("leading"),d=Ie("breakpoint"),h=Ie("container"),p=Ie("spacing"),m=Ie("radius"),y=Ie("shadow"),w=Ie("inset-shadow"),S=Ie("text-shadow"),R=Ie("drop-shadow"),T=Ie("blur"),E=Ie("perspective"),N=Ie("aspect"),O=Ie("ease"),_=Ie("animate"),D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],G=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],V=()=>[...G(),ae,ne],F=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],Y=()=>[ae,ne,p],ee=()=>[wl,"full","auto",...Y()],ce=()=>[ea,"none","subgrid",ae,ne],he=()=>["auto",{span:["full",ea,ae,ne]},ea,ae,ne],de=()=>[ea,"auto",ae,ne],ge=()=>["auto","min","max","fr",ae,ne],ye=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],oe=()=>["start","end","center","stretch","center-safe","end-safe"],j=()=>["auto",...Y()],K=()=>[wl,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Y()],H=()=>[a,ae,ne],le=()=>[...G(),Lp,Up,{position:[ae,ne]}],C=()=>["no-repeat",{repeat:["","x","y","space","round"]}],X=()=>["auto","cover","contain",fw,uw,{size:[ae,ne]}],J=()=>[ps,Gr,Ma],$=()=>["","none","full",m,ae,ne],I=()=>["",xe,Gr,Ma],pe=()=>["solid","dashed","dotted","double"],ie=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[xe,ps,Lp,Up],ue=()=>["","none",T,ae,ne],De=()=>["none",xe,ae,ne],Re=()=>["none",xe,ae,ne],we=()=>[xe,ae,ne],Ee=()=>[wl,"full",...Y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[En],breakpoint:[En],color:[aw],container:[En],"drop-shadow":[En],ease:["in","out","in-out"],font:[ow],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[En],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[En],shadow:[En],spacing:["px",xe],text:[En],"text-shadow":[En],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",wl,ne,ae,N]}],container:["container"],columns:[{columns:[xe,ne,ae,h]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:V()}],overflow:[{overflow:F()}],"overflow-x":[{"overflow-x":F()}],"overflow-y":[{"overflow-y":F()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ee()}],"inset-x":[{"inset-x":ee()}],"inset-y":[{"inset-y":ee()}],start:[{start:ee()}],end:[{end:ee()}],top:[{top:ee()}],right:[{right:ee()}],bottom:[{bottom:ee()}],left:[{left:ee()}],visibility:["visible","invisible","collapse"],z:[{z:[ea,"auto",ae,ne]}],basis:[{basis:[wl,"full","auto",h,...Y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[xe,wl,"auto","initial","none",ne]}],grow:[{grow:["",xe,ae,ne]}],shrink:[{shrink:["",xe,ae,ne]}],order:[{order:[ea,"first","last","none",ae,ne]}],"grid-cols":[{"grid-cols":ce()}],"col-start-end":[{col:he()}],"col-start":[{"col-start":de()}],"col-end":[{"col-end":de()}],"grid-rows":[{"grid-rows":ce()}],"row-start-end":[{row:he()}],"row-start":[{"row-start":de()}],"row-end":[{"row-end":de()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ge()}],"auto-rows":[{"auto-rows":ge()}],gap:[{gap:Y()}],"gap-x":[{"gap-x":Y()}],"gap-y":[{"gap-y":Y()}],"justify-content":[{justify:[...ye(),"normal"]}],"justify-items":[{"justify-items":[...oe(),"normal"]}],"justify-self":[{"justify-self":["auto",...oe()]}],"align-content":[{content:["normal",...ye()]}],"align-items":[{items:[...oe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...oe(),{baseline:["","last"]}]}],"place-content":[{"place-content":ye()}],"place-items":[{"place-items":[...oe(),"baseline"]}],"place-self":[{"place-self":["auto",...oe()]}],p:[{p:Y()}],px:[{px:Y()}],py:[{py:Y()}],ps:[{ps:Y()}],pe:[{pe:Y()}],pt:[{pt:Y()}],pr:[{pr:Y()}],pb:[{pb:Y()}],pl:[{pl:Y()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":Y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Y()}],"space-y-reverse":["space-y-reverse"],size:[{size:K()}],w:[{w:[h,"screen",...K()]}],"min-w":[{"min-w":[h,"screen","none",...K()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[d]},...K()]}],h:[{h:["screen","lh",...K()]}],"min-h":[{"min-h":["screen","lh","none",...K()]}],"max-h":[{"max-h":["screen","lh",...K()]}],"font-size":[{text:["base",u,Gr,Ma]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,ae,gs]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ps,ne]}],"font-family":[{font:[sw,ne,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,ae,ne]}],"line-clamp":[{"line-clamp":[xe,"none",ae,gs]}],leading:[{leading:[f,...Y()]}],"list-image":[{"list-image":["none",ae,ne]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ae,ne]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:H()}],"text-color":[{text:H()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pe(),"wavy"]}],"text-decoration-thickness":[{decoration:[xe,"from-font","auto",ae,Ma]}],"text-decoration-color":[{decoration:H()}],"underline-offset":[{"underline-offset":[xe,"auto",ae,ne]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ae,ne]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ae,ne]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:le()}],"bg-repeat":[{bg:C()}],"bg-size":[{bg:X()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ea,ae,ne],radial:["",ae,ne],conic:[ea,ae,ne]},dw,cw]}],"bg-color":[{bg:H()}],"gradient-from-pos":[{from:J()}],"gradient-via-pos":[{via:J()}],"gradient-to-pos":[{to:J()}],"gradient-from":[{from:H()}],"gradient-via":[{via:H()}],"gradient-to":[{to:H()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...pe(),"hidden","none"]}],"divide-style":[{divide:[...pe(),"hidden","none"]}],"border-color":[{border:H()}],"border-color-x":[{"border-x":H()}],"border-color-y":[{"border-y":H()}],"border-color-s":[{"border-s":H()}],"border-color-e":[{"border-e":H()}],"border-color-t":[{"border-t":H()}],"border-color-r":[{"border-r":H()}],"border-color-b":[{"border-b":H()}],"border-color-l":[{"border-l":H()}],"divide-color":[{divide:H()}],"outline-style":[{outline:[...pe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[xe,ae,ne]}],"outline-w":[{outline:["",xe,Gr,Ma]}],"outline-color":[{outline:H()}],shadow:[{shadow:["","none",y,vo,go]}],"shadow-color":[{shadow:H()}],"inset-shadow":[{"inset-shadow":["none",w,vo,go]}],"inset-shadow-color":[{"inset-shadow":H()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:H()}],"ring-offset-w":[{"ring-offset":[xe,Ma]}],"ring-offset-color":[{"ring-offset":H()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":H()}],"text-shadow":[{"text-shadow":["none",S,vo,go]}],"text-shadow-color":[{"text-shadow":H()}],opacity:[{opacity:[xe,ae,ne]}],"mix-blend":[{"mix-blend":[...ie(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ie()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[xe]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":H()}],"mask-image-linear-to-color":[{"mask-linear-to":H()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":H()}],"mask-image-t-to-color":[{"mask-t-to":H()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":H()}],"mask-image-r-to-color":[{"mask-r-to":H()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":H()}],"mask-image-b-to-color":[{"mask-b-to":H()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":H()}],"mask-image-l-to-color":[{"mask-l-to":H()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":H()}],"mask-image-x-to-color":[{"mask-x-to":H()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":H()}],"mask-image-y-to-color":[{"mask-y-to":H()}],"mask-image-radial":[{"mask-radial":[ae,ne]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":H()}],"mask-image-radial-to-color":[{"mask-radial-to":H()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":G()}],"mask-image-conic-pos":[{"mask-conic":[xe]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":H()}],"mask-image-conic-to-color":[{"mask-conic-to":H()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:le()}],"mask-repeat":[{mask:C()}],"mask-size":[{mask:X()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ae,ne]}],filter:[{filter:["","none",ae,ne]}],blur:[{blur:ue()}],brightness:[{brightness:[xe,ae,ne]}],contrast:[{contrast:[xe,ae,ne]}],"drop-shadow":[{"drop-shadow":["","none",R,vo,go]}],"drop-shadow-color":[{"drop-shadow":H()}],grayscale:[{grayscale:["",xe,ae,ne]}],"hue-rotate":[{"hue-rotate":[xe,ae,ne]}],invert:[{invert:["",xe,ae,ne]}],saturate:[{saturate:[xe,ae,ne]}],sepia:[{sepia:["",xe,ae,ne]}],"backdrop-filter":[{"backdrop-filter":["","none",ae,ne]}],"backdrop-blur":[{"backdrop-blur":ue()}],"backdrop-brightness":[{"backdrop-brightness":[xe,ae,ne]}],"backdrop-contrast":[{"backdrop-contrast":[xe,ae,ne]}],"backdrop-grayscale":[{"backdrop-grayscale":["",xe,ae,ne]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[xe,ae,ne]}],"backdrop-invert":[{"backdrop-invert":["",xe,ae,ne]}],"backdrop-opacity":[{"backdrop-opacity":[xe,ae,ne]}],"backdrop-saturate":[{"backdrop-saturate":[xe,ae,ne]}],"backdrop-sepia":[{"backdrop-sepia":["",xe,ae,ne]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Y()}],"border-spacing-x":[{"border-spacing-x":Y()}],"border-spacing-y":[{"border-spacing-y":Y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ae,ne]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[xe,"initial",ae,ne]}],ease:[{ease:["linear","initial",O,ae,ne]}],delay:[{delay:[xe,ae,ne]}],animate:[{animate:["none",_,ae,ne]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[E,ae,ne]}],"perspective-origin":[{"perspective-origin":V()}],rotate:[{rotate:De()}],"rotate-x":[{"rotate-x":De()}],"rotate-y":[{"rotate-y":De()}],"rotate-z":[{"rotate-z":De()}],scale:[{scale:Re()}],"scale-x":[{"scale-x":Re()}],"scale-y":[{"scale-y":Re()}],"scale-z":[{"scale-z":Re()}],"scale-3d":["scale-3d"],skew:[{skew:we()}],"skew-x":[{"skew-x":we()}],"skew-y":[{"skew-y":we()}],transform:[{transform:[ae,ne,"","none","gpu","cpu"]}],"transform-origin":[{origin:V()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ee()}],"translate-x":[{"translate-x":Ee()}],"translate-y":[{"translate-y":Ee()}],"translate-z":[{"translate-z":Ee()}],"translate-none":["translate-none"],accent:[{accent:H()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:H()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ae,ne]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Y()}],"scroll-mx":[{"scroll-mx":Y()}],"scroll-my":[{"scroll-my":Y()}],"scroll-ms":[{"scroll-ms":Y()}],"scroll-me":[{"scroll-me":Y()}],"scroll-mt":[{"scroll-mt":Y()}],"scroll-mr":[{"scroll-mr":Y()}],"scroll-mb":[{"scroll-mb":Y()}],"scroll-ml":[{"scroll-ml":Y()}],"scroll-p":[{"scroll-p":Y()}],"scroll-px":[{"scroll-px":Y()}],"scroll-py":[{"scroll-py":Y()}],"scroll-ps":[{"scroll-ps":Y()}],"scroll-pe":[{"scroll-pe":Y()}],"scroll-pt":[{"scroll-pt":Y()}],"scroll-pr":[{"scroll-pr":Y()}],"scroll-pb":[{"scroll-pb":Y()}],"scroll-pl":[{"scroll-pl":Y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ae,ne]}],fill:[{fill:["none",...H()]}],"stroke-w":[{stroke:[xe,Gr,Ma,gs]}],stroke:[{stroke:["none",...H()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},gw=FS(pw);function Ve(...a){return gw(Lg(a))}const vw=kg("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function _a({className:a,variant:r,size:u,asChild:o=!1,...s}){const f=o?jg:"button";return b.jsx(f,{"data-slot":"button",className:Ve(vw({variant:r,size:u,className:a})),...s})}const yw=()=>{const a=ia(),{theme:r,toggleTheme:u}=zl(),o=[{path:"/chat",icon:yS,label:"Chat"},{path:"/documents",icon:Fs,label:"Documents"},{path:"/models",icon:Mo,label:"Models"}];return b.jsxs("div",{className:"w-64 bg-sidebar border-r border-sidebar-border flex flex-col",children:[b.jsxs("div",{className:"p-4 border-b border-sidebar-border",children:[b.jsx("h1",{className:"text-xl font-bold text-sidebar-foreground",children:"ASCAES"}),b.jsx("p",{className:"text-sm text-sidebar-foreground/70",children:"Academic Document Specialist"})]}),b.jsx("nav",{className:"flex-1 p-4",children:b.jsx("ul",{className:"space-y-2",children:o.map(s=>{const f=s.icon,d=a.pathname===s.path;return b.jsx("li",{children:b.jsxs($s,{to:s.path,className:`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${d?"bg-sidebar-accent text-sidebar-accent-foreground":"text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"}`,children:[b.jsx(f,{size:20}),b.jsx("span",{children:s.label})]})},s.path)})})}),b.jsx("div",{className:"p-4 border-t border-sidebar-border",children:b.jsxs(_a,{variant:"ghost",size:"sm",onClick:u,className:"w-full justify-start gap-3",children:[r==="light"?b.jsx(xS,{size:20}):b.jsx(RS,{size:20}),b.jsx("span",{children:r==="light"?"Dark Mode":"Light Mode"})]})})]})};function Vr({className:a,type:r,...u}){return b.jsx("input",{type:r,"data-slot":"input",className:Ve("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...u})}function bw({className:a,...r}){return b.jsx("textarea",{"data-slot":"textarea",className:Ve("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...r})}var xw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ne=xw.reduce((a,r)=>{const u=Nl(`Primitive.${r}`),o=v.forwardRef((s,f)=>{const{asChild:d,...h}=s,p=d?u:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),b.jsx(p,{...h,ref:f})});return o.displayName=`Primitive.${r}`,{...a,[r]:o}},{});function Sw(a,r){a&&Fr.flushSync(()=>a.dispatchEvent(r))}var ww="Label",Pg=v.forwardRef((a,r)=>b.jsx(Ne.label,{...a,ref:r,onMouseDown:u=>{var s;u.target.closest("button, input, select, textarea")||((s=a.onMouseDown)==null||s.call(a,u),!u.defaultPrevented&&u.detail>1&&u.preventDefault())}}));Pg.displayName=ww;var Ew=Pg;function Xt({className:a,...r}){return b.jsx(Ew,{"data-slot":"label",className:Ve("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...r})}function kp(a,[r,u]){return Math.min(u,Math.max(r,a))}function Ce(a,r,{checkForDefaultPrevented:u=!0}={}){return function(s){if(a==null||a(s),u===!1||!s.defaultPrevented)return r==null?void 0:r(s)}}function Tw(a,r){const u=v.createContext(r),o=f=>{const{children:d,...h}=f,p=v.useMemo(()=>h,Object.values(h));return b.jsx(u.Provider,{value:p,children:d})};o.displayName=a+"Provider";function s(f){const d=v.useContext(u);if(d)return d;if(r!==void 0)return r;throw new Error(`\`${f}\` must be used within \`${a}\``)}return[o,s]}function La(a,r=[]){let u=[];function o(f,d){const h=v.createContext(d),p=u.length;u=[...u,d];const m=w=>{var O;const{scope:S,children:R,...T}=w,E=((O=S==null?void 0:S[a])==null?void 0:O[p])||h,N=v.useMemo(()=>T,Object.values(T));return b.jsx(E.Provider,{value:N,children:R})};m.displayName=f+"Provider";function y(w,S){var E;const R=((E=S==null?void 0:S[a])==null?void 0:E[p])||h,T=v.useContext(R);if(T)return T;if(d!==void 0)return d;throw new Error(`\`${w}\` must be used within \`${f}\``)}return[m,y]}const s=()=>{const f=u.map(d=>v.createContext(d));return function(h){const p=(h==null?void 0:h[a])||f;return v.useMemo(()=>({[`__scope${a}`]:{...h,[a]:p}}),[h,p])}};return s.scopeName=a,[o,Aw(s,...r)]}function Aw(...a){const r=a[0];if(a.length===1)return r;const u=()=>{const o=a.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(f){const d=o.reduce((h,{useScope:p,scopeName:m})=>{const w=p(f)[`__scope${m}`];return{...h,...w}},{});return v.useMemo(()=>({[`__scope${r.scopeName}`]:d}),[d])}};return u.scopeName=r.scopeName,u}function $g(a){const r=a+"CollectionProvider",[u,o]=La(r),[s,f]=u(r,{collectionRef:{current:null},itemMap:new Map}),d=E=>{const{scope:N,children:O}=E,_=en.useRef(null),D=en.useRef(new Map).current;return b.jsx(s,{scope:N,itemMap:D,collectionRef:_,children:O})};d.displayName=r;const h=a+"CollectionSlot",p=Nl(h),m=en.forwardRef((E,N)=>{const{scope:O,children:_}=E,D=f(h,O),G=Ge(N,D.collectionRef);return b.jsx(p,{ref:G,children:_})});m.displayName=h;const y=a+"CollectionItemSlot",w="data-radix-collection-item",S=Nl(y),R=en.forwardRef((E,N)=>{const{scope:O,children:_,...D}=E,G=en.useRef(null),V=Ge(N,G),F=f(y,O);return en.useEffect(()=>(F.itemMap.set(G,{ref:G,...D}),()=>void F.itemMap.delete(G))),b.jsx(S,{[w]:"",ref:V,children:_})});R.displayName=y;function T(E){const N=f(a+"CollectionConsumer",E);return en.useCallback(()=>{const _=N.collectionRef.current;if(!_)return[];const D=Array.from(_.querySelectorAll(`[${w}]`));return Array.from(N.itemMap.values()).sort((F,P)=>D.indexOf(F.ref.current)-D.indexOf(P.ref.current))},[N.collectionRef,N.itemMap])}return[{Provider:d,Slot:m,ItemSlot:R},T,o]}var Rw=v.createContext(void 0);function Ws(a){const r=v.useContext(Rw);return a||r||"ltr"}function aa(a){const r=v.useRef(a);return v.useEffect(()=>{r.current=a}),v.useMemo(()=>(...u)=>{var o;return(o=r.current)==null?void 0:o.call(r,...u)},[])}function Cw(a,r=globalThis==null?void 0:globalThis.document){const u=aa(a);v.useEffect(()=>{const o=s=>{s.key==="Escape"&&u(s)};return r.addEventListener("keydown",o,{capture:!0}),()=>r.removeEventListener("keydown",o,{capture:!0})},[u,r])}var Nw="DismissableLayer",Ds="dismissableLayer.update",Ow="dismissableLayer.pointerDownOutside",Mw="dismissableLayer.focusOutside",Bp,Fg=v.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Is=v.forwardRef((a,r)=>{const{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:f,onInteractOutside:d,onDismiss:h,...p}=a,m=v.useContext(Fg),[y,w]=v.useState(null),S=(y==null?void 0:y.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,R]=v.useState({}),T=Ge(r,P=>w(P)),E=Array.from(m.layers),[N]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),O=E.indexOf(N),_=y?E.indexOf(y):-1,D=m.layersWithOutsidePointerEventsDisabled.size>0,G=_>=O,V=zw(P=>{const Y=P.target,ee=[...m.branches].some(ce=>ce.contains(Y));!G||ee||(s==null||s(P),d==null||d(P),P.defaultPrevented||h==null||h())},S),F=jw(P=>{const Y=P.target;[...m.branches].some(ce=>ce.contains(Y))||(f==null||f(P),d==null||d(P),P.defaultPrevented||h==null||h())},S);return Cw(P=>{_===m.layers.size-1&&(o==null||o(P),!P.defaultPrevented&&h&&(P.preventDefault(),h()))},S),v.useEffect(()=>{if(y)return u&&(m.layersWithOutsidePointerEventsDisabled.size===0&&(Bp=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(y)),m.layers.add(y),Hp(),()=>{u&&m.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=Bp)}},[y,S,u,m]),v.useEffect(()=>()=>{y&&(m.layers.delete(y),m.layersWithOutsidePointerEventsDisabled.delete(y),Hp())},[y,m]),v.useEffect(()=>{const P=()=>R({});return document.addEventListener(Ds,P),()=>document.removeEventListener(Ds,P)},[]),b.jsx(Ne.div,{...p,ref:T,style:{pointerEvents:D?G?"auto":"none":void 0,...a.style},onFocusCapture:Ce(a.onFocusCapture,F.onFocusCapture),onBlurCapture:Ce(a.onBlurCapture,F.onBlurCapture),onPointerDownCapture:Ce(a.onPointerDownCapture,V.onPointerDownCapture)})});Is.displayName=Nw;var _w="DismissableLayerBranch",Dw=v.forwardRef((a,r)=>{const u=v.useContext(Fg),o=v.useRef(null),s=Ge(r,o);return v.useEffect(()=>{const f=o.current;if(f)return u.branches.add(f),()=>{u.branches.delete(f)}},[u.branches]),b.jsx(Ne.div,{...a,ref:s})});Dw.displayName=_w;function zw(a,r=globalThis==null?void 0:globalThis.document){const u=aa(a),o=v.useRef(!1),s=v.useRef(()=>{});return v.useEffect(()=>{const f=h=>{if(h.target&&!o.current){let p=function(){Jg(Ow,u,m,{discrete:!0})};const m={originalEvent:h};h.pointerType==="touch"?(r.removeEventListener("click",s.current),s.current=p,r.addEventListener("click",s.current,{once:!0})):p()}else r.removeEventListener("click",s.current);o.current=!1},d=window.setTimeout(()=>{r.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(d),r.removeEventListener("pointerdown",f),r.removeEventListener("click",s.current)}},[r,u]),{onPointerDownCapture:()=>o.current=!0}}function jw(a,r=globalThis==null?void 0:globalThis.document){const u=aa(a),o=v.useRef(!1);return v.useEffect(()=>{const s=f=>{f.target&&!o.current&&Jg(Mw,u,{originalEvent:f},{discrete:!1})};return r.addEventListener("focusin",s),()=>r.removeEventListener("focusin",s)},[r,u]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Hp(){const a=new CustomEvent(Ds);document.dispatchEvent(a)}function Jg(a,r,u,{discrete:o}){const s=u.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:u});r&&s.addEventListener(a,r,{once:!0}),o?Sw(s,f):s.dispatchEvent(f)}var vs=0;function Wg(){v.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??Gp()),document.body.insertAdjacentElement("beforeend",a[1]??Gp()),vs++,()=>{vs===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),vs--}},[])}function Gp(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var ys="focusScope.autoFocusOnMount",bs="focusScope.autoFocusOnUnmount",Vp={bubbles:!1,cancelable:!0},Uw="FocusScope",ef=v.forwardRef((a,r)=>{const{loop:u=!1,trapped:o=!1,onMountAutoFocus:s,onUnmountAutoFocus:f,...d}=a,[h,p]=v.useState(null),m=aa(s),y=aa(f),w=v.useRef(null),S=Ge(r,E=>p(E)),R=v.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;v.useEffect(()=>{if(o){let E=function(D){if(R.paused||!h)return;const G=D.target;h.contains(G)?w.current=G:ta(w.current,{select:!0})},N=function(D){if(R.paused||!h)return;const G=D.relatedTarget;G!==null&&(h.contains(G)||ta(w.current,{select:!0}))},O=function(D){if(document.activeElement===document.body)for(const V of D)V.removedNodes.length>0&&ta(h)};document.addEventListener("focusin",E),document.addEventListener("focusout",N);const _=new MutationObserver(O);return h&&_.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",N),_.disconnect()}}},[o,h,R.paused]),v.useEffect(()=>{if(h){Yp.add(R);const E=document.activeElement;if(!h.contains(E)){const O=new CustomEvent(ys,Vp);h.addEventListener(ys,m),h.dispatchEvent(O),O.defaultPrevented||(Lw(Vw(Ig(h)),{select:!0}),document.activeElement===E&&ta(h))}return()=>{h.removeEventListener(ys,m),setTimeout(()=>{const O=new CustomEvent(bs,Vp);h.addEventListener(bs,y),h.dispatchEvent(O),O.defaultPrevented||ta(E??document.body,{select:!0}),h.removeEventListener(bs,y),Yp.remove(R)},0)}}},[h,m,y,R]);const T=v.useCallback(E=>{if(!u&&!o||R.paused)return;const N=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,O=document.activeElement;if(N&&O){const _=E.currentTarget,[D,G]=kw(_);D&&G?!E.shiftKey&&O===G?(E.preventDefault(),u&&ta(D,{select:!0})):E.shiftKey&&O===D&&(E.preventDefault(),u&&ta(G,{select:!0})):O===_&&E.preventDefault()}},[u,o,R.paused]);return b.jsx(Ne.div,{tabIndex:-1,...d,ref:S,onKeyDown:T})});ef.displayName=Uw;function Lw(a,{select:r=!1}={}){const u=document.activeElement;for(const o of a)if(ta(o,{select:r}),document.activeElement!==u)return}function kw(a){const r=Ig(a),u=qp(r,a),o=qp(r.reverse(),a);return[u,o]}function Ig(a){const r=[],u=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const s=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||s?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;u.nextNode();)r.push(u.currentNode);return r}function qp(a,r){for(const u of a)if(!Bw(u,{upTo:r}))return u}function Bw(a,{upTo:r}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(r!==void 0&&a===r)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function Hw(a){return a instanceof HTMLInputElement&&"select"in a}function ta(a,{select:r=!1}={}){if(a&&a.focus){const u=document.activeElement;a.focus({preventScroll:!0}),a!==u&&Hw(a)&&r&&a.select()}}var Yp=Gw();function Gw(){let a=[];return{add(r){const u=a[0];r!==u&&(u==null||u.pause()),a=Xp(a,r),a.unshift(r)},remove(r){var u;a=Xp(a,r),(u=a[0])==null||u.resume()}}}function Xp(a,r){const u=[...a],o=u.indexOf(r);return o!==-1&&u.splice(o,1),u}function Vw(a){return a.filter(r=>r.tagName!=="A")}var dt=globalThis!=null&&globalThis.document?v.useLayoutEffect:()=>{},qw=hg[" useId ".trim().toString()]||(()=>{}),Yw=0;function na(a){const[r,u]=v.useState(qw());return dt(()=>{u(o=>o??String(Yw++))},[a]),a||(r?`radix-${r}`:"")}const Xw=["top","right","bottom","left"],la=Math.min,Mt=Math.max,_o=Math.round,yo=Math.floor,nn=a=>({x:a,y:a}),Qw={left:"right",right:"left",bottom:"top",top:"bottom"},Zw={start:"end",end:"start"};function zs(a,r,u){return Mt(a,la(r,u))}function Cn(a,r){return typeof a=="function"?a(r):a}function Nn(a){return a.split("-")[0]}function Ll(a){return a.split("-")[1]}function tf(a){return a==="x"?"y":"x"}function nf(a){return a==="y"?"height":"width"}function An(a){return["top","bottom"].includes(Nn(a))?"y":"x"}function af(a){return tf(An(a))}function Kw(a,r,u){u===void 0&&(u=!1);const o=Ll(a),s=af(a),f=nf(s);let d=s==="x"?o===(u?"end":"start")?"right":"left":o==="start"?"bottom":"top";return r.reference[f]>r.floating[f]&&(d=Do(d)),[d,Do(d)]}function Pw(a){const r=Do(a);return[js(a),r,js(r)]}function js(a){return a.replace(/start|end/g,r=>Zw[r])}function $w(a,r,u){const o=["left","right"],s=["right","left"],f=["top","bottom"],d=["bottom","top"];switch(a){case"top":case"bottom":return u?r?s:o:r?o:s;case"left":case"right":return r?f:d;default:return[]}}function Fw(a,r,u,o){const s=Ll(a);let f=$w(Nn(a),u==="start",o);return s&&(f=f.map(d=>d+"-"+s),r&&(f=f.concat(f.map(js)))),f}function Do(a){return a.replace(/left|right|bottom|top/g,r=>Qw[r])}function Jw(a){return{top:0,right:0,bottom:0,left:0,...a}}function ev(a){return typeof a!="number"?Jw(a):{top:a,right:a,bottom:a,left:a}}function zo(a){const{x:r,y:u,width:o,height:s}=a;return{width:o,height:s,top:u,left:r,right:r+o,bottom:u+s,x:r,y:u}}function Qp(a,r,u){let{reference:o,floating:s}=a;const f=An(r),d=af(r),h=nf(d),p=Nn(r),m=f==="y",y=o.x+o.width/2-s.width/2,w=o.y+o.height/2-s.height/2,S=o[h]/2-s[h]/2;let R;switch(p){case"top":R={x:y,y:o.y-s.height};break;case"bottom":R={x:y,y:o.y+o.height};break;case"right":R={x:o.x+o.width,y:w};break;case"left":R={x:o.x-s.width,y:w};break;default:R={x:o.x,y:o.y}}switch(Ll(r)){case"start":R[d]-=S*(u&&m?-1:1);break;case"end":R[d]+=S*(u&&m?-1:1);break}return R}const Ww=async(a,r,u)=>{const{placement:o="bottom",strategy:s="absolute",middleware:f=[],platform:d}=u,h=f.filter(Boolean),p=await(d.isRTL==null?void 0:d.isRTL(r));let m=await d.getElementRects({reference:a,floating:r,strategy:s}),{x:y,y:w}=Qp(m,o,p),S=o,R={},T=0;for(let E=0;E<h.length;E++){const{name:N,fn:O}=h[E],{x:_,y:D,data:G,reset:V}=await O({x:y,y:w,initialPlacement:o,placement:S,strategy:s,middlewareData:R,rects:m,platform:d,elements:{reference:a,floating:r}});y=_??y,w=D??w,R={...R,[N]:{...R[N],...G}},V&&T<=50&&(T++,typeof V=="object"&&(V.placement&&(S=V.placement),V.rects&&(m=V.rects===!0?await d.getElementRects({reference:a,floating:r,strategy:s}):V.rects),{x:y,y:w}=Qp(m,S,p)),E=-1)}return{x:y,y:w,placement:S,strategy:s,middlewareData:R}};async function Qr(a,r){var u;r===void 0&&(r={});const{x:o,y:s,platform:f,rects:d,elements:h,strategy:p}=a,{boundary:m="clippingAncestors",rootBoundary:y="viewport",elementContext:w="floating",altBoundary:S=!1,padding:R=0}=Cn(r,a),T=ev(R),N=h[S?w==="floating"?"reference":"floating":w],O=zo(await f.getClippingRect({element:(u=await(f.isElement==null?void 0:f.isElement(N)))==null||u?N:N.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:m,rootBoundary:y,strategy:p})),_=w==="floating"?{x:o,y:s,width:d.floating.width,height:d.floating.height}:d.reference,D=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),G=await(f.isElement==null?void 0:f.isElement(D))?await(f.getScale==null?void 0:f.getScale(D))||{x:1,y:1}:{x:1,y:1},V=zo(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:_,offsetParent:D,strategy:p}):_);return{top:(O.top-V.top+T.top)/G.y,bottom:(V.bottom-O.bottom+T.bottom)/G.y,left:(O.left-V.left+T.left)/G.x,right:(V.right-O.right+T.right)/G.x}}const Iw=a=>({name:"arrow",options:a,async fn(r){const{x:u,y:o,placement:s,rects:f,platform:d,elements:h,middlewareData:p}=r,{element:m,padding:y=0}=Cn(a,r)||{};if(m==null)return{};const w=ev(y),S={x:u,y:o},R=af(s),T=nf(R),E=await d.getDimensions(m),N=R==="y",O=N?"top":"left",_=N?"bottom":"right",D=N?"clientHeight":"clientWidth",G=f.reference[T]+f.reference[R]-S[R]-f.floating[T],V=S[R]-f.reference[R],F=await(d.getOffsetParent==null?void 0:d.getOffsetParent(m));let P=F?F[D]:0;(!P||!await(d.isElement==null?void 0:d.isElement(F)))&&(P=h.floating[D]||f.floating[T]);const Y=G/2-V/2,ee=P/2-E[T]/2-1,ce=la(w[O],ee),he=la(w[_],ee),de=ce,ge=P-E[T]-he,ye=P/2-E[T]/2+Y,oe=zs(de,ye,ge),j=!p.arrow&&Ll(s)!=null&&ye!==oe&&f.reference[T]/2-(ye<de?ce:he)-E[T]/2<0,K=j?ye<de?ye-de:ye-ge:0;return{[R]:S[R]+K,data:{[R]:oe,centerOffset:ye-oe-K,...j&&{alignmentOffset:K}},reset:j}}}),eE=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(r){var u,o;const{placement:s,middlewareData:f,rects:d,initialPlacement:h,platform:p,elements:m}=r,{mainAxis:y=!0,crossAxis:w=!0,fallbackPlacements:S,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:E=!0,...N}=Cn(a,r);if((u=f.arrow)!=null&&u.alignmentOffset)return{};const O=Nn(s),_=An(h),D=Nn(h)===h,G=await(p.isRTL==null?void 0:p.isRTL(m.floating)),V=S||(D||!E?[Do(h)]:Pw(h)),F=T!=="none";!S&&F&&V.push(...Fw(h,E,T,G));const P=[h,...V],Y=await Qr(r,N),ee=[];let ce=((o=f.flip)==null?void 0:o.overflows)||[];if(y&&ee.push(Y[O]),w){const oe=Kw(s,d,G);ee.push(Y[oe[0]],Y[oe[1]])}if(ce=[...ce,{placement:s,overflows:ee}],!ee.every(oe=>oe<=0)){var he,de;const oe=(((he=f.flip)==null?void 0:he.index)||0)+1,j=P[oe];if(j){var ge;const H=w==="alignment"?_!==An(j):!1,le=((ge=ce[0])==null?void 0:ge.overflows[0])>0;if(!H||le)return{data:{index:oe,overflows:ce},reset:{placement:j}}}let K=(de=ce.filter(H=>H.overflows[0]<=0).sort((H,le)=>H.overflows[1]-le.overflows[1])[0])==null?void 0:de.placement;if(!K)switch(R){case"bestFit":{var ye;const H=(ye=ce.filter(le=>{if(F){const C=An(le.placement);return C===_||C==="y"}return!0}).map(le=>[le.placement,le.overflows.filter(C=>C>0).reduce((C,X)=>C+X,0)]).sort((le,C)=>le[1]-C[1])[0])==null?void 0:ye[0];H&&(K=H);break}case"initialPlacement":K=h;break}if(s!==K)return{reset:{placement:K}}}return{}}}};function Zp(a,r){return{top:a.top-r.height,right:a.right-r.width,bottom:a.bottom-r.height,left:a.left-r.width}}function Kp(a){return Xw.some(r=>a[r]>=0)}const tE=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(r){const{rects:u}=r,{strategy:o="referenceHidden",...s}=Cn(a,r);switch(o){case"referenceHidden":{const f=await Qr(r,{...s,elementContext:"reference"}),d=Zp(f,u.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:Kp(d)}}}case"escaped":{const f=await Qr(r,{...s,altBoundary:!0}),d=Zp(f,u.floating);return{data:{escapedOffsets:d,escaped:Kp(d)}}}default:return{}}}}};async function nE(a,r){const{placement:u,platform:o,elements:s}=a,f=await(o.isRTL==null?void 0:o.isRTL(s.floating)),d=Nn(u),h=Ll(u),p=An(u)==="y",m=["left","top"].includes(d)?-1:1,y=f&&p?-1:1,w=Cn(r,a);let{mainAxis:S,crossAxis:R,alignmentAxis:T}=typeof w=="number"?{mainAxis:w,crossAxis:0,alignmentAxis:null}:{mainAxis:w.mainAxis||0,crossAxis:w.crossAxis||0,alignmentAxis:w.alignmentAxis};return h&&typeof T=="number"&&(R=h==="end"?T*-1:T),p?{x:R*y,y:S*m}:{x:S*m,y:R*y}}const aE=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(r){var u,o;const{x:s,y:f,placement:d,middlewareData:h}=r,p=await nE(r,a);return d===((u=h.offset)==null?void 0:u.placement)&&(o=h.arrow)!=null&&o.alignmentOffset?{}:{x:s+p.x,y:f+p.y,data:{...p,placement:d}}}}},lE=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(r){const{x:u,y:o,placement:s}=r,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:N=>{let{x:O,y:_}=N;return{x:O,y:_}}},...p}=Cn(a,r),m={x:u,y:o},y=await Qr(r,p),w=An(Nn(s)),S=tf(w);let R=m[S],T=m[w];if(f){const N=S==="y"?"top":"left",O=S==="y"?"bottom":"right",_=R+y[N],D=R-y[O];R=zs(_,R,D)}if(d){const N=w==="y"?"top":"left",O=w==="y"?"bottom":"right",_=T+y[N],D=T-y[O];T=zs(_,T,D)}const E=h.fn({...r,[S]:R,[w]:T});return{...E,data:{x:E.x-u,y:E.y-o,enabled:{[S]:f,[w]:d}}}}}},rE=function(a){return a===void 0&&(a={}),{options:a,fn(r){const{x:u,y:o,placement:s,rects:f,middlewareData:d}=r,{offset:h=0,mainAxis:p=!0,crossAxis:m=!0}=Cn(a,r),y={x:u,y:o},w=An(s),S=tf(w);let R=y[S],T=y[w];const E=Cn(h,r),N=typeof E=="number"?{mainAxis:E,crossAxis:0}:{mainAxis:0,crossAxis:0,...E};if(p){const D=S==="y"?"height":"width",G=f.reference[S]-f.floating[D]+N.mainAxis,V=f.reference[S]+f.reference[D]-N.mainAxis;R<G?R=G:R>V&&(R=V)}if(m){var O,_;const D=S==="y"?"width":"height",G=["top","left"].includes(Nn(s)),V=f.reference[w]-f.floating[D]+(G&&((O=d.offset)==null?void 0:O[w])||0)+(G?0:N.crossAxis),F=f.reference[w]+f.reference[D]+(G?0:((_=d.offset)==null?void 0:_[w])||0)-(G?N.crossAxis:0);T<V?T=V:T>F&&(T=F)}return{[S]:R,[w]:T}}}},iE=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(r){var u,o;const{placement:s,rects:f,platform:d,elements:h}=r,{apply:p=()=>{},...m}=Cn(a,r),y=await Qr(r,m),w=Nn(s),S=Ll(s),R=An(s)==="y",{width:T,height:E}=f.floating;let N,O;w==="top"||w==="bottom"?(N=w,O=S===(await(d.isRTL==null?void 0:d.isRTL(h.floating))?"start":"end")?"left":"right"):(O=w,N=S==="end"?"top":"bottom");const _=E-y.top-y.bottom,D=T-y.left-y.right,G=la(E-y[N],_),V=la(T-y[O],D),F=!r.middlewareData.shift;let P=G,Y=V;if((u=r.middlewareData.shift)!=null&&u.enabled.x&&(Y=D),(o=r.middlewareData.shift)!=null&&o.enabled.y&&(P=_),F&&!S){const ce=Mt(y.left,0),he=Mt(y.right,0),de=Mt(y.top,0),ge=Mt(y.bottom,0);R?Y=T-2*(ce!==0||he!==0?ce+he:Mt(y.left,y.right)):P=E-2*(de!==0||ge!==0?de+ge:Mt(y.top,y.bottom))}await p({...r,availableWidth:Y,availableHeight:P});const ee=await d.getDimensions(h.floating);return T!==ee.width||E!==ee.height?{reset:{rects:!0}}:{}}}};function Ho(){return typeof window<"u"}function kl(a){return tv(a)?(a.nodeName||"").toLowerCase():"#document"}function _t(a){var r;return(a==null||(r=a.ownerDocument)==null?void 0:r.defaultView)||window}function on(a){var r;return(r=(tv(a)?a.ownerDocument:a.document)||window.document)==null?void 0:r.documentElement}function tv(a){return Ho()?a instanceof Node||a instanceof _t(a).Node:!1}function Zt(a){return Ho()?a instanceof Element||a instanceof _t(a).Element:!1}function ln(a){return Ho()?a instanceof HTMLElement||a instanceof _t(a).HTMLElement:!1}function Pp(a){return!Ho()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof _t(a).ShadowRoot}function Jr(a){const{overflow:r,overflowX:u,overflowY:o,display:s}=Kt(a);return/auto|scroll|overlay|hidden|clip/.test(r+o+u)&&!["inline","contents"].includes(s)}function oE(a){return["table","td","th"].includes(kl(a))}function Go(a){return[":popover-open",":modal"].some(r=>{try{return a.matches(r)}catch{return!1}})}function lf(a){const r=rf(),u=Zt(a)?Kt(a):a;return["transform","translate","scale","rotate","perspective"].some(o=>u[o]?u[o]!=="none":!1)||(u.containerType?u.containerType!=="normal":!1)||!r&&(u.backdropFilter?u.backdropFilter!=="none":!1)||!r&&(u.filter?u.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(u.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(u.contain||"").includes(o))}function uE(a){let r=ra(a);for(;ln(r)&&!Ol(r);){if(lf(r))return r;if(Go(r))return null;r=ra(r)}return null}function rf(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ol(a){return["html","body","#document"].includes(kl(a))}function Kt(a){return _t(a).getComputedStyle(a)}function Vo(a){return Zt(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ra(a){if(kl(a)==="html")return a;const r=a.assignedSlot||a.parentNode||Pp(a)&&a.host||on(a);return Pp(r)?r.host:r}function nv(a){const r=ra(a);return Ol(r)?a.ownerDocument?a.ownerDocument.body:a.body:ln(r)&&Jr(r)?r:nv(r)}function Zr(a,r,u){var o;r===void 0&&(r=[]),u===void 0&&(u=!0);const s=nv(a),f=s===((o=a.ownerDocument)==null?void 0:o.body),d=_t(s);if(f){const h=Us(d);return r.concat(d,d.visualViewport||[],Jr(s)?s:[],h&&u?Zr(h):[])}return r.concat(s,Zr(s,[],u))}function Us(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function av(a){const r=Kt(a);let u=parseFloat(r.width)||0,o=parseFloat(r.height)||0;const s=ln(a),f=s?a.offsetWidth:u,d=s?a.offsetHeight:o,h=_o(u)!==f||_o(o)!==d;return h&&(u=f,o=d),{width:u,height:o,$:h}}function of(a){return Zt(a)?a:a.contextElement}function Rl(a){const r=of(a);if(!ln(r))return nn(1);const u=r.getBoundingClientRect(),{width:o,height:s,$:f}=av(r);let d=(f?_o(u.width):u.width)/o,h=(f?_o(u.height):u.height)/s;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const cE=nn(0);function lv(a){const r=_t(a);return!rf()||!r.visualViewport?cE:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function sE(a,r,u){return r===void 0&&(r=!1),!u||r&&u!==_t(a)?!1:r}function Da(a,r,u,o){r===void 0&&(r=!1),u===void 0&&(u=!1);const s=a.getBoundingClientRect(),f=of(a);let d=nn(1);r&&(o?Zt(o)&&(d=Rl(o)):d=Rl(a));const h=sE(f,u,o)?lv(f):nn(0);let p=(s.left+h.x)/d.x,m=(s.top+h.y)/d.y,y=s.width/d.x,w=s.height/d.y;if(f){const S=_t(f),R=o&&Zt(o)?_t(o):o;let T=S,E=Us(T);for(;E&&o&&R!==T;){const N=Rl(E),O=E.getBoundingClientRect(),_=Kt(E),D=O.left+(E.clientLeft+parseFloat(_.paddingLeft))*N.x,G=O.top+(E.clientTop+parseFloat(_.paddingTop))*N.y;p*=N.x,m*=N.y,y*=N.x,w*=N.y,p+=D,m+=G,T=_t(E),E=Us(T)}}return zo({width:y,height:w,x:p,y:m})}function uf(a,r){const u=Vo(a).scrollLeft;return r?r.left+u:Da(on(a)).left+u}function rv(a,r,u){u===void 0&&(u=!1);const o=a.getBoundingClientRect(),s=o.left+r.scrollLeft-(u?0:uf(a,o)),f=o.top+r.scrollTop;return{x:s,y:f}}function fE(a){let{elements:r,rect:u,offsetParent:o,strategy:s}=a;const f=s==="fixed",d=on(o),h=r?Go(r.floating):!1;if(o===d||h&&f)return u;let p={scrollLeft:0,scrollTop:0},m=nn(1);const y=nn(0),w=ln(o);if((w||!w&&!f)&&((kl(o)!=="body"||Jr(d))&&(p=Vo(o)),ln(o))){const R=Da(o);m=Rl(o),y.x=R.x+o.clientLeft,y.y=R.y+o.clientTop}const S=d&&!w&&!f?rv(d,p,!0):nn(0);return{width:u.width*m.x,height:u.height*m.y,x:u.x*m.x-p.scrollLeft*m.x+y.x+S.x,y:u.y*m.y-p.scrollTop*m.y+y.y+S.y}}function dE(a){return Array.from(a.getClientRects())}function mE(a){const r=on(a),u=Vo(a),o=a.ownerDocument.body,s=Mt(r.scrollWidth,r.clientWidth,o.scrollWidth,o.clientWidth),f=Mt(r.scrollHeight,r.clientHeight,o.scrollHeight,o.clientHeight);let d=-u.scrollLeft+uf(a);const h=-u.scrollTop;return Kt(o).direction==="rtl"&&(d+=Mt(r.clientWidth,o.clientWidth)-s),{width:s,height:f,x:d,y:h}}function hE(a,r){const u=_t(a),o=on(a),s=u.visualViewport;let f=o.clientWidth,d=o.clientHeight,h=0,p=0;if(s){f=s.width,d=s.height;const m=rf();(!m||m&&r==="fixed")&&(h=s.offsetLeft,p=s.offsetTop)}return{width:f,height:d,x:h,y:p}}function pE(a,r){const u=Da(a,!0,r==="fixed"),o=u.top+a.clientTop,s=u.left+a.clientLeft,f=ln(a)?Rl(a):nn(1),d=a.clientWidth*f.x,h=a.clientHeight*f.y,p=s*f.x,m=o*f.y;return{width:d,height:h,x:p,y:m}}function $p(a,r,u){let o;if(r==="viewport")o=hE(a,u);else if(r==="document")o=mE(on(a));else if(Zt(r))o=pE(r,u);else{const s=lv(a);o={x:r.x-s.x,y:r.y-s.y,width:r.width,height:r.height}}return zo(o)}function iv(a,r){const u=ra(a);return u===r||!Zt(u)||Ol(u)?!1:Kt(u).position==="fixed"||iv(u,r)}function gE(a,r){const u=r.get(a);if(u)return u;let o=Zr(a,[],!1).filter(h=>Zt(h)&&kl(h)!=="body"),s=null;const f=Kt(a).position==="fixed";let d=f?ra(a):a;for(;Zt(d)&&!Ol(d);){const h=Kt(d),p=lf(d);!p&&h.position==="fixed"&&(s=null),(f?!p&&!s:!p&&h.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||Jr(d)&&!p&&iv(a,d))?o=o.filter(y=>y!==d):s=h,d=ra(d)}return r.set(a,o),o}function vE(a){let{element:r,boundary:u,rootBoundary:o,strategy:s}=a;const d=[...u==="clippingAncestors"?Go(r)?[]:gE(r,this._c):[].concat(u),o],h=d[0],p=d.reduce((m,y)=>{const w=$p(r,y,s);return m.top=Mt(w.top,m.top),m.right=la(w.right,m.right),m.bottom=la(w.bottom,m.bottom),m.left=Mt(w.left,m.left),m},$p(r,h,s));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function yE(a){const{width:r,height:u}=av(a);return{width:r,height:u}}function bE(a,r,u){const o=ln(r),s=on(r),f=u==="fixed",d=Da(a,!0,f,r);let h={scrollLeft:0,scrollTop:0};const p=nn(0);function m(){p.x=uf(s)}if(o||!o&&!f)if((kl(r)!=="body"||Jr(s))&&(h=Vo(r)),o){const R=Da(r,!0,f,r);p.x=R.x+r.clientLeft,p.y=R.y+r.clientTop}else s&&m();f&&!o&&s&&m();const y=s&&!o&&!f?rv(s,h):nn(0),w=d.left+h.scrollLeft-p.x-y.x,S=d.top+h.scrollTop-p.y-y.y;return{x:w,y:S,width:d.width,height:d.height}}function xs(a){return Kt(a).position==="static"}function Fp(a,r){if(!ln(a)||Kt(a).position==="fixed")return null;if(r)return r(a);let u=a.offsetParent;return on(a)===u&&(u=u.ownerDocument.body),u}function ov(a,r){const u=_t(a);if(Go(a))return u;if(!ln(a)){let s=ra(a);for(;s&&!Ol(s);){if(Zt(s)&&!xs(s))return s;s=ra(s)}return u}let o=Fp(a,r);for(;o&&oE(o)&&xs(o);)o=Fp(o,r);return o&&Ol(o)&&xs(o)&&!lf(o)?u:o||uE(a)||u}const xE=async function(a){const r=this.getOffsetParent||ov,u=this.getDimensions,o=await u(a.floating);return{reference:bE(a.reference,await r(a.floating),a.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function SE(a){return Kt(a).direction==="rtl"}const wE={convertOffsetParentRelativeRectToViewportRelativeRect:fE,getDocumentElement:on,getClippingRect:vE,getOffsetParent:ov,getElementRects:xE,getClientRects:dE,getDimensions:yE,getScale:Rl,isElement:Zt,isRTL:SE};function uv(a,r){return a.x===r.x&&a.y===r.y&&a.width===r.width&&a.height===r.height}function EE(a,r){let u=null,o;const s=on(a);function f(){var h;clearTimeout(o),(h=u)==null||h.disconnect(),u=null}function d(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),f();const m=a.getBoundingClientRect(),{left:y,top:w,width:S,height:R}=m;if(h||r(),!S||!R)return;const T=yo(w),E=yo(s.clientWidth-(y+S)),N=yo(s.clientHeight-(w+R)),O=yo(y),D={rootMargin:-T+"px "+-E+"px "+-N+"px "+-O+"px",threshold:Mt(0,la(1,p))||1};let G=!0;function V(F){const P=F[0].intersectionRatio;if(P!==p){if(!G)return d();P?d(!1,P):o=setTimeout(()=>{d(!1,1e-7)},1e3)}P===1&&!uv(m,a.getBoundingClientRect())&&d(),G=!1}try{u=new IntersectionObserver(V,{...D,root:s.ownerDocument})}catch{u=new IntersectionObserver(V,D)}u.observe(a)}return d(!0),f}function TE(a,r,u,o){o===void 0&&(o={});const{ancestorScroll:s=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=o,m=of(a),y=s||f?[...m?Zr(m):[],...Zr(r)]:[];y.forEach(O=>{s&&O.addEventListener("scroll",u,{passive:!0}),f&&O.addEventListener("resize",u)});const w=m&&h?EE(m,u):null;let S=-1,R=null;d&&(R=new ResizeObserver(O=>{let[_]=O;_&&_.target===m&&R&&(R.unobserve(r),cancelAnimationFrame(S),S=requestAnimationFrame(()=>{var D;(D=R)==null||D.observe(r)})),u()}),m&&!p&&R.observe(m),R.observe(r));let T,E=p?Da(a):null;p&&N();function N(){const O=Da(a);E&&!uv(E,O)&&u(),E=O,T=requestAnimationFrame(N)}return u(),()=>{var O;y.forEach(_=>{s&&_.removeEventListener("scroll",u),f&&_.removeEventListener("resize",u)}),w==null||w(),(O=R)==null||O.disconnect(),R=null,p&&cancelAnimationFrame(T)}}const AE=aE,RE=lE,CE=eE,NE=iE,OE=tE,Jp=Iw,ME=rE,_E=(a,r,u)=>{const o=new Map,s={platform:wE,...u},f={...s.platform,_c:o};return Ww(a,r,{...s,platform:f})};var Ro=typeof document<"u"?v.useLayoutEffect:v.useEffect;function jo(a,r){if(a===r)return!0;if(typeof a!=typeof r)return!1;if(typeof a=="function"&&a.toString()===r.toString())return!0;let u,o,s;if(a&&r&&typeof a=="object"){if(Array.isArray(a)){if(u=a.length,u!==r.length)return!1;for(o=u;o--!==0;)if(!jo(a[o],r[o]))return!1;return!0}if(s=Object.keys(a),u=s.length,u!==Object.keys(r).length)return!1;for(o=u;o--!==0;)if(!{}.hasOwnProperty.call(r,s[o]))return!1;for(o=u;o--!==0;){const f=s[o];if(!(f==="_owner"&&a.$$typeof)&&!jo(a[f],r[f]))return!1}return!0}return a!==a&&r!==r}function cv(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function Wp(a,r){const u=cv(a);return Math.round(r*u)/u}function Ss(a){const r=v.useRef(a);return Ro(()=>{r.current=a}),r}function DE(a){a===void 0&&(a={});const{placement:r="bottom",strategy:u="absolute",middleware:o=[],platform:s,elements:{reference:f,floating:d}={},transform:h=!0,whileElementsMounted:p,open:m}=a,[y,w]=v.useState({x:0,y:0,strategy:u,placement:r,middlewareData:{},isPositioned:!1}),[S,R]=v.useState(o);jo(S,o)||R(o);const[T,E]=v.useState(null),[N,O]=v.useState(null),_=v.useCallback(H=>{H!==F.current&&(F.current=H,E(H))},[]),D=v.useCallback(H=>{H!==P.current&&(P.current=H,O(H))},[]),G=f||T,V=d||N,F=v.useRef(null),P=v.useRef(null),Y=v.useRef(y),ee=p!=null,ce=Ss(p),he=Ss(s),de=Ss(m),ge=v.useCallback(()=>{if(!F.current||!P.current)return;const H={placement:r,strategy:u,middleware:S};he.current&&(H.platform=he.current),_E(F.current,P.current,H).then(le=>{const C={...le,isPositioned:de.current!==!1};ye.current&&!jo(Y.current,C)&&(Y.current=C,Fr.flushSync(()=>{w(C)}))})},[S,r,u,he,de]);Ro(()=>{m===!1&&Y.current.isPositioned&&(Y.current.isPositioned=!1,w(H=>({...H,isPositioned:!1})))},[m]);const ye=v.useRef(!1);Ro(()=>(ye.current=!0,()=>{ye.current=!1}),[]),Ro(()=>{if(G&&(F.current=G),V&&(P.current=V),G&&V){if(ce.current)return ce.current(G,V,ge);ge()}},[G,V,ge,ce,ee]);const oe=v.useMemo(()=>({reference:F,floating:P,setReference:_,setFloating:D}),[_,D]),j=v.useMemo(()=>({reference:G,floating:V}),[G,V]),K=v.useMemo(()=>{const H={position:u,left:0,top:0};if(!j.floating)return H;const le=Wp(j.floating,y.x),C=Wp(j.floating,y.y);return h?{...H,transform:"translate("+le+"px, "+C+"px)",...cv(j.floating)>=1.5&&{willChange:"transform"}}:{position:u,left:le,top:C}},[u,h,j.floating,y.x,y.y]);return v.useMemo(()=>({...y,update:ge,refs:oe,elements:j,floatingStyles:K}),[y,ge,oe,j,K])}const zE=a=>{function r(u){return{}.hasOwnProperty.call(u,"current")}return{name:"arrow",options:a,fn(u){const{element:o,padding:s}=typeof a=="function"?a(u):a;return o&&r(o)?o.current!=null?Jp({element:o.current,padding:s}).fn(u):{}:o?Jp({element:o,padding:s}).fn(u):{}}}},jE=(a,r)=>({...AE(a),options:[a,r]}),UE=(a,r)=>({...RE(a),options:[a,r]}),LE=(a,r)=>({...ME(a),options:[a,r]}),kE=(a,r)=>({...CE(a),options:[a,r]}),BE=(a,r)=>({...NE(a),options:[a,r]}),HE=(a,r)=>({...OE(a),options:[a,r]}),GE=(a,r)=>({...zE(a),options:[a,r]});var VE="Arrow",sv=v.forwardRef((a,r)=>{const{children:u,width:o=10,height:s=5,...f}=a;return b.jsx(Ne.svg,{...f,ref:r,width:o,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?u:b.jsx("polygon",{points:"0,0 30,0 15,10"})})});sv.displayName=VE;var qE=sv;function fv(a){const[r,u]=v.useState(void 0);return dt(()=>{if(a){u({width:a.offsetWidth,height:a.offsetHeight});const o=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const f=s[0];let d,h;if("borderBoxSize"in f){const p=f.borderBoxSize,m=Array.isArray(p)?p[0]:p;d=m.inlineSize,h=m.blockSize}else d=a.offsetWidth,h=a.offsetHeight;u({width:d,height:h})});return o.observe(a,{box:"border-box"}),()=>o.unobserve(a)}else u(void 0)},[a]),r}var cf="Popper",[dv,mv]=La(cf),[YE,hv]=dv(cf),pv=a=>{const{__scopePopper:r,children:u}=a,[o,s]=v.useState(null);return b.jsx(YE,{scope:r,anchor:o,onAnchorChange:s,children:u})};pv.displayName=cf;var gv="PopperAnchor",vv=v.forwardRef((a,r)=>{const{__scopePopper:u,virtualRef:o,...s}=a,f=hv(gv,u),d=v.useRef(null),h=Ge(r,d);return v.useEffect(()=>{f.onAnchorChange((o==null?void 0:o.current)||d.current)}),o?null:b.jsx(Ne.div,{...s,ref:h})});vv.displayName=gv;var sf="PopperContent",[XE,QE]=dv(sf),yv=v.forwardRef((a,r)=>{var W,ue,De,Re,we,Ee;const{__scopePopper:u,side:o="bottom",sideOffset:s=0,align:f="center",alignOffset:d=0,arrowPadding:h=0,avoidCollisions:p=!0,collisionBoundary:m=[],collisionPadding:y=0,sticky:w="partial",hideWhenDetached:S=!1,updatePositionStrategy:R="optimized",onPlaced:T,...E}=a,N=hv(sf,u),[O,_]=v.useState(null),D=Ge(r,lt=>_(lt)),[G,V]=v.useState(null),F=fv(G),P=(F==null?void 0:F.width)??0,Y=(F==null?void 0:F.height)??0,ee=o+(f!=="center"?"-"+f:""),ce=typeof y=="number"?y:{top:0,right:0,bottom:0,left:0,...y},he=Array.isArray(m)?m:[m],de=he.length>0,ge={padding:ce,boundary:he.filter(KE),altBoundary:de},{refs:ye,floatingStyles:oe,placement:j,isPositioned:K,middlewareData:H}=DE({strategy:"fixed",placement:ee,whileElementsMounted:(...lt)=>TE(...lt,{animationFrame:R==="always"}),elements:{reference:N.anchor},middleware:[jE({mainAxis:s+Y,alignmentAxis:d}),p&&UE({mainAxis:!0,crossAxis:!1,limiter:w==="partial"?LE():void 0,...ge}),p&&kE({...ge}),BE({...ge,apply:({elements:lt,rects:pt,availableWidth:ca,availableHeight:sa})=>{const{width:ut,height:$o}=pt.reference,fa=lt.floating.style;fa.setProperty("--radix-popper-available-width",`${ca}px`),fa.setProperty("--radix-popper-available-height",`${sa}px`),fa.setProperty("--radix-popper-anchor-width",`${ut}px`),fa.setProperty("--radix-popper-anchor-height",`${$o}px`)}}),G&&GE({element:G,padding:h}),PE({arrowWidth:P,arrowHeight:Y}),S&&HE({strategy:"referenceHidden",...ge})]}),[le,C]=Sv(j),X=aa(T);dt(()=>{K&&(X==null||X())},[K,X]);const J=(W=H.arrow)==null?void 0:W.x,$=(ue=H.arrow)==null?void 0:ue.y,I=((De=H.arrow)==null?void 0:De.centerOffset)!==0,[pe,ie]=v.useState();return dt(()=>{O&&ie(window.getComputedStyle(O).zIndex)},[O]),b.jsx("div",{ref:ye.setFloating,"data-radix-popper-content-wrapper":"",style:{...oe,transform:K?oe.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:pe,"--radix-popper-transform-origin":[(Re=H.transformOrigin)==null?void 0:Re.x,(we=H.transformOrigin)==null?void 0:we.y].join(" "),...((Ee=H.hide)==null?void 0:Ee.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:b.jsx(XE,{scope:u,placedSide:le,onArrowChange:V,arrowX:J,arrowY:$,shouldHideArrow:I,children:b.jsx(Ne.div,{"data-side":le,"data-align":C,...E,ref:D,style:{...E.style,animation:K?void 0:"none"}})})})});yv.displayName=sf;var bv="PopperArrow",ZE={top:"bottom",right:"left",bottom:"top",left:"right"},xv=v.forwardRef(function(r,u){const{__scopePopper:o,...s}=r,f=QE(bv,o),d=ZE[f.placedSide];return b.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:b.jsx(qE,{...s,ref:u,style:{...s.style,display:"block"}})})});xv.displayName=bv;function KE(a){return a!==null}var PE=a=>({name:"transformOrigin",options:a,fn(r){var N,O,_;const{placement:u,rects:o,middlewareData:s}=r,d=((N=s.arrow)==null?void 0:N.centerOffset)!==0,h=d?0:a.arrowWidth,p=d?0:a.arrowHeight,[m,y]=Sv(u),w={start:"0%",center:"50%",end:"100%"}[y],S=(((O=s.arrow)==null?void 0:O.x)??0)+h/2,R=(((_=s.arrow)==null?void 0:_.y)??0)+p/2;let T="",E="";return m==="bottom"?(T=d?w:`${S}px`,E=`${-p}px`):m==="top"?(T=d?w:`${S}px`,E=`${o.floating.height+p}px`):m==="right"?(T=`${-p}px`,E=d?w:`${R}px`):m==="left"&&(T=`${o.floating.width+p}px`,E=d?w:`${R}px`),{data:{x:T,y:E}}}});function Sv(a){const[r,u="center"]=a.split("-");return[r,u]}var $E=pv,FE=vv,JE=yv,WE=xv,IE="Portal",ff=v.forwardRef((a,r)=>{var h;const{container:u,...o}=a,[s,f]=v.useState(!1);dt(()=>f(!0),[]);const d=u||s&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return d?F1.createPortal(b.jsx(Ne.div,{...o,ref:r}),d):null});ff.displayName=IE;var e2=hg[" useInsertionEffect ".trim().toString()]||dt;function Ml({prop:a,defaultProp:r,onChange:u=()=>{},caller:o}){const[s,f,d]=t2({defaultProp:r,onChange:u}),h=a!==void 0,p=h?a:s;{const y=v.useRef(a!==void 0);v.useEffect(()=>{const w=y.current;w!==h&&console.warn(`${o} is changing from ${w?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),y.current=h},[h,o])}const m=v.useCallback(y=>{var w;if(h){const S=n2(y)?y(a):y;S!==a&&((w=d.current)==null||w.call(d,S))}else f(y)},[h,a,f,d]);return[p,m]}function t2({defaultProp:a,onChange:r}){const[u,o]=v.useState(a),s=v.useRef(u),f=v.useRef(r);return e2(()=>{f.current=r},[r]),v.useEffect(()=>{var d;s.current!==u&&((d=f.current)==null||d.call(f,u),s.current=u)},[u,s]),[u,o,f]}function n2(a){return typeof a=="function"}function wv(a){const r=v.useRef({value:a,previous:a});return v.useMemo(()=>(r.current.value!==a&&(r.current.previous=r.current.value,r.current.value=a),r.current.previous),[a])}var Ev=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a2="VisuallyHidden",l2=v.forwardRef((a,r)=>b.jsx(Ne.span,{...a,ref:r,style:{...Ev,...a.style}}));l2.displayName=a2;var r2=function(a){if(typeof document>"u")return null;var r=Array.isArray(a)?a[0]:a;return r.ownerDocument.body},El=new WeakMap,bo=new WeakMap,xo={},ws=0,Tv=function(a){return a&&(a.host||Tv(a.parentNode))},i2=function(a,r){return r.map(function(u){if(a.contains(u))return u;var o=Tv(u);return o&&a.contains(o)?o:(console.error("aria-hidden",u,"in not contained inside",a,". Doing nothing"),null)}).filter(function(u){return!!u})},o2=function(a,r,u,o){var s=i2(r,Array.isArray(a)?a:[a]);xo[u]||(xo[u]=new WeakMap);var f=xo[u],d=[],h=new Set,p=new Set(s),m=function(w){!w||h.has(w)||(h.add(w),m(w.parentNode))};s.forEach(m);var y=function(w){!w||p.has(w)||Array.prototype.forEach.call(w.children,function(S){if(h.has(S))y(S);else try{var R=S.getAttribute(o),T=R!==null&&R!=="false",E=(El.get(S)||0)+1,N=(f.get(S)||0)+1;El.set(S,E),f.set(S,N),d.push(S),E===1&&T&&bo.set(S,!0),N===1&&S.setAttribute(u,"true"),T||S.setAttribute(o,"true")}catch(O){console.error("aria-hidden: cannot operate on ",S,O)}})};return y(r),h.clear(),ws++,function(){d.forEach(function(w){var S=El.get(w)-1,R=f.get(w)-1;El.set(w,S),f.set(w,R),S||(bo.has(w)||w.removeAttribute(o),bo.delete(w)),R||w.removeAttribute(u)}),ws--,ws||(El=new WeakMap,El=new WeakMap,bo=new WeakMap,xo={})}},Av=function(a,r,u){u===void 0&&(u="data-aria-hidden");var o=Array.from(Array.isArray(a)?a:[a]),s=r2(a);return s?(o.push.apply(o,Array.from(s.querySelectorAll("[aria-live]"))),o2(o,s,u,"aria-hidden")):function(){return null}},tn=function(){return tn=Object.assign||function(r){for(var u,o=1,s=arguments.length;o<s;o++){u=arguments[o];for(var f in u)Object.prototype.hasOwnProperty.call(u,f)&&(r[f]=u[f])}return r},tn.apply(this,arguments)};function Rv(a,r){var u={};for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&r.indexOf(o)<0&&(u[o]=a[o]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,o=Object.getOwnPropertySymbols(a);s<o.length;s++)r.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(a,o[s])&&(u[o[s]]=a[o[s]]);return u}function u2(a,r,u){if(u||arguments.length===2)for(var o=0,s=r.length,f;o<s;o++)(f||!(o in r))&&(f||(f=Array.prototype.slice.call(r,0,o)),f[o]=r[o]);return a.concat(f||Array.prototype.slice.call(r))}var Co="right-scroll-bar-position",No="width-before-scroll-bar",c2="with-scroll-bars-hidden",s2="--removed-body-scroll-bar-size";function Es(a,r){return typeof a=="function"?a(r):a&&(a.current=r),a}function f2(a,r){var u=v.useState(function(){return{value:a,callback:r,facade:{get current(){return u.value},set current(o){var s=u.value;s!==o&&(u.value=o,u.callback(o,s))}}}})[0];return u.callback=r,u.facade}var d2=typeof window<"u"?v.useLayoutEffect:v.useEffect,Ip=new WeakMap;function m2(a,r){var u=f2(null,function(o){return a.forEach(function(s){return Es(s,o)})});return d2(function(){var o=Ip.get(u);if(o){var s=new Set(o),f=new Set(a),d=u.current;s.forEach(function(h){f.has(h)||Es(h,null)}),f.forEach(function(h){s.has(h)||Es(h,d)})}Ip.set(u,a)},[a]),u}function h2(a){return a}function p2(a,r){r===void 0&&(r=h2);var u=[],o=!1,s={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return u.length?u[u.length-1]:a},useMedium:function(f){var d=r(f,o);return u.push(d),function(){u=u.filter(function(h){return h!==d})}},assignSyncMedium:function(f){for(o=!0;u.length;){var d=u;u=[],d.forEach(f)}u={push:function(h){return f(h)},filter:function(){return u}}},assignMedium:function(f){o=!0;var d=[];if(u.length){var h=u;u=[],h.forEach(f),d=u}var p=function(){var y=d;d=[],y.forEach(f)},m=function(){return Promise.resolve().then(p)};m(),u={push:function(y){d.push(y),m()},filter:function(y){return d=d.filter(y),u}}}};return s}function g2(a){a===void 0&&(a={});var r=p2(null);return r.options=tn({async:!0,ssr:!1},a),r}var Cv=function(a){var r=a.sideCar,u=Rv(a,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=r.read();if(!o)throw new Error("Sidecar medium not found");return v.createElement(o,tn({},u))};Cv.isSideCarExport=!0;function v2(a,r){return a.useMedium(r),Cv}var Nv=g2(),Ts=function(){},qo=v.forwardRef(function(a,r){var u=v.useRef(null),o=v.useState({onScrollCapture:Ts,onWheelCapture:Ts,onTouchMoveCapture:Ts}),s=o[0],f=o[1],d=a.forwardProps,h=a.children,p=a.className,m=a.removeScrollBar,y=a.enabled,w=a.shards,S=a.sideCar,R=a.noIsolation,T=a.inert,E=a.allowPinchZoom,N=a.as,O=N===void 0?"div":N,_=a.gapMode,D=Rv(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=S,V=m2([u,r]),F=tn(tn({},D),s);return v.createElement(v.Fragment,null,y&&v.createElement(G,{sideCar:Nv,removeScrollBar:m,shards:w,noIsolation:R,inert:T,setCallbacks:f,allowPinchZoom:!!E,lockRef:u,gapMode:_}),d?v.cloneElement(v.Children.only(h),tn(tn({},F),{ref:V})):v.createElement(O,tn({},F,{className:p,ref:V}),h))});qo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};qo.classNames={fullWidth:No,zeroRight:Co};var y2=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function b2(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var r=y2();return r&&a.setAttribute("nonce",r),a}function x2(a,r){a.styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r))}function S2(a){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(a)}var w2=function(){var a=0,r=null;return{add:function(u){a==0&&(r=b2())&&(x2(r,u),S2(r)),a++},remove:function(){a--,!a&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},E2=function(){var a=w2();return function(r,u){v.useEffect(function(){return a.add(r),function(){a.remove()}},[r&&u])}},Ov=function(){var a=E2(),r=function(u){var o=u.styles,s=u.dynamic;return a(o,s),null};return r},T2={left:0,top:0,right:0,gap:0},As=function(a){return parseInt(a||"",10)||0},A2=function(a){var r=window.getComputedStyle(document.body),u=r[a==="padding"?"paddingLeft":"marginLeft"],o=r[a==="padding"?"paddingTop":"marginTop"],s=r[a==="padding"?"paddingRight":"marginRight"];return[As(u),As(o),As(s)]},R2=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return T2;var r=A2(a),u=document.documentElement.clientWidth,o=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,o-u+r[2]-r[0])}},C2=Ov(),Cl="data-scroll-locked",N2=function(a,r,u,o){var s=a.left,f=a.top,d=a.right,h=a.gap;return u===void 0&&(u="margin"),`
  .`.concat(c2,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(h,"px ").concat(o,`;
  }
  body[`).concat(Cl,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(o,";"),u==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(o,`;
    `),u==="padding"&&"padding-right: ".concat(h,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Co,` {
    right: `).concat(h,"px ").concat(o,`;
  }
  
  .`).concat(No,` {
    margin-right: `).concat(h,"px ").concat(o,`;
  }
  
  .`).concat(Co," .").concat(Co,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(No," .").concat(No,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(Cl,`] {
    `).concat(s2,": ").concat(h,`px;
  }
`)},eg=function(){var a=parseInt(document.body.getAttribute(Cl)||"0",10);return isFinite(a)?a:0},O2=function(){v.useEffect(function(){return document.body.setAttribute(Cl,(eg()+1).toString()),function(){var a=eg()-1;a<=0?document.body.removeAttribute(Cl):document.body.setAttribute(Cl,a.toString())}},[])},M2=function(a){var r=a.noRelative,u=a.noImportant,o=a.gapMode,s=o===void 0?"margin":o;O2();var f=v.useMemo(function(){return R2(s)},[s]);return v.createElement(C2,{styles:N2(f,!r,s,u?"":"!important")})},Ls=!1;if(typeof window<"u")try{var So=Object.defineProperty({},"passive",{get:function(){return Ls=!0,!0}});window.addEventListener("test",So,So),window.removeEventListener("test",So,So)}catch{Ls=!1}var Tl=Ls?{passive:!1}:!1,_2=function(a){return a.tagName==="TEXTAREA"},Mv=function(a,r){if(!(a instanceof Element))return!1;var u=window.getComputedStyle(a);return u[r]!=="hidden"&&!(u.overflowY===u.overflowX&&!_2(a)&&u[r]==="visible")},D2=function(a){return Mv(a,"overflowY")},z2=function(a){return Mv(a,"overflowX")},tg=function(a,r){var u=r.ownerDocument,o=r;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var s=_v(a,o);if(s){var f=Dv(a,o),d=f[1],h=f[2];if(d>h)return!0}o=o.parentNode}while(o&&o!==u.body);return!1},j2=function(a){var r=a.scrollTop,u=a.scrollHeight,o=a.clientHeight;return[r,u,o]},U2=function(a){var r=a.scrollLeft,u=a.scrollWidth,o=a.clientWidth;return[r,u,o]},_v=function(a,r){return a==="v"?D2(r):z2(r)},Dv=function(a,r){return a==="v"?j2(r):U2(r)},L2=function(a,r){return a==="h"&&r==="rtl"?-1:1},k2=function(a,r,u,o,s){var f=L2(a,window.getComputedStyle(r).direction),d=f*o,h=u.target,p=r.contains(h),m=!1,y=d>0,w=0,S=0;do{var R=Dv(a,h),T=R[0],E=R[1],N=R[2],O=E-N-f*T;(T||O)&&_v(a,h)&&(w+=O,S+=T),h instanceof ShadowRoot?h=h.host:h=h.parentNode}while(!p&&h!==document.body||p&&(r.contains(h)||r===h));return(y&&Math.abs(w)<1||!y&&Math.abs(S)<1)&&(m=!0),m},wo=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},ng=function(a){return[a.deltaX,a.deltaY]},ag=function(a){return a&&"current"in a?a.current:a},B2=function(a,r){return a[0]===r[0]&&a[1]===r[1]},H2=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},G2=0,Al=[];function V2(a){var r=v.useRef([]),u=v.useRef([0,0]),o=v.useRef(),s=v.useState(G2++)[0],f=v.useState(Ov)[0],d=v.useRef(a);v.useEffect(function(){d.current=a},[a]),v.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(s));var E=u2([a.lockRef.current],(a.shards||[]).map(ag),!0).filter(Boolean);return E.forEach(function(N){return N.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),E.forEach(function(N){return N.classList.remove("allow-interactivity-".concat(s))})}}},[a.inert,a.lockRef.current,a.shards]);var h=v.useCallback(function(E,N){if("touches"in E&&E.touches.length===2||E.type==="wheel"&&E.ctrlKey)return!d.current.allowPinchZoom;var O=wo(E),_=u.current,D="deltaX"in E?E.deltaX:_[0]-O[0],G="deltaY"in E?E.deltaY:_[1]-O[1],V,F=E.target,P=Math.abs(D)>Math.abs(G)?"h":"v";if("touches"in E&&P==="h"&&F.type==="range")return!1;var Y=tg(P,F);if(!Y)return!0;if(Y?V=P:(V=P==="v"?"h":"v",Y=tg(P,F)),!Y)return!1;if(!o.current&&"changedTouches"in E&&(D||G)&&(o.current=V),!V)return!0;var ee=o.current||V;return k2(ee,N,E,ee==="h"?D:G)},[]),p=v.useCallback(function(E){var N=E;if(!(!Al.length||Al[Al.length-1]!==f)){var O="deltaY"in N?ng(N):wo(N),_=r.current.filter(function(V){return V.name===N.type&&(V.target===N.target||N.target===V.shadowParent)&&B2(V.delta,O)})[0];if(_&&_.should){N.cancelable&&N.preventDefault();return}if(!_){var D=(d.current.shards||[]).map(ag).filter(Boolean).filter(function(V){return V.contains(N.target)}),G=D.length>0?h(N,D[0]):!d.current.noIsolation;G&&N.cancelable&&N.preventDefault()}}},[]),m=v.useCallback(function(E,N,O,_){var D={name:E,delta:N,target:O,should:_,shadowParent:q2(O)};r.current.push(D),setTimeout(function(){r.current=r.current.filter(function(G){return G!==D})},1)},[]),y=v.useCallback(function(E){u.current=wo(E),o.current=void 0},[]),w=v.useCallback(function(E){m(E.type,ng(E),E.target,h(E,a.lockRef.current))},[]),S=v.useCallback(function(E){m(E.type,wo(E),E.target,h(E,a.lockRef.current))},[]);v.useEffect(function(){return Al.push(f),a.setCallbacks({onScrollCapture:w,onWheelCapture:w,onTouchMoveCapture:S}),document.addEventListener("wheel",p,Tl),document.addEventListener("touchmove",p,Tl),document.addEventListener("touchstart",y,Tl),function(){Al=Al.filter(function(E){return E!==f}),document.removeEventListener("wheel",p,Tl),document.removeEventListener("touchmove",p,Tl),document.removeEventListener("touchstart",y,Tl)}},[]);var R=a.removeScrollBar,T=a.inert;return v.createElement(v.Fragment,null,T?v.createElement(f,{styles:H2(s)}):null,R?v.createElement(M2,{gapMode:a.gapMode}):null)}function q2(a){for(var r=null;a!==null;)a instanceof ShadowRoot&&(r=a.host,a=a.host),a=a.parentNode;return r}const Y2=v2(Nv,V2);var df=v.forwardRef(function(a,r){return v.createElement(qo,tn({},a,{ref:r,sideCar:Y2}))});df.classNames=qo.classNames;var X2=[" ","Enter","ArrowUp","ArrowDown"],Q2=[" ","Enter"],za="Select",[Yo,Xo,Z2]=$g(za),[Bl,CA]=La(za,[Z2,mv]),Qo=mv(),[K2,oa]=Bl(za),[P2,$2]=Bl(za),zv=a=>{const{__scopeSelect:r,children:u,open:o,defaultOpen:s,onOpenChange:f,value:d,defaultValue:h,onValueChange:p,dir:m,name:y,autoComplete:w,disabled:S,required:R,form:T}=a,E=Qo(r),[N,O]=v.useState(null),[_,D]=v.useState(null),[G,V]=v.useState(!1),F=Ws(m),[P,Y]=Ml({prop:o,defaultProp:s??!1,onChange:f,caller:za}),[ee,ce]=Ml({prop:d,defaultProp:h,onChange:p,caller:za}),he=v.useRef(null),de=N?T||!!N.closest("form"):!0,[ge,ye]=v.useState(new Set),oe=Array.from(ge).map(j=>j.props.value).join(";");return b.jsx($E,{...E,children:b.jsxs(K2,{required:R,scope:r,trigger:N,onTriggerChange:O,valueNode:_,onValueNodeChange:D,valueNodeHasChildren:G,onValueNodeHasChildrenChange:V,contentId:na(),value:ee,onValueChange:ce,open:P,onOpenChange:Y,dir:F,triggerPointerDownPosRef:he,disabled:S,children:[b.jsx(Yo.Provider,{scope:r,children:b.jsx(P2,{scope:a.__scopeSelect,onNativeOptionAdd:v.useCallback(j=>{ye(K=>new Set(K).add(j))},[]),onNativeOptionRemove:v.useCallback(j=>{ye(K=>{const H=new Set(K);return H.delete(j),H})},[]),children:u})}),de?b.jsxs(ty,{"aria-hidden":!0,required:R,tabIndex:-1,name:y,autoComplete:w,value:ee,onChange:j=>ce(j.target.value),disabled:S,form:T,children:[ee===void 0?b.jsx("option",{value:""}):null,Array.from(ge)]},oe):null]})})};zv.displayName=za;var jv="SelectTrigger",Uv=v.forwardRef((a,r)=>{const{__scopeSelect:u,disabled:o=!1,...s}=a,f=Qo(u),d=oa(jv,u),h=d.disabled||o,p=Ge(r,d.onTriggerChange),m=Xo(u),y=v.useRef("touch"),[w,S,R]=ay(E=>{const N=m().filter(D=>!D.disabled),O=N.find(D=>D.value===d.value),_=ly(N,E,O);_!==void 0&&d.onValueChange(_.value)}),T=E=>{h||(d.onOpenChange(!0),R()),E&&(d.triggerPointerDownPosRef.current={x:Math.round(E.pageX),y:Math.round(E.pageY)})};return b.jsx(FE,{asChild:!0,...f,children:b.jsx(Ne.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":ny(d.value)?"":void 0,...s,ref:p,onClick:Ce(s.onClick,E=>{E.currentTarget.focus(),y.current!=="mouse"&&T(E)}),onPointerDown:Ce(s.onPointerDown,E=>{y.current=E.pointerType;const N=E.target;N.hasPointerCapture(E.pointerId)&&N.releasePointerCapture(E.pointerId),E.button===0&&E.ctrlKey===!1&&E.pointerType==="mouse"&&(T(E),E.preventDefault())}),onKeyDown:Ce(s.onKeyDown,E=>{const N=w.current!=="";!(E.ctrlKey||E.altKey||E.metaKey)&&E.key.length===1&&S(E.key),!(N&&E.key===" ")&&X2.includes(E.key)&&(T(),E.preventDefault())})})})});Uv.displayName=jv;var Lv="SelectValue",kv=v.forwardRef((a,r)=>{const{__scopeSelect:u,className:o,style:s,children:f,placeholder:d="",...h}=a,p=oa(Lv,u),{onValueNodeHasChildrenChange:m}=p,y=f!==void 0,w=Ge(r,p.onValueNodeChange);return dt(()=>{m(y)},[m,y]),b.jsx(Ne.span,{...h,ref:w,style:{pointerEvents:"none"},children:ny(p.value)?b.jsx(b.Fragment,{children:d}):f})});kv.displayName=Lv;var F2="SelectIcon",Bv=v.forwardRef((a,r)=>{const{__scopeSelect:u,children:o,...s}=a;return b.jsx(Ne.span,{"aria-hidden":!0,...s,ref:r,children:o||"▼"})});Bv.displayName=F2;var J2="SelectPortal",Hv=a=>b.jsx(ff,{asChild:!0,...a});Hv.displayName=J2;var ja="SelectContent",Gv=v.forwardRef((a,r)=>{const u=oa(ja,a.__scopeSelect),[o,s]=v.useState();if(dt(()=>{s(new DocumentFragment)},[]),!u.open){const f=o;return f?Fr.createPortal(b.jsx(Vv,{scope:a.__scopeSelect,children:b.jsx(Yo.Slot,{scope:a.__scopeSelect,children:b.jsx("div",{children:a.children})})}),f):null}return b.jsx(qv,{...a,ref:r})});Gv.displayName=ja;var Qt=10,[Vv,ua]=Bl(ja),W2="SelectContentImpl",I2=Nl("SelectContent.RemoveScroll"),qv=v.forwardRef((a,r)=>{const{__scopeSelect:u,position:o="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:f,onPointerDownOutside:d,side:h,sideOffset:p,align:m,alignOffset:y,arrowPadding:w,collisionBoundary:S,collisionPadding:R,sticky:T,hideWhenDetached:E,avoidCollisions:N,...O}=a,_=oa(ja,u),[D,G]=v.useState(null),[V,F]=v.useState(null),P=Ge(r,W=>G(W)),[Y,ee]=v.useState(null),[ce,he]=v.useState(null),de=Xo(u),[ge,ye]=v.useState(!1),oe=v.useRef(!1);v.useEffect(()=>{if(D)return Av(D)},[D]),Wg();const j=v.useCallback(W=>{const[ue,...De]=de().map(Ee=>Ee.ref.current),[Re]=De.slice(-1),we=document.activeElement;for(const Ee of W)if(Ee===we||(Ee==null||Ee.scrollIntoView({block:"nearest"}),Ee===ue&&V&&(V.scrollTop=0),Ee===Re&&V&&(V.scrollTop=V.scrollHeight),Ee==null||Ee.focus(),document.activeElement!==we))return},[de,V]),K=v.useCallback(()=>j([Y,D]),[j,Y,D]);v.useEffect(()=>{ge&&K()},[ge,K]);const{onOpenChange:H,triggerPointerDownPosRef:le}=_;v.useEffect(()=>{if(D){let W={x:0,y:0};const ue=Re=>{var we,Ee;W={x:Math.abs(Math.round(Re.pageX)-(((we=le.current)==null?void 0:we.x)??0)),y:Math.abs(Math.round(Re.pageY)-(((Ee=le.current)==null?void 0:Ee.y)??0))}},De=Re=>{W.x<=10&&W.y<=10?Re.preventDefault():D.contains(Re.target)||H(!1),document.removeEventListener("pointermove",ue),le.current=null};return le.current!==null&&(document.addEventListener("pointermove",ue),document.addEventListener("pointerup",De,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ue),document.removeEventListener("pointerup",De,{capture:!0})}}},[D,H,le]),v.useEffect(()=>{const W=()=>H(!1);return window.addEventListener("blur",W),window.addEventListener("resize",W),()=>{window.removeEventListener("blur",W),window.removeEventListener("resize",W)}},[H]);const[C,X]=ay(W=>{const ue=de().filter(we=>!we.disabled),De=ue.find(we=>we.ref.current===document.activeElement),Re=ly(ue,W,De);Re&&setTimeout(()=>Re.ref.current.focus())}),J=v.useCallback((W,ue,De)=>{const Re=!oe.current&&!De;(_.value!==void 0&&_.value===ue||Re)&&(ee(W),Re&&(oe.current=!0))},[_.value]),$=v.useCallback(()=>D==null?void 0:D.focus(),[D]),I=v.useCallback((W,ue,De)=>{const Re=!oe.current&&!De;(_.value!==void 0&&_.value===ue||Re)&&he(W)},[_.value]),pe=o==="popper"?ks:Yv,ie=pe===ks?{side:h,sideOffset:p,align:m,alignOffset:y,arrowPadding:w,collisionBoundary:S,collisionPadding:R,sticky:T,hideWhenDetached:E,avoidCollisions:N}:{};return b.jsx(Vv,{scope:u,content:D,viewport:V,onViewportChange:F,itemRefCallback:J,selectedItem:Y,onItemLeave:$,itemTextRefCallback:I,focusSelectedItem:K,selectedItemText:ce,position:o,isPositioned:ge,searchRef:C,children:b.jsx(df,{as:I2,allowPinchZoom:!0,children:b.jsx(ef,{asChild:!0,trapped:_.open,onMountAutoFocus:W=>{W.preventDefault()},onUnmountAutoFocus:Ce(s,W=>{var ue;(ue=_.trigger)==null||ue.focus({preventScroll:!0}),W.preventDefault()}),children:b.jsx(Is,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:W=>W.preventDefault(),onDismiss:()=>_.onOpenChange(!1),children:b.jsx(pe,{role:"listbox",id:_.contentId,"data-state":_.open?"open":"closed",dir:_.dir,onContextMenu:W=>W.preventDefault(),...O,...ie,onPlaced:()=>ye(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...O.style},onKeyDown:Ce(O.onKeyDown,W=>{const ue=W.ctrlKey||W.altKey||W.metaKey;if(W.key==="Tab"&&W.preventDefault(),!ue&&W.key.length===1&&X(W.key),["ArrowUp","ArrowDown","Home","End"].includes(W.key)){let Re=de().filter(we=>!we.disabled).map(we=>we.ref.current);if(["ArrowUp","End"].includes(W.key)&&(Re=Re.slice().reverse()),["ArrowUp","ArrowDown"].includes(W.key)){const we=W.target,Ee=Re.indexOf(we);Re=Re.slice(Ee+1)}setTimeout(()=>j(Re)),W.preventDefault()}})})})})})})});qv.displayName=W2;var eT="SelectItemAlignedPosition",Yv=v.forwardRef((a,r)=>{const{__scopeSelect:u,onPlaced:o,...s}=a,f=oa(ja,u),d=ua(ja,u),[h,p]=v.useState(null),[m,y]=v.useState(null),w=Ge(r,P=>y(P)),S=Xo(u),R=v.useRef(!1),T=v.useRef(!0),{viewport:E,selectedItem:N,selectedItemText:O,focusSelectedItem:_}=d,D=v.useCallback(()=>{if(f.trigger&&f.valueNode&&h&&m&&E&&N&&O){const P=f.trigger.getBoundingClientRect(),Y=m.getBoundingClientRect(),ee=f.valueNode.getBoundingClientRect(),ce=O.getBoundingClientRect();if(f.dir!=="rtl"){const we=ce.left-Y.left,Ee=ee.left-we,lt=P.left-Ee,pt=P.width+lt,ca=Math.max(pt,Y.width),sa=window.innerWidth-Qt,ut=kp(Ee,[Qt,Math.max(Qt,sa-ca)]);h.style.minWidth=pt+"px",h.style.left=ut+"px"}else{const we=Y.right-ce.right,Ee=window.innerWidth-ee.right-we,lt=window.innerWidth-P.right-Ee,pt=P.width+lt,ca=Math.max(pt,Y.width),sa=window.innerWidth-Qt,ut=kp(Ee,[Qt,Math.max(Qt,sa-ca)]);h.style.minWidth=pt+"px",h.style.right=ut+"px"}const he=S(),de=window.innerHeight-Qt*2,ge=E.scrollHeight,ye=window.getComputedStyle(m),oe=parseInt(ye.borderTopWidth,10),j=parseInt(ye.paddingTop,10),K=parseInt(ye.borderBottomWidth,10),H=parseInt(ye.paddingBottom,10),le=oe+j+ge+H+K,C=Math.min(N.offsetHeight*5,le),X=window.getComputedStyle(E),J=parseInt(X.paddingTop,10),$=parseInt(X.paddingBottom,10),I=P.top+P.height/2-Qt,pe=de-I,ie=N.offsetHeight/2,W=N.offsetTop+ie,ue=oe+j+W,De=le-ue;if(ue<=I){const we=he.length>0&&N===he[he.length-1].ref.current;h.style.bottom="0px";const Ee=m.clientHeight-E.offsetTop-E.offsetHeight,lt=Math.max(pe,ie+(we?$:0)+Ee+K),pt=ue+lt;h.style.height=pt+"px"}else{const we=he.length>0&&N===he[0].ref.current;h.style.top="0px";const lt=Math.max(I,oe+E.offsetTop+(we?J:0)+ie)+De;h.style.height=lt+"px",E.scrollTop=ue-I+E.offsetTop}h.style.margin=`${Qt}px 0`,h.style.minHeight=C+"px",h.style.maxHeight=de+"px",o==null||o(),requestAnimationFrame(()=>R.current=!0)}},[S,f.trigger,f.valueNode,h,m,E,N,O,f.dir,o]);dt(()=>D(),[D]);const[G,V]=v.useState();dt(()=>{m&&V(window.getComputedStyle(m).zIndex)},[m]);const F=v.useCallback(P=>{P&&T.current===!0&&(D(),_==null||_(),T.current=!1)},[D,_]);return b.jsx(nT,{scope:u,contentWrapper:h,shouldExpandOnScrollRef:R,onScrollButtonChange:F,children:b.jsx("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:G},children:b.jsx(Ne.div,{...s,ref:w,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});Yv.displayName=eT;var tT="SelectPopperPosition",ks=v.forwardRef((a,r)=>{const{__scopeSelect:u,align:o="start",collisionPadding:s=Qt,...f}=a,d=Qo(u);return b.jsx(JE,{...d,...f,ref:r,align:o,collisionPadding:s,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ks.displayName=tT;var[nT,mf]=Bl(ja,{}),Bs="SelectViewport",Xv=v.forwardRef((a,r)=>{const{__scopeSelect:u,nonce:o,...s}=a,f=ua(Bs,u),d=mf(Bs,u),h=Ge(r,f.onViewportChange),p=v.useRef(0);return b.jsxs(b.Fragment,{children:[b.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),b.jsx(Yo.Slot,{scope:u,children:b.jsx(Ne.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:h,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:Ce(s.onScroll,m=>{const y=m.currentTarget,{contentWrapper:w,shouldExpandOnScrollRef:S}=d;if(S!=null&&S.current&&w){const R=Math.abs(p.current-y.scrollTop);if(R>0){const T=window.innerHeight-Qt*2,E=parseFloat(w.style.minHeight),N=parseFloat(w.style.height),O=Math.max(E,N);if(O<T){const _=O+R,D=Math.min(T,_),G=_-D;w.style.height=D+"px",w.style.bottom==="0px"&&(y.scrollTop=G>0?G:0,w.style.justifyContent="flex-end")}}}p.current=y.scrollTop})})})]})});Xv.displayName=Bs;var Qv="SelectGroup",[aT,lT]=Bl(Qv),rT=v.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a,s=na();return b.jsx(aT,{scope:u,id:s,children:b.jsx(Ne.div,{role:"group","aria-labelledby":s,...o,ref:r})})});rT.displayName=Qv;var Zv="SelectLabel",iT=v.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a,s=lT(Zv,u);return b.jsx(Ne.div,{id:s.id,...o,ref:r})});iT.displayName=Zv;var Uo="SelectItem",[oT,Kv]=Bl(Uo),Pv=v.forwardRef((a,r)=>{const{__scopeSelect:u,value:o,disabled:s=!1,textValue:f,...d}=a,h=oa(Uo,u),p=ua(Uo,u),m=h.value===o,[y,w]=v.useState(f??""),[S,R]=v.useState(!1),T=Ge(r,_=>{var D;return(D=p.itemRefCallback)==null?void 0:D.call(p,_,o,s)}),E=na(),N=v.useRef("touch"),O=()=>{s||(h.onValueChange(o),h.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return b.jsx(oT,{scope:u,value:o,disabled:s,textId:E,isSelected:m,onItemTextChange:v.useCallback(_=>{w(D=>D||((_==null?void 0:_.textContent)??"").trim())},[]),children:b.jsx(Yo.ItemSlot,{scope:u,value:o,disabled:s,textValue:y,children:b.jsx(Ne.div,{role:"option","aria-labelledby":E,"data-highlighted":S?"":void 0,"aria-selected":m&&S,"data-state":m?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...d,ref:T,onFocus:Ce(d.onFocus,()=>R(!0)),onBlur:Ce(d.onBlur,()=>R(!1)),onClick:Ce(d.onClick,()=>{N.current!=="mouse"&&O()}),onPointerUp:Ce(d.onPointerUp,()=>{N.current==="mouse"&&O()}),onPointerDown:Ce(d.onPointerDown,_=>{N.current=_.pointerType}),onPointerMove:Ce(d.onPointerMove,_=>{var D;N.current=_.pointerType,s?(D=p.onItemLeave)==null||D.call(p):N.current==="mouse"&&_.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Ce(d.onPointerLeave,_=>{var D;_.currentTarget===document.activeElement&&((D=p.onItemLeave)==null||D.call(p))}),onKeyDown:Ce(d.onKeyDown,_=>{var G;((G=p.searchRef)==null?void 0:G.current)!==""&&_.key===" "||(Q2.includes(_.key)&&O(),_.key===" "&&_.preventDefault())})})})})});Pv.displayName=Uo;var Yr="SelectItemText",$v=v.forwardRef((a,r)=>{const{__scopeSelect:u,className:o,style:s,...f}=a,d=oa(Yr,u),h=ua(Yr,u),p=Kv(Yr,u),m=$2(Yr,u),[y,w]=v.useState(null),S=Ge(r,O=>w(O),p.onItemTextChange,O=>{var _;return(_=h.itemTextRefCallback)==null?void 0:_.call(h,O,p.value,p.disabled)}),R=y==null?void 0:y.textContent,T=v.useMemo(()=>b.jsx("option",{value:p.value,disabled:p.disabled,children:R},p.value),[p.disabled,p.value,R]),{onNativeOptionAdd:E,onNativeOptionRemove:N}=m;return dt(()=>(E(T),()=>N(T)),[E,N,T]),b.jsxs(b.Fragment,{children:[b.jsx(Ne.span,{id:p.textId,...f,ref:S}),p.isSelected&&d.valueNode&&!d.valueNodeHasChildren?Fr.createPortal(f.children,d.valueNode):null]})});$v.displayName=Yr;var Fv="SelectItemIndicator",Jv=v.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a;return Kv(Fv,u).isSelected?b.jsx(Ne.span,{"aria-hidden":!0,...o,ref:r}):null});Jv.displayName=Fv;var Hs="SelectScrollUpButton",Wv=v.forwardRef((a,r)=>{const u=ua(Hs,a.__scopeSelect),o=mf(Hs,a.__scopeSelect),[s,f]=v.useState(!1),d=Ge(r,o.onScrollButtonChange);return dt(()=>{if(u.viewport&&u.isPositioned){let h=function(){const m=p.scrollTop>0;f(m)};const p=u.viewport;return h(),p.addEventListener("scroll",h),()=>p.removeEventListener("scroll",h)}},[u.viewport,u.isPositioned]),s?b.jsx(ey,{...a,ref:d,onAutoScroll:()=>{const{viewport:h,selectedItem:p}=u;h&&p&&(h.scrollTop=h.scrollTop-p.offsetHeight)}}):null});Wv.displayName=Hs;var Gs="SelectScrollDownButton",Iv=v.forwardRef((a,r)=>{const u=ua(Gs,a.__scopeSelect),o=mf(Gs,a.__scopeSelect),[s,f]=v.useState(!1),d=Ge(r,o.onScrollButtonChange);return dt(()=>{if(u.viewport&&u.isPositioned){let h=function(){const m=p.scrollHeight-p.clientHeight,y=Math.ceil(p.scrollTop)<m;f(y)};const p=u.viewport;return h(),p.addEventListener("scroll",h),()=>p.removeEventListener("scroll",h)}},[u.viewport,u.isPositioned]),s?b.jsx(ey,{...a,ref:d,onAutoScroll:()=>{const{viewport:h,selectedItem:p}=u;h&&p&&(h.scrollTop=h.scrollTop+p.offsetHeight)}}):null});Iv.displayName=Gs;var ey=v.forwardRef((a,r)=>{const{__scopeSelect:u,onAutoScroll:o,...s}=a,f=ua("SelectScrollButton",u),d=v.useRef(null),h=Xo(u),p=v.useCallback(()=>{d.current!==null&&(window.clearInterval(d.current),d.current=null)},[]);return v.useEffect(()=>()=>p(),[p]),dt(()=>{var y;const m=h().find(w=>w.ref.current===document.activeElement);(y=m==null?void 0:m.ref.current)==null||y.scrollIntoView({block:"nearest"})},[h]),b.jsx(Ne.div,{"aria-hidden":!0,...s,ref:r,style:{flexShrink:0,...s.style},onPointerDown:Ce(s.onPointerDown,()=>{d.current===null&&(d.current=window.setInterval(o,50))}),onPointerMove:Ce(s.onPointerMove,()=>{var m;(m=f.onItemLeave)==null||m.call(f),d.current===null&&(d.current=window.setInterval(o,50))}),onPointerLeave:Ce(s.onPointerLeave,()=>{p()})})}),uT="SelectSeparator",cT=v.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a;return b.jsx(Ne.div,{"aria-hidden":!0,...o,ref:r})});cT.displayName=uT;var Vs="SelectArrow",sT=v.forwardRef((a,r)=>{const{__scopeSelect:u,...o}=a,s=Qo(u),f=oa(Vs,u),d=ua(Vs,u);return f.open&&d.position==="popper"?b.jsx(WE,{...s,...o,ref:r}):null});sT.displayName=Vs;var fT="SelectBubbleInput",ty=v.forwardRef(({__scopeSelect:a,value:r,...u},o)=>{const s=v.useRef(null),f=Ge(o,s),d=wv(r);return v.useEffect(()=>{const h=s.current;if(!h)return;const p=window.HTMLSelectElement.prototype,y=Object.getOwnPropertyDescriptor(p,"value").set;if(d!==r&&y){const w=new Event("change",{bubbles:!0});y.call(h,r),h.dispatchEvent(w)}},[d,r]),b.jsx(Ne.select,{...u,style:{...Ev,...u.style},ref:f,defaultValue:r})});ty.displayName=fT;function ny(a){return a===""||a===void 0}function ay(a){const r=aa(a),u=v.useRef(""),o=v.useRef(0),s=v.useCallback(d=>{const h=u.current+d;r(h),function p(m){u.current=m,window.clearTimeout(o.current),m!==""&&(o.current=window.setTimeout(()=>p(""),1e3))}(h)},[r]),f=v.useCallback(()=>{u.current="",window.clearTimeout(o.current)},[]);return v.useEffect(()=>()=>window.clearTimeout(o.current),[]),[u,s,f]}function ly(a,r,u){const s=r.length>1&&Array.from(r).every(m=>m===r[0])?r[0]:r,f=u?a.indexOf(u):-1;let d=dT(a,Math.max(f,0));s.length===1&&(d=d.filter(m=>m!==u));const p=d.find(m=>m.textValue.toLowerCase().startsWith(s.toLowerCase()));return p!==u?p:void 0}function dT(a,r){return a.map((u,o)=>a[(r+o)%a.length])}var mT=zv,hT=Uv,pT=kv,gT=Bv,vT=Hv,yT=Gv,bT=Xv,xT=Pv,ST=$v,wT=Jv,ET=Wv,TT=Iv;function lg({...a}){return b.jsx(mT,{"data-slot":"select",...a})}function rg({...a}){return b.jsx(pT,{"data-slot":"select-value",...a})}function ig({className:a,size:r="default",children:u,...o}){return b.jsxs(hT,{"data-slot":"select-trigger","data-size":r,className:Ve("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...o,children:[u,b.jsx(gT,{asChild:!0,children:b.jsx(zg,{className:"size-4 opacity-50"})})]})}function og({className:a,children:r,position:u="popper",...o}){return b.jsx(vT,{children:b.jsxs(yT,{"data-slot":"select-content",className:Ve("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",u==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:u,...o,children:[b.jsx(AT,{}),b.jsx(bT,{className:Ve("p-1",u==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),b.jsx(RT,{})]})})}function ug({className:a,children:r,...u}){return b.jsxs(xT,{"data-slot":"select-item",className:Ve("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...u,children:[b.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:b.jsx(wT,{children:b.jsx(oS,{className:"size-4"})})}),b.jsx(ST,{children:r})]})}function AT({className:a,...r}){return b.jsx(ET,{"data-slot":"select-scroll-up-button",className:Ve("flex cursor-default items-center justify-center py-1",a),...r,children:b.jsx(sS,{className:"size-4"})})}function RT({className:a,...r}){return b.jsx(TT,{"data-slot":"select-scroll-down-button",className:Ve("flex cursor-default items-center justify-center py-1",a),...r,children:b.jsx(zg,{className:"size-4"})})}var Zo="Switch",[CT,NA]=La(Zo),[NT,OT]=CT(Zo),ry=v.forwardRef((a,r)=>{const{__scopeSwitch:u,name:o,checked:s,defaultChecked:f,required:d,disabled:h,value:p="on",onCheckedChange:m,form:y,...w}=a,[S,R]=v.useState(null),T=Ge(r,D=>R(D)),E=v.useRef(!1),N=S?y||!!S.closest("form"):!0,[O,_]=Ml({prop:s,defaultProp:f??!1,onChange:m,caller:Zo});return b.jsxs(NT,{scope:u,checked:O,disabled:h,children:[b.jsx(Ne.button,{type:"button",role:"switch","aria-checked":O,"aria-required":d,"data-state":cy(O),"data-disabled":h?"":void 0,disabled:h,value:p,...w,ref:T,onClick:Ce(a.onClick,D=>{_(G=>!G),N&&(E.current=D.isPropagationStopped(),E.current||D.stopPropagation())})}),N&&b.jsx(uy,{control:S,bubbles:!E.current,name:o,value:p,checked:O,required:d,disabled:h,form:y,style:{transform:"translateX(-100%)"}})]})});ry.displayName=Zo;var iy="SwitchThumb",oy=v.forwardRef((a,r)=>{const{__scopeSwitch:u,...o}=a,s=OT(iy,u);return b.jsx(Ne.span,{"data-state":cy(s.checked),"data-disabled":s.disabled?"":void 0,...o,ref:r})});oy.displayName=iy;var MT="SwitchBubbleInput",uy=v.forwardRef(({__scopeSwitch:a,control:r,checked:u,bubbles:o=!0,...s},f)=>{const d=v.useRef(null),h=Ge(d,f),p=wv(u),m=fv(r);return v.useEffect(()=>{const y=d.current;if(!y)return;const w=window.HTMLInputElement.prototype,R=Object.getOwnPropertyDescriptor(w,"checked").set;if(p!==u&&R){const T=new Event("click",{bubbles:o});R.call(y,u),y.dispatchEvent(T)}},[p,u,o]),b.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:u,...s,tabIndex:-1,ref:h,style:{...s.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});uy.displayName=MT;function cy(a){return a?"checked":"unchecked"}var _T=ry,DT=oy;function cg({className:a,...r}){return b.jsx(_T,{"data-slot":"switch",className:Ve("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...r,children:b.jsx(DT,{"data-slot":"switch-thumb",className:Ve("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}var Rs="rovingFocusGroup.onEntryFocus",zT={bubbles:!1,cancelable:!0},Wr="RovingFocusGroup",[qs,sy,jT]=$g(Wr),[UT,fy]=La(Wr,[jT]),[LT,kT]=UT(Wr),dy=v.forwardRef((a,r)=>b.jsx(qs.Provider,{scope:a.__scopeRovingFocusGroup,children:b.jsx(qs.Slot,{scope:a.__scopeRovingFocusGroup,children:b.jsx(BT,{...a,ref:r})})}));dy.displayName=Wr;var BT=v.forwardRef((a,r)=>{const{__scopeRovingFocusGroup:u,orientation:o,loop:s=!1,dir:f,currentTabStopId:d,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:p,onEntryFocus:m,preventScrollOnEntryFocus:y=!1,...w}=a,S=v.useRef(null),R=Ge(r,S),T=Ws(f),[E,N]=Ml({prop:d,defaultProp:h??null,onChange:p,caller:Wr}),[O,_]=v.useState(!1),D=aa(m),G=sy(u),V=v.useRef(!1),[F,P]=v.useState(0);return v.useEffect(()=>{const Y=S.current;if(Y)return Y.addEventListener(Rs,D),()=>Y.removeEventListener(Rs,D)},[D]),b.jsx(LT,{scope:u,orientation:o,dir:T,loop:s,currentTabStopId:E,onItemFocus:v.useCallback(Y=>N(Y),[N]),onItemShiftTab:v.useCallback(()=>_(!0),[]),onFocusableItemAdd:v.useCallback(()=>P(Y=>Y+1),[]),onFocusableItemRemove:v.useCallback(()=>P(Y=>Y-1),[]),children:b.jsx(Ne.div,{tabIndex:O||F===0?-1:0,"data-orientation":o,...w,ref:R,style:{outline:"none",...a.style},onMouseDown:Ce(a.onMouseDown,()=>{V.current=!0}),onFocus:Ce(a.onFocus,Y=>{const ee=!V.current;if(Y.target===Y.currentTarget&&ee&&!O){const ce=new CustomEvent(Rs,zT);if(Y.currentTarget.dispatchEvent(ce),!ce.defaultPrevented){const he=G().filter(j=>j.focusable),de=he.find(j=>j.active),ge=he.find(j=>j.id===E),oe=[de,ge,...he].filter(Boolean).map(j=>j.ref.current);py(oe,y)}}V.current=!1}),onBlur:Ce(a.onBlur,()=>_(!1))})})}),my="RovingFocusGroupItem",hy=v.forwardRef((a,r)=>{const{__scopeRovingFocusGroup:u,focusable:o=!0,active:s=!1,tabStopId:f,children:d,...h}=a,p=na(),m=f||p,y=kT(my,u),w=y.currentTabStopId===m,S=sy(u),{onFocusableItemAdd:R,onFocusableItemRemove:T,currentTabStopId:E}=y;return v.useEffect(()=>{if(o)return R(),()=>T()},[o,R,T]),b.jsx(qs.ItemSlot,{scope:u,id:m,focusable:o,active:s,children:b.jsx(Ne.span,{tabIndex:w?0:-1,"data-orientation":y.orientation,...h,ref:r,onMouseDown:Ce(a.onMouseDown,N=>{o?y.onItemFocus(m):N.preventDefault()}),onFocus:Ce(a.onFocus,()=>y.onItemFocus(m)),onKeyDown:Ce(a.onKeyDown,N=>{if(N.key==="Tab"&&N.shiftKey){y.onItemShiftTab();return}if(N.target!==N.currentTarget)return;const O=VT(N,y.orientation,y.dir);if(O!==void 0){if(N.metaKey||N.ctrlKey||N.altKey||N.shiftKey)return;N.preventDefault();let D=S().filter(G=>G.focusable).map(G=>G.ref.current);if(O==="last")D.reverse();else if(O==="prev"||O==="next"){O==="prev"&&D.reverse();const G=D.indexOf(N.currentTarget);D=y.loop?qT(D,G+1):D.slice(G+1)}setTimeout(()=>py(D))}}),children:typeof d=="function"?d({isCurrentTabStop:w,hasTabStop:E!=null}):d})})});hy.displayName=my;var HT={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function GT(a,r){return r!=="rtl"?a:a==="ArrowLeft"?"ArrowRight":a==="ArrowRight"?"ArrowLeft":a}function VT(a,r,u){const o=GT(a.key,u);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return HT[o]}function py(a,r=!1){const u=document.activeElement;for(const o of a)if(o===u||(o.focus({preventScroll:r}),document.activeElement!==u))return}function qT(a,r){return a.map((u,o)=>a[(r+o)%a.length])}var YT=dy,XT=hy;function QT(a,r){return v.useReducer((u,o)=>r[u][o]??u,a)}var Ir=a=>{const{present:r,children:u}=a,o=ZT(r),s=typeof u=="function"?u({present:o.isPresent}):v.Children.only(u),f=Ge(o.ref,KT(s));return typeof u=="function"||o.isPresent?v.cloneElement(s,{ref:f}):null};Ir.displayName="Presence";function ZT(a){const[r,u]=v.useState(),o=v.useRef(null),s=v.useRef(a),f=v.useRef("none"),d=a?"mounted":"unmounted",[h,p]=QT(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return v.useEffect(()=>{const m=Eo(o.current);f.current=h==="mounted"?m:"none"},[h]),dt(()=>{const m=o.current,y=s.current;if(y!==a){const S=f.current,R=Eo(m);a?p("MOUNT"):R==="none"||(m==null?void 0:m.display)==="none"?p("UNMOUNT"):p(y&&S!==R?"ANIMATION_OUT":"UNMOUNT"),s.current=a}},[a,p]),dt(()=>{if(r){let m;const y=r.ownerDocument.defaultView??window,w=R=>{const E=Eo(o.current).includes(R.animationName);if(R.target===r&&E&&(p("ANIMATION_END"),!s.current)){const N=r.style.animationFillMode;r.style.animationFillMode="forwards",m=y.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=N)})}},S=R=>{R.target===r&&(f.current=Eo(o.current))};return r.addEventListener("animationstart",S),r.addEventListener("animationcancel",w),r.addEventListener("animationend",w),()=>{y.clearTimeout(m),r.removeEventListener("animationstart",S),r.removeEventListener("animationcancel",w),r.removeEventListener("animationend",w)}}else p("ANIMATION_END")},[r,p]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:v.useCallback(m=>{o.current=m?getComputedStyle(m):null,u(m)},[])}}function Eo(a){return(a==null?void 0:a.animationName)||"none"}function KT(a){var o,s;let r=(o=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:o.get,u=r&&"isReactWarning"in r&&r.isReactWarning;return u?a.ref:(r=(s=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:s.get,u=r&&"isReactWarning"in r&&r.isReactWarning,u?a.props.ref:a.props.ref||a.ref)}var Ko="Tabs",[PT,OA]=La(Ko,[fy]),gy=fy(),[$T,hf]=PT(Ko),vy=v.forwardRef((a,r)=>{const{__scopeTabs:u,value:o,onValueChange:s,defaultValue:f,orientation:d="horizontal",dir:h,activationMode:p="automatic",...m}=a,y=Ws(h),[w,S]=Ml({prop:o,onChange:s,defaultProp:f??"",caller:Ko});return b.jsx($T,{scope:u,baseId:na(),value:w,onValueChange:S,orientation:d,dir:y,activationMode:p,children:b.jsx(Ne.div,{dir:y,"data-orientation":d,...m,ref:r})})});vy.displayName=Ko;var yy="TabsList",by=v.forwardRef((a,r)=>{const{__scopeTabs:u,loop:o=!0,...s}=a,f=hf(yy,u),d=gy(u);return b.jsx(YT,{asChild:!0,...d,orientation:f.orientation,dir:f.dir,loop:o,children:b.jsx(Ne.div,{role:"tablist","aria-orientation":f.orientation,...s,ref:r})})});by.displayName=yy;var xy="TabsTrigger",Sy=v.forwardRef((a,r)=>{const{__scopeTabs:u,value:o,disabled:s=!1,...f}=a,d=hf(xy,u),h=gy(u),p=Ty(d.baseId,o),m=Ay(d.baseId,o),y=o===d.value;return b.jsx(XT,{asChild:!0,...h,focusable:!s,active:y,children:b.jsx(Ne.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":m,"data-state":y?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:p,...f,ref:r,onMouseDown:Ce(a.onMouseDown,w=>{!s&&w.button===0&&w.ctrlKey===!1?d.onValueChange(o):w.preventDefault()}),onKeyDown:Ce(a.onKeyDown,w=>{[" ","Enter"].includes(w.key)&&d.onValueChange(o)}),onFocus:Ce(a.onFocus,()=>{const w=d.activationMode!=="manual";!y&&!s&&w&&d.onValueChange(o)})})})});Sy.displayName=xy;var wy="TabsContent",Ey=v.forwardRef((a,r)=>{const{__scopeTabs:u,value:o,forceMount:s,children:f,...d}=a,h=hf(wy,u),p=Ty(h.baseId,o),m=Ay(h.baseId,o),y=o===h.value,w=v.useRef(y);return v.useEffect(()=>{const S=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(S)},[]),b.jsx(Ir,{present:s||y,children:({present:S})=>b.jsx(Ne.div,{"data-state":y?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":p,hidden:!S,id:m,tabIndex:0,...d,ref:r,style:{...a.style,animationDuration:w.current?"0s":void 0},children:S&&f})})});Ey.displayName=wy;function Ty(a,r){return`${a}-trigger-${r}`}function Ay(a,r){return`${a}-content-${r}`}var FT=vy,JT=by,WT=Sy,IT=Ey;function eA({className:a,...r}){return b.jsx(FT,{"data-slot":"tabs",className:Ve("flex flex-col gap-2",a),...r})}function tA({className:a,...r}){return b.jsx(JT,{"data-slot":"tabs-list",className:Ve("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...r})}function sg({className:a,...r}){return b.jsx(WT,{"data-slot":"tabs-trigger",className:Ve("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...r})}function fg({className:a,...r}){return b.jsx(IT,{"data-slot":"tabs-content",className:Ve("flex-1 outline-none",a),...r})}var Po="Dialog",[Ry,MA]=La(Po),[nA,Pt]=Ry(Po),Cy=a=>{const{__scopeDialog:r,children:u,open:o,defaultOpen:s,onOpenChange:f,modal:d=!0}=a,h=v.useRef(null),p=v.useRef(null),[m,y]=Ml({prop:o,defaultProp:s??!1,onChange:f,caller:Po});return b.jsx(nA,{scope:r,triggerRef:h,contentRef:p,contentId:na(),titleId:na(),descriptionId:na(),open:m,onOpenChange:y,onOpenToggle:v.useCallback(()=>y(w=>!w),[y]),modal:d,children:u})};Cy.displayName=Po;var Ny="DialogTrigger",Oy=v.forwardRef((a,r)=>{const{__scopeDialog:u,...o}=a,s=Pt(Ny,u),f=Ge(r,s.triggerRef);return b.jsx(Ne.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":vf(s.open),...o,ref:f,onClick:Ce(a.onClick,s.onOpenToggle)})});Oy.displayName=Ny;var pf="DialogPortal",[aA,My]=Ry(pf,{forceMount:void 0}),_y=a=>{const{__scopeDialog:r,forceMount:u,children:o,container:s}=a,f=Pt(pf,r);return b.jsx(aA,{scope:r,forceMount:u,children:v.Children.map(o,d=>b.jsx(Ir,{present:u||f.open,children:b.jsx(ff,{asChild:!0,container:s,children:d})}))})};_y.displayName=pf;var Lo="DialogOverlay",Dy=v.forwardRef((a,r)=>{const u=My(Lo,a.__scopeDialog),{forceMount:o=u.forceMount,...s}=a,f=Pt(Lo,a.__scopeDialog);return f.modal?b.jsx(Ir,{present:o||f.open,children:b.jsx(rA,{...s,ref:r})}):null});Dy.displayName=Lo;var lA=Nl("DialogOverlay.RemoveScroll"),rA=v.forwardRef((a,r)=>{const{__scopeDialog:u,...o}=a,s=Pt(Lo,u);return b.jsx(df,{as:lA,allowPinchZoom:!0,shards:[s.contentRef],children:b.jsx(Ne.div,{"data-state":vf(s.open),...o,ref:r,style:{pointerEvents:"auto",...o.style}})})}),Ua="DialogContent",zy=v.forwardRef((a,r)=>{const u=My(Ua,a.__scopeDialog),{forceMount:o=u.forceMount,...s}=a,f=Pt(Ua,a.__scopeDialog);return b.jsx(Ir,{present:o||f.open,children:f.modal?b.jsx(iA,{...s,ref:r}):b.jsx(oA,{...s,ref:r})})});zy.displayName=Ua;var iA=v.forwardRef((a,r)=>{const u=Pt(Ua,a.__scopeDialog),o=v.useRef(null),s=Ge(r,u.contentRef,o);return v.useEffect(()=>{const f=o.current;if(f)return Av(f)},[]),b.jsx(jy,{...a,ref:s,trapFocus:u.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ce(a.onCloseAutoFocus,f=>{var d;f.preventDefault(),(d=u.triggerRef.current)==null||d.focus()}),onPointerDownOutside:Ce(a.onPointerDownOutside,f=>{const d=f.detail.originalEvent,h=d.button===0&&d.ctrlKey===!0;(d.button===2||h)&&f.preventDefault()}),onFocusOutside:Ce(a.onFocusOutside,f=>f.preventDefault())})}),oA=v.forwardRef((a,r)=>{const u=Pt(Ua,a.__scopeDialog),o=v.useRef(!1),s=v.useRef(!1);return b.jsx(jy,{...a,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:f=>{var d,h;(d=a.onCloseAutoFocus)==null||d.call(a,f),f.defaultPrevented||(o.current||(h=u.triggerRef.current)==null||h.focus(),f.preventDefault()),o.current=!1,s.current=!1},onInteractOutside:f=>{var p,m;(p=a.onInteractOutside)==null||p.call(a,f),f.defaultPrevented||(o.current=!0,f.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const d=f.target;((m=u.triggerRef.current)==null?void 0:m.contains(d))&&f.preventDefault(),f.detail.originalEvent.type==="focusin"&&s.current&&f.preventDefault()}})}),jy=v.forwardRef((a,r)=>{const{__scopeDialog:u,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:f,...d}=a,h=Pt(Ua,u),p=v.useRef(null),m=Ge(r,p);return Wg(),b.jsxs(b.Fragment,{children:[b.jsx(ef,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:s,onUnmountAutoFocus:f,children:b.jsx(Is,{role:"dialog",id:h.contentId,"aria-describedby":h.descriptionId,"aria-labelledby":h.titleId,"data-state":vf(h.open),...d,ref:m,onDismiss:()=>h.onOpenChange(!1)})}),b.jsxs(b.Fragment,{children:[b.jsx(cA,{titleId:h.titleId}),b.jsx(fA,{contentRef:p,descriptionId:h.descriptionId})]})]})}),gf="DialogTitle",Uy=v.forwardRef((a,r)=>{const{__scopeDialog:u,...o}=a,s=Pt(gf,u);return b.jsx(Ne.h2,{id:s.titleId,...o,ref:r})});Uy.displayName=gf;var Ly="DialogDescription",uA=v.forwardRef((a,r)=>{const{__scopeDialog:u,...o}=a,s=Pt(Ly,u);return b.jsx(Ne.p,{id:s.descriptionId,...o,ref:r})});uA.displayName=Ly;var ky="DialogClose",By=v.forwardRef((a,r)=>{const{__scopeDialog:u,...o}=a,s=Pt(ky,u);return b.jsx(Ne.button,{type:"button",...o,ref:r,onClick:Ce(a.onClick,()=>s.onOpenChange(!1))})});By.displayName=ky;function vf(a){return a?"open":"closed"}var Hy="DialogTitleWarning",[_A,Gy]=Tw(Hy,{contentName:Ua,titleName:gf,docsSlug:"dialog"}),cA=({titleId:a})=>{const r=Gy(Hy),u=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return v.useEffect(()=>{a&&(document.getElementById(a)||console.error(u))},[u,a]),null},sA="DialogDescriptionWarning",fA=({contentRef:a,descriptionId:r})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Gy(sA).contentName}}.`;return v.useEffect(()=>{var f;const s=(f=a.current)==null?void 0:f.getAttribute("aria-describedby");r&&s&&(document.getElementById(r)||console.warn(o))},[o,a,r]),null},dA=Cy,mA=Oy,hA=_y,pA=Dy,gA=zy,vA=Uy,yA=By;function Vy({...a}){return b.jsx(dA,{"data-slot":"dialog",...a})}function qy({...a}){return b.jsx(mA,{"data-slot":"dialog-trigger",...a})}function bA({...a}){return b.jsx(hA,{"data-slot":"dialog-portal",...a})}function xA({className:a,...r}){return b.jsx(pA,{"data-slot":"dialog-overlay",className:Ve("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...r})}function Yy({className:a,children:r,...u}){return b.jsxs(bA,{"data-slot":"dialog-portal",children:[b.jsx(xA,{}),b.jsxs(gA,{"data-slot":"dialog-content",className:Ve("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...u,children:[r,b.jsxs(yA,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[b.jsx(MS,{}),b.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Xy({className:a,...r}){return b.jsx("div",{"data-slot":"dialog-header",className:Ve("flex flex-col gap-2 text-center sm:text-left",a),...r})}function Qy({className:a,...r}){return b.jsx(vA,{"data-slot":"dialog-title",className:Ve("text-lg leading-none font-semibold",a),...r})}const SA=()=>{const{generationSettings:a,updateGenerationSettings:r,generatedText:u}=zl(),[o,s]=v.useState(!1),[f,d]=v.useState({American:0,Russian:0,German:0,Japanese:0,French:0}),h=["Analytical","Instructional","Reporting","Argumentative / Persuasive","Exploratory / Reflective","Descriptive","Narrative","Schematic / Referential"],p=["MLA","APA","Chicago"],m=()=>{const y=Object.entries(f).filter(([w,S])=>S>0);if(y.length<=2&&y.reduce((S,[R,T])=>S+T,0)===100){const S={};y.forEach(([R,T])=>{S[R]=T}),r({culturalInflection:S}),s(!1)}};return b.jsx("div",{className:"w-80 bg-card border-l border-border flex flex-col",children:b.jsxs(eA,{defaultValue:"settings",className:"flex-1 flex flex-col",children:[b.jsxs(tA,{className:"grid w-full grid-cols-2 m-4 mb-0",children:[b.jsxs(sg,{value:"document",className:"flex items-center gap-2",children:[b.jsx(Fs,{size:16}),"Document"]}),b.jsxs(sg,{value:"settings",className:"flex items-center gap-2",children:[b.jsx(Mo,{size:16}),"Settings"]})]}),b.jsx(fg,{value:"document",className:"flex-1 p-4 overflow-y-auto",children:b.jsx("div",{className:"space-y-4",children:b.jsxs("div",{children:[b.jsx(Xt,{className:"text-sm font-medium",children:"Document Preview"}),b.jsx("div",{className:"mt-2 p-3 bg-muted rounded-lg text-sm max-h-96 overflow-y-auto",children:u?b.jsxs("div",{className:"whitespace-pre-wrap",children:[u.substring(0,500),"..."]}):b.jsx("div",{className:"text-muted-foreground",children:"No document generated yet"})})]})})}),b.jsx(fg,{value:"settings",className:"flex-1 p-4 overflow-y-auto",children:b.jsxs("div",{className:"space-y-6",children:[b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm font-medium",children:"Writing Style"}),b.jsxs(lg,{value:a.writingStyle,onValueChange:y=>r({writingStyle:y}),children:[b.jsx(ig,{children:b.jsx(rg,{})}),b.jsx(og,{children:h.map(y=>b.jsx(ug,{value:y,children:y},y))})]})]}),b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm font-medium",children:"Cultural Inflection"}),b.jsxs(Vy,{open:o,onOpenChange:s,children:[b.jsx(qy,{asChild:!0,children:b.jsx(_a,{variant:"outline",className:"w-full justify-start",children:"Configure Cultures"})}),b.jsxs(Yy,{children:[b.jsx(Xy,{children:b.jsx(Qy,{children:"Cultural Inflection Settings"})}),b.jsxs("div",{className:"space-y-4",children:[b.jsx("p",{className:"text-sm text-muted-foreground",children:"Select up to 2 cultures. Total must equal 100%."}),Object.entries(f).map(([y,w])=>b.jsxs("div",{className:"flex items-center gap-3",children:[b.jsx(Xt,{className:"w-20 text-sm",children:y}),b.jsx(Vr,{type:"number",min:"0",max:"100",value:w,onChange:S=>d(R=>({...R,[y]:parseInt(S.target.value)||0})),className:"w-20"}),b.jsx("span",{className:"text-sm",children:"%"})]},y)),b.jsx(_a,{onClick:m,className:"w-full",children:"Save Settings"})]})]})]})]}),b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm font-medium",children:"Pages"}),b.jsx(Vr,{type:"number",min:"1",max:"1500",value:a.pages,onChange:y=>r({pages:parseInt(y.target.value)||1})}),b.jsxs("p",{className:"text-xs text-muted-foreground",children:["Approx. ",a.pages*250," words"]})]}),b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsx(Xt,{className:"text-sm font-medium",children:"Improve realism"}),b.jsx(cg,{checked:a.humanization,onCheckedChange:y=>r({humanization:y})})]}),b.jsxs("div",{className:"space-y-4",children:[b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsx(Xt,{className:"text-sm font-medium",children:"Enable standard academic formatting"}),b.jsx(cg,{checked:a.academicFormatting.enabled,onCheckedChange:y=>r({academicFormatting:{...a.academicFormatting,enabled:y}})})]}),a.academicFormatting.enabled&&b.jsxs("div",{className:"space-y-3 pl-4 border-l-2 border-border",children:[b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm",children:"Citation Style"}),b.jsxs(lg,{value:a.academicFormatting.citationStyle,onValueChange:y=>r({academicFormatting:{...a.academicFormatting,citationStyle:y}}),children:[b.jsx(ig,{children:b.jsx(rg,{})}),b.jsx(og,{children:p.map(y=>b.jsx(ug,{value:y,children:y},y))})]})]}),b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm",children:"Student Name"}),b.jsx(Vr,{value:a.academicFormatting.studentName,onChange:y=>r({academicFormatting:{...a.academicFormatting,studentName:y.target.value}})})]}),b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm",children:"Professor Name"}),b.jsx(Vr,{value:a.academicFormatting.professorName,onChange:y=>r({academicFormatting:{...a.academicFormatting,professorName:y.target.value}})})]}),b.jsxs("div",{className:"space-y-2",children:[b.jsx(Xt,{className:"text-sm",children:"Course Info"}),b.jsx(Vr,{value:a.academicFormatting.courseInfo,onChange:y=>r({academicFormatting:{...a.academicFormatting,courseInfo:y.target.value}})})]})]})]})]})})]})})},dg=()=>{const{generationSettings:a,updateGenerationSettings:r,isGenerating:u,generatedText:o,setIsGenerating:s,setGeneratedText:f,appendGeneratedText:d}=zl(),[h,p]=v.useState(""),m=async()=>{if(a.topic.trim()){s(!0),f("");try{const w=await fetch("http://localhost:8001/api/v1/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({topic:a.topic,writing_style:a.writingStyle,cultural_inflection_config:a.culturalInflection,total_pages:a.pages,humanization:a.humanization,academic_formatting:a.academicFormatting.enabled?a.academicFormatting:null})});if(!w.ok)throw new Error("Generation failed");const S=w.body.getReader(),R=new TextDecoder;for(;;){const{done:T,value:E}=await S.read();if(T)break;const N=R.decode(E);d(N)}}catch(w){console.error("Generation error:",w),f("Error: Failed to generate document. Please check if Ollama is running and the model is available.")}finally{s(!1)}}},y=()=>{h.trim()&&(r({topic:h}),p(""),m())};return b.jsxs("div",{className:"flex h-full",children:[b.jsxs("div",{className:"flex-1 flex flex-col",children:[b.jsxs("div",{className:"p-4 border-b border-border",children:[b.jsx("h2",{className:"text-xl font-semibold",children:"Document Generation"}),b.jsx("p",{className:"text-sm text-muted-foreground",children:"Generate academic documents using AI"})]}),b.jsx("div",{className:"flex-1 p-4 overflow-y-auto",children:o?b.jsxs("div",{className:"bg-card border border-border rounded-lg p-4",children:[b.jsx("div",{className:"text-sm text-muted-foreground mb-2",children:"Generated Document:"}),b.jsx("div",{className:"whitespace-pre-wrap text-sm leading-relaxed",children:o}),u&&b.jsx("div",{className:"mt-2 text-sm text-muted-foreground animate-pulse",children:"Generating..."})]}):b.jsx("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:b.jsxs("div",{className:"text-center",children:[b.jsx("p",{className:"text-lg mb-2",children:"Welcome to ASCAES"}),b.jsx("p",{className:"text-sm",children:"Enter a topic below to generate an academic document"})]})})}),b.jsx("div",{className:"p-4 border-t border-border",children:b.jsxs("div",{className:"flex gap-2",children:[b.jsx(bw,{value:h,onChange:w=>p(w.target.value),placeholder:"Enter your document topic...",className:"flex-1 min-h-[60px] resize-none",onKeyDown:w=>{w.key==="Enter"&&!w.shiftKey&&(w.preventDefault(),y())}}),b.jsx(_a,{onClick:y,disabled:u||!h.trim(),className:"self-end",children:b.jsx(ES,{size:16})})]})})]}),b.jsx(SA,{})]})};function Zy({className:a,...r}){return b.jsx("div",{"data-slot":"card",className:Ve("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function Ky({className:a,...r}){return b.jsx("div",{"data-slot":"card-header",className:Ve("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function Py({className:a,...r}){return b.jsx("div",{"data-slot":"card-title",className:Ve("leading-none font-semibold",a),...r})}function $y({className:a,...r}){return b.jsx("div",{"data-slot":"card-content",className:Ve("px-6",a),...r})}const wA=()=>{const{documents:a,setDocuments:r,deleteDocument:u}=zl(),[o,s]=v.useState(null);v.useEffect(()=>{f()},[]);const f=async()=>{try{const p=await fetch("http://localhost:8001/api/v1/documents");if(p.ok){const m=await p.json();r(m)}}catch(p){console.error("Failed to fetch documents:",p)}},d=async p=>{try{(await fetch(`http://localhost:8001/api/v1/documents/${p}`,{method:"DELETE"})).ok&&u(p)}catch(m){console.error("Failed to delete document:",m)}},h=p=>new Date(p).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return b.jsxs("div",{className:"flex flex-col h-full",children:[b.jsxs("div",{className:"p-6 border-b border-border",children:[b.jsx("h2",{className:"text-2xl font-semibold",children:"Documents"}),b.jsx("p",{className:"text-muted-foreground",children:"Manage your generated academic documents"})]}),b.jsx("div",{className:"flex-1 p-6 overflow-y-auto",children:a.length===0?b.jsx("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:b.jsxs("div",{className:"text-center",children:[b.jsx(Fs,{size:48,className:"mx-auto mb-4 opacity-50"}),b.jsx("p",{className:"text-lg mb-2",children:"No documents yet"}),b.jsx("p",{className:"text-sm",children:"Generate your first document in the Chat view"})]})}):b.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:a.map(p=>b.jsxs(Zy,{className:"hover:shadow-md transition-shadow",children:[b.jsxs(Ky,{className:"pb-3",children:[b.jsx(Py,{className:"text-lg line-clamp-2",children:p.title}),b.jsx("p",{className:"text-sm text-muted-foreground",children:h(p.timestamp)})]}),b.jsxs($y,{className:"space-y-3",children:[b.jsxs("div",{className:"text-sm",children:[b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Style:"})," ",p.writing_style]}),b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Pages:"})," ",p.total_pages]}),b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Humanization:"})," ",p.humanization?"Yes":"No"]})]}),b.jsxs("div",{className:"flex gap-2",children:[b.jsxs(Vy,{children:[b.jsx(qy,{asChild:!0,children:b.jsxs(_a,{variant:"outline",size:"sm",className:"flex-1",onClick:()=>s(p),children:[b.jsx(pS,{size:16,className:"mr-2"}),"View"]})}),b.jsxs(Yy,{className:"max-w-4xl max-h-[80vh]",children:[b.jsx(Xy,{children:b.jsx(Qy,{children:o==null?void 0:o.title})}),b.jsx("div",{className:"overflow-y-auto max-h-[60vh] p-4 bg-muted rounded-lg",children:b.jsx("pre",{className:"whitespace-pre-wrap text-sm leading-relaxed",children:o==null?void 0:o.generated_text})})]})]}),b.jsx(_a,{variant:"destructive",size:"sm",onClick:()=>d(p.id),children:b.jsx(NS,{size:16})})]})]})]},p.id))})})]})},EA=kg("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function TA({className:a,variant:r,asChild:u=!1,...o}){const s=u?jg:"span";return b.jsx(s,{"data-slot":"badge",className:Ve(EA({variant:r}),a),...o})}const AA=()=>{const{models:a,setModels:r}=zl(),[u,o]=v.useState(!1),[s,f]=v.useState("unknown");v.useEffect(()=>{d()},[]);const d=async()=>{o(!0);try{const S=await fetch("http://localhost:8001/api/v1/models");if(S.ok){const R=await S.json();r(R.models||[]),f("connected")}else f("error")}catch(S){console.error("Failed to fetch models:",S),f("disconnected")}finally{o(!1)}},h=S=>{if(!S)return"Unknown";const R=["B","KB","MB","GB","TB"],T=Math.floor(Math.log(S)/Math.log(1024));return Math.round(S/Math.pow(1024,T)*100)/100+" "+R[T]},p=S=>S?new Date(S).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"Unknown",m=()=>{switch(s){case"connected":return"text-green-600";case"error":return"text-red-600";case"disconnected":return"text-red-600";default:return"text-gray-600"}},y=()=>{switch(s){case"connected":return b.jsx(dS,{size:20,className:"text-green-600"});case"error":return b.jsx(Np,{size:20,className:"text-red-600"});case"disconnected":return b.jsx(Np,{size:20,className:"text-red-600"});default:return b.jsx(Mo,{size:20,className:"text-gray-600"})}},w=()=>{switch(s){case"connected":return"Connected to Ollama";case"error":return"Ollama API Error";case"disconnected":return"Cannot connect to Ollama";default:return"Checking Ollama status..."}};return b.jsxs("div",{className:"flex flex-col h-full",children:[b.jsx("div",{className:"p-6 border-b border-border",children:b.jsxs("div",{className:"flex items-center justify-between",children:[b.jsxs("div",{children:[b.jsx("h2",{className:"text-2xl font-semibold",children:"Model Management"}),b.jsx("p",{className:"text-muted-foreground",children:"Manage your local Ollama models"})]}),b.jsxs(_a,{onClick:d,disabled:u,children:[b.jsx(Op,{size:16,className:u?"animate-spin":""}),"Refresh"]})]})}),b.jsxs("div",{className:"p-6 border-b border-border",children:[b.jsxs("div",{className:"flex items-center gap-3",children:[y(),b.jsx("span",{className:`font-medium ${m()}`,children:w()})]}),s==="disconnected"&&b.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Make sure Ollama is running on localhost:11434"})]}),b.jsx("div",{className:"flex-1 p-6 overflow-y-auto",children:u?b.jsx("div",{className:"flex items-center justify-center h-full",children:b.jsxs("div",{className:"text-center",children:[b.jsx(Op,{size:48,className:"mx-auto mb-4 animate-spin opacity-50"}),b.jsx("p",{className:"text-lg mb-2",children:"Loading models..."})]})}):a.length===0?b.jsx("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:b.jsxs("div",{className:"text-center",children:[b.jsx(Mo,{size:48,className:"mx-auto mb-4 opacity-50"}),b.jsx("p",{className:"text-lg mb-2",children:"No models found"}),b.jsx("p",{className:"text-sm",children:s==="connected"?"No models are installed in Ollama":"Check your Ollama connection"})]})}):b.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:a.map((S,R)=>b.jsxs(Zy,{className:"hover:shadow-md transition-shadow",children:[b.jsx(Ky,{className:"pb-3",children:b.jsxs("div",{className:"flex items-start justify-between",children:[b.jsx(Py,{className:"text-lg line-clamp-2",children:S.name||"Unknown Model"}),S.name==="gemma3n:e2b"&&b.jsx(TA,{variant:"default",className:"ml-2",children:"Active"})]})}),b.jsxs($y,{className:"space-y-3",children:[b.jsxs("div",{className:"text-sm space-y-1",children:[b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Size:"})," ",h(S.size)]}),b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Modified:"})," ",p(S.modified_at)]}),S.digest&&b.jsxs("p",{className:"text-xs text-muted-foreground",children:[b.jsx("span",{className:"font-medium",children:"Digest:"})," ",S.digest.substring(0,16),"..."]})]}),S.details&&b.jsxs("div",{className:"text-xs text-muted-foreground space-y-1",children:[S.details.family&&b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Family:"})," ",S.details.family]}),S.details.parameter_size&&b.jsxs("p",{children:[b.jsx("span",{className:"font-medium",children:"Parameters:"})," ",S.details.parameter_size]})]})]})]},R))})})]})};function RA(){const{theme:a}=zl();return v.useEffect(()=>{a==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[a]),b.jsx(G1,{children:b.jsxs("div",{className:"flex h-screen bg-background text-foreground",children:[b.jsx(yw,{}),b.jsx("main",{className:"flex-1 overflow-hidden",children:b.jsxs(g1,{children:[b.jsx(qr,{path:"/",element:b.jsx(dg,{})}),b.jsx(qr,{path:"/chat",element:b.jsx(dg,{})}),b.jsx(qr,{path:"/documents",element:b.jsx(wA,{})}),b.jsx(qr,{path:"/models",element:b.jsx(AA,{})})]})})]})})}Tx.createRoot(document.getElementById("root")).render(b.jsx(v.StrictMode,{children:b.jsx(RA,{})}));
