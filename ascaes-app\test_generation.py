#!/usr/bin/env python3
import requests
import json

def test_document_generation():
    """Test the document generation endpoint"""
    
    # Test data
    test_request = {
        "topic": "The impact of artificial intelligence on modern education",
        "writing_style": "Analytical",
        "cultural_inflection_config": {"American": 100},
        "total_pages": 1,
        "humanization": True,
        "academic_formatting": {
            "enabled": True,
            "citation_style": "MLA",
            "student_name": "Test Student",
            "professor_name": "Dr. Test Professor",
            "course_info": "CS 101 - Introduction to Computer Science"
        }
    }
    
    print("Testing ASCAES Document Generation...")
    print(f"Topic: {test_request['topic']}")
    print(f"Writing Style: {test_request['writing_style']}")
    print(f"Pages: {test_request['total_pages']}")
    print("-" * 50)
    
    try:
        # Make the request
        response = requests.post(
            "http://localhost:8001/api/v1/generate",
            json=test_request,
            stream=True,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ Generation started successfully!")
            print("Generated content:")
            print("-" * 30)
            
            # Read the streamed response
            generated_text = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    generated_text += chunk
                    print(chunk, end='', flush=True)
            
            print("\n" + "-" * 30)
            print(f"✅ Generation completed! Total length: {len(generated_text)} characters")
            return True
            
        else:
            print(f"❌ Generation failed with status code: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_document_generation()
    if success:
        print("\n🎉 ASCAES is working correctly!")
    else:
        print("\n❌ ASCAES test failed!")
