from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api_generation import router as generation_router
from app.api_documents import router as documents_router
from app.api_models import router as models_router
from app.database import init_db

app = FastAPI(
    title="ASCAES Backend API",
    description="Backend API for Academic Document Generation Specialist (ASCAES) application.",
    version="1.0.0",
)

# Initialize the database
init_db()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include API routers
app.include_router(generation_router, prefix="/api/v1", tags=["Generation"])
app.include_router(documents_router, prefix="/api/v1", tags=["Documents"])
app.include_router(models_router, prefix="/api/v1", tags=["Models"])

@app.get("/api/v1/health")
async def health_check():
    return {"status": "ok", "message": "ASCAES Backend is running!"}


