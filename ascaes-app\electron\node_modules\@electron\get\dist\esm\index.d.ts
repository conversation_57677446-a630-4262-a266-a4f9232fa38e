import { ElectronDownloadRequestOptions, ElectronPlatformArtifactDetailsWithDefaults } from './types';
export { getHostArch } from './utils';
export { initializeProxy } from './proxy';
export * from './types';
/**
 * Downloads an artifact from an Electron release and returns an absolute path
 * to the downloaded file.
 *
 * @param artifactDetails - The information required to download the artifact
 */
export declare function downloadArtifact(_artifactDetails: ElectronPlatformArtifactDetailsWithDefaults): Promise<string>;
/**
 * Downloads a specific version of Electron and returns an absolute path to a
 * ZIP file.
 *
 * @param version - The version of Electron you want to download
 */
export declare function download(version: string, options?: ElectronDownloadRequestOptions): Promise<string>;
