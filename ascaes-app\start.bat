@echo off
title ASCAES - Academic Document Generator
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ASCAES Application                        ║
echo ║              Academic Document Generator                     ║
echo ║                Using DeepSeek-R1 1.5B                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Check prerequisites
echo [1/6] Checking prerequisites...
where ollama >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Ollama not found. Please install from https://ollama.ai/
    pause & exit /b 1
)
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js not found. Please install from https://nodejs.org/
    pause & exit /b 1
)
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python not found. Please install from https://python.org/
    pause & exit /b 1
)
echo ✅ All prerequisites found

:: Stop existing services
echo.
echo [2/6] Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
echo ✅ Cleanup complete

:: Setup Ollama
echo.
echo [3/6] Setting up Ollama with DeepSeek-R1...
set OLLAMA_NUM_PARALLEL=4
set OLLAMA_MAX_LOADED_MODELS=1
set OLLAMA_FLASH_ATTENTION=1

ollama list | findstr "deepseek-r1:1.5b" >nul
if %errorlevel% neq 0 (
    echo Pulling DeepSeek-R1 1.5B model...
    ollama pull deepseek-r1:1.5b
)

ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting Ollama server...
    start "Ollama" cmd /k "title Ollama Server && ollama serve"
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ Ollama is running with DeepSeek-R1
)

:: Setup backend
echo.
echo [4/6] Setting up backend...
cd backend-python

if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Installing dependencies...
venv\Scripts\pip.exe install --quiet --upgrade pip
venv\Scripts\pip.exe install --quiet fastapi uvicorn httpx pydantic

echo Starting backend server...
start "ASCAES Backend" cmd /k "title ASCAES Backend && cd /d %cd% && venv\Scripts\python.exe -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload"

cd ..

:: Setup frontend
echo.
echo [5/6] Setting up frontend...
cd frontend-react

if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install --silent
)

echo Starting frontend server...
start "ASCAES Frontend" cmd /k "title ASCAES Frontend && cd /d %cd% && npm run dev"

cd ..

:: Launch application
echo.
echo [6/6] Launching application...
timeout /t 12 /nobreak >nul

echo Verifying services...
curl -s http://localhost:8001/api/v1/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend ready
) else (
    echo ⚠️  Backend starting...
)

curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend ready
) else (
    echo ⚠️  Frontend starting...
)

start http://localhost:5173

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 ASCAES IS READY! 🚀                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📱 Application: http://localhost:5173
echo 🔧 Backend API: http://localhost:8001
echo 🤖 Model: DeepSeek-R1 1.5B (Advanced Reasoning)
echo.
echo 🛑 To stop: Close the opened terminal windows
echo 📋 All services are running in separate windows
echo.
echo Press any key to close this launcher...
pause >nul
