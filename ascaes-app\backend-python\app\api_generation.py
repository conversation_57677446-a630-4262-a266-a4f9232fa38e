from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator
import httpx
import asyncio
import json

from app.models import GenerationRequest
from app.services import construct_prompt
from app.database import insert_document

router = APIRouter()

OLLAMA_API_URL = "http://localhost:11434/api/generate"
MODEL_NAME = "llama3.2:3b"  # Using smaller, faster model

@router.post("/generate")
async def generate_document(request: GenerationRequest):
    if request.total_pages > 1500:
        raise HTTPException(status_code=400, detail="Maximum page limit is 1500.")

    # Use a simplified prompt for faster generation
    full_prompt = f"Write a {request.writing_style.lower()} academic essay about {request.topic}. Make it approximately {request.total_pages * 250} words."

    async def generate_and_stream():
        generated_text_parts = []
        print(f"Starting generation for topic: {request.topic}")
        print(f"Using prompt: {full_prompt}")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    OLLAMA_API_URL,
                    json={
                        "model": MODEL_NAME,
                        "prompt": full_prompt,
                        "stream": True
                    },
                    timeout=300.0  # 5 minute timeout
                )
                response.raise_for_status()

                async for chunk in response.aiter_bytes():
                    try:
                        # Ollama streams JSON objects, each on a new line
                        decoded_chunk = chunk.decode("utf-8")
                        for line in decoded_chunk.splitlines():
                            if line.strip():
                                json_data = json.loads(line)
                                if "response" in json_data:
                                    text_part = json_data["response"]
                                    generated_text_parts.append(text_part)
                                    yield text_part.encode("utf-8")
                                elif "error" in json_data:
                                    raise HTTPException(status_code=500, detail=f"Ollama error: {json_data['error']}")
                    except json.JSONDecodeError:
                        # Handle cases where a chunk might not be a complete JSON line
                        continue

        except httpx.RequestError as exc:
            raise HTTPException(status_code=500, detail=f"Ollama connection error: {exc}")
        except httpx.HTTPStatusError as exc:
            raise HTTPException(status_code=exc.response.status_code, detail=f"Ollama API error: {exc.response.text}")

        # After generation, save to database
        full_generated_text = "".join(generated_text_parts)
        insert_document(
            title=request.topic, # Using topic as title for now, can be refined
            topic=request.topic,
            writing_style=request.writing_style,
            cultural_inflection_config=request.cultural_inflection_config,
            total_pages=request.total_pages,
            humanization=request.humanization,
            academic_formatting=request.academic_formatting.dict() if request.academic_formatting else None,
            generated_text=full_generated_text
        )

    return StreamingResponse(generate_and_stream(), media_type="text/plain")

@router.get("/test-ollama")
async def test_ollama():
    """Test endpoint to check if Ollama is working"""
    try:
        print(f"Testing Ollama with model: {MODEL_NAME}")
        print(f"Ollama URL: {OLLAMA_API_URL}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                OLLAMA_API_URL,
                json={
                    "model": MODEL_NAME,
                    "prompt": "Say hello in one sentence.",
                    "stream": False
                },
                timeout=300.0
            )
            print(f"Response status: {response.status_code}")
            response.raise_for_status()
            result = response.json()
            print(f"Response content: {result}")
            return {"status": "success", "response": result.get("response", "No response")}
    except Exception as e:
        error_msg = str(e)
        print(f"Ollama test error: {error_msg}")
        return {"status": "error", "message": error_msg}


