from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator
import httpx
import asyncio
import json

from app.models import GenerationRequest
from app.services import construct_prompt
from app.database import insert_document

router = APIRouter()

OLLAMA_API_URL = "http://localhost:11434/api/generate"
MODEL_NAME = "gemma3n:e2b"

@router.post("/generate")
async def generate_document(request: GenerationRequest):
    if request.total_pages > 1500:
        raise HTTPException(status_code=400, detail="Maximum page limit is 1500.")

    full_prompt = construct_prompt(
        topic=request.topic,
        writing_style=request.writing_style,
        cultural_inflection_config=request.cultural_inflection_config,
        humanization=request.humanization,
        academic_formatting=request.academic_formatting.dict() if request.academic_formatting else None
    )

    async def generate_and_stream():
        generated_text_parts = []
        for i in range(0, request.total_pages, 10):
            current_pages = min(10, request.total_pages - i)
            prompt_for_batch = f"{full_prompt}\n\nGenerate approximately {current_pages * 250} words for the next {current_pages} pages."
            
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        OLLAMA_API_URL,
                        json={
                            "model": MODEL_NAME,
                            "prompt": prompt_for_batch,
                            "stream": True
                        },
                        timeout=None
                    )
                    response.raise_for_status()

                    async for chunk in response.aiter_bytes():
                        try:
                            # Ollama streams JSON objects, each on a new line
                            decoded_chunk = chunk.decode("utf-8")
                            for line in decoded_chunk.splitlines():
                                if line.strip():
                                    json_data = json.loads(line)
                                    if "response" in json_data:
                                        text_part = json_data["response"]
                                        generated_text_parts.append(text_part)
                                        yield text_part.encode("utf-8")
                                    elif "error" in json_data:
                                        raise HTTPException(status_code=500, detail=f"Ollama error: {json_data['error']}")
                        except json.JSONDecodeError:
                            # Handle cases where a chunk might not be a complete JSON line
                            continue

            except httpx.RequestError as exc:
                raise HTTPException(status_code=500, detail=f"Ollama connection error: {exc}")
            except httpx.HTTPStatusError as exc:
                raise HTTPException(status_code=exc.response.status_code, detail=f"Ollama API error: {exc.response.text}")

            if i + 10 < request.total_pages:
                await asyncio.sleep(6) # 6-second delay between batches

        # After generation, save to database
        full_generated_text = "".join(generated_text_parts)
        insert_document(
            title=request.topic, # Using topic as title for now, can be refined
            topic=request.topic,
            writing_style=request.writing_style,
            cultural_inflection_config=request.cultural_inflection_config,
            total_pages=request.total_pages,
            humanization=request.humanization,
            academic_formatting=request.academic_formatting.dict() if request.academic_formatting else None,
            generated_text=full_generated_text
        )

    return StreamingResponse(generate_and_stream(), media_type="text/plain")


