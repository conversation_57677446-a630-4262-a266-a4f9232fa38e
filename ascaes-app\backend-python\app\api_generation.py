from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingR<PERSON>ponse
from typing import AsyncGenerator
import httpx
import asyncio
import json

from app.models import GenerationRequest
from app.services import construct_prompt
from app.database import insert_document

router = APIRouter()

OLLAMA_API_URL = "http://localhost:11434/api/generate"

@router.post("/generate")
async def generate_document(request: GenerationRequest):
    if request.total_pages > 1500:
        raise HTTPException(status_code=400, detail="Maximum page limit is 1500.")

    # Use the full prompt construction for better quality
    full_prompt = construct_prompt(
        topic=request.topic,
        writing_style=request.writing_style,
        cultural_inflection_config=request.cultural_inflection_config,
        total_pages=request.total_pages,
        humanization=request.humanization,
        academic_formatting=request.academic_formatting.model_dump() if request.academic_formatting else None
    )

    async def generate_and_stream():
        generated_text_parts = []
        print(f"Starting generation for topic: {request.topic}")

        # Process in batches for better progress tracking
        pages_per_batch = min(10, request.total_pages)
        total_batches = (request.total_pages + pages_per_batch - 1) // pages_per_batch

        for batch_num in range(total_batches):
            start_page = batch_num * pages_per_batch + 1
            end_page = min((batch_num + 1) * pages_per_batch, request.total_pages)
            current_batch_pages = end_page - start_page + 1

            # Send progress update
            progress_msg = f"PROGRESS:{start_page}:{request.total_pages}:Generating pages {start_page}-{end_page}\n"
            yield progress_msg.encode("utf-8")

            # Create batch-specific prompt
            if request.total_pages > 1:
                batch_prompt = f"{full_prompt}\n\nGenerate approximately {current_batch_pages * 250} words for pages {start_page} to {end_page}."
            else:
                batch_prompt = full_prompt

            print(f"Batch {batch_num + 1}/{total_batches}: Pages {start_page}-{end_page}")

            try:
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        OLLAMA_API_URL,
                        json={
                            "model": request.model_name or "llama3.2:1b",
                            "prompt": batch_prompt,
                            "stream": True
                        },
                        timeout=300.0  # 5 minute timeout
                    )
                    response.raise_for_status()

                    async for chunk in response.aiter_bytes():
                        try:
                            # Ollama streams JSON objects, each on a new line
                            decoded_chunk = chunk.decode("utf-8")
                            for line in decoded_chunk.splitlines():
                                if line.strip():
                                    json_data = json.loads(line)
                                    if "response" in json_data:
                                        text_part = json_data["response"]
                                        generated_text_parts.append(text_part)
                                        yield text_part.encode("utf-8")
                                    elif "error" in json_data:
                                        raise HTTPException(status_code=500, detail=f"Ollama error: {json_data['error']}")
                        except json.JSONDecodeError:
                            # Handle cases where a chunk might not be a complete JSON line
                            continue

            except httpx.RequestError as exc:
                raise HTTPException(status_code=500, detail=f"Ollama connection error: {exc}")
            except httpx.HTTPStatusError as exc:
                raise HTTPException(status_code=exc.response.status_code, detail=f"Ollama API error: {exc.response.text}")

            # Add spacing between batches
            if batch_num < total_batches - 1:
                yield "\n\n".encode("utf-8")

        # After generation, save to database
        full_generated_text = "".join(generated_text_parts)
        insert_document(
            title=request.topic, # Using topic as title for now, can be refined
            topic=request.topic,
            writing_style=request.writing_style,
            cultural_inflection_config=request.cultural_inflection_config,
            total_pages=request.total_pages,
            humanization=request.humanization,
            academic_formatting=request.academic_formatting.model_dump() if request.academic_formatting else None,
            generated_text=full_generated_text
        )

    return StreamingResponse(generate_and_stream(), media_type="text/plain")

@router.get("/test-ollama")
async def test_ollama(model_name: str = "llama3.2:1b"):
    """Test endpoint to check if Ollama is working"""
    try:
        print(f"Testing Ollama with model: {model_name}")
        print(f"Ollama URL: {OLLAMA_API_URL}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                OLLAMA_API_URL,
                json={
                    "model": model_name,
                    "prompt": "Say hello in one sentence.",
                    "stream": False
                },
                timeout=300.0
            )
            print(f"Response status: {response.status_code}")
            response.raise_for_status()
            result = response.json()
            print(f"Response content: {result}")
            return {"status": "success", "response": result.get("response", "No response")}
    except Exception as e:
        error_msg = str(e)
        print(f"Ollama test error: {error_msg}")
        return {"status": "error", "message": error_msg}


