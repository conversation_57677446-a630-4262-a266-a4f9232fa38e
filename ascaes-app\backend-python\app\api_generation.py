from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import httpx
import json

from app.models import GenerationRequest
from app.services import construct_prompt
from app.database import insert_document

router = APIRouter()

OLLAMA_API_URL = "http://localhost:11434/api/generate"

@router.post("/generate")
async def generate_document(request: GenerationRequest):
    if request.total_pages > 1000:
        raise HTTPException(status_code=400, detail="Maximum page limit is 1000.")

    # Use the full prompt construction for better quality
    full_prompt = construct_prompt(
        topic=request.topic,
        writing_style=request.writing_style,
        cultural_inflection_config=request.cultural_inflection_config,
        total_pages=request.total_pages,
        humanization=request.humanization,
        academic_formatting=request.academic_formatting.model_dump() if request.academic_formatting else None
    )

    async def generate_and_stream():
        generated_text_parts = []
        total_words_generated = 0
        print(f"Starting generation for topic: {request.topic}")

        # Process in batches for better progress tracking
        pages_per_batch = 10  # Fixed batch size of 10 pages
        total_batches = (request.total_pages + pages_per_batch - 1) // pages_per_batch

        for batch_num in range(total_batches):
            start_page = batch_num * pages_per_batch + 1
            end_page = min((batch_num + 1) * pages_per_batch, request.total_pages)
            current_batch_pages = end_page - start_page + 1

            # Send enhanced progress update with batch info
            progress_msg = f"PROGRESS:{start_page}:{end_page}:{request.total_pages}:{batch_num + 1}:{total_batches}:Generating pages {start_page}-{end_page} (Batch {batch_num + 1}/{total_batches})\n"
            yield progress_msg.encode("utf-8")

            # Create batch-specific prompt with strict word count enforcement
            target_words_for_batch = current_batch_pages * 250
            if request.total_pages > 1:
                batch_prompt = f"""{full_prompt}

CRITICAL BATCH REQUIREMENTS FOR PAGES {start_page}-{end_page}:
- Generate EXACTLY {target_words_for_batch} words (no more, no less)
- This is batch {batch_num + 1} of {total_batches}
- Each page must contain approximately 250 words
- Continue the document flow naturally from previous content
- Do NOT include word count in your response
- Do NOT stop until you reach exactly {target_words_for_batch} words

Write the content for pages {start_page} to {end_page} now:"""
            else:
                batch_prompt = full_prompt

            print(f"Batch {batch_num + 1}/{total_batches}: Pages {start_page}-{end_page}")

            try:
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        OLLAMA_API_URL,
                        json={
                            "model": request.model_name or "deepseek-r1:1.5b",
                            "prompt": batch_prompt,
                            "stream": True,
                            "options": {
                                "temperature": 0.7,  # Balanced creativity
                                "top_p": 0.9,        # Focus on likely tokens
                                "top_k": 40,         # Limit vocabulary for speed
                                "repeat_penalty": 1.1,  # Reduce repetition
                                "num_predict": target_words_for_batch + 100,  # Allow extra tokens for exact word count
                                "num_ctx": 4096,     # Larger context window
                                "num_batch": 512,    # Batch size for faster processing
                                "num_gpu": -1,       # Use all available GPU
                                "num_thread": -1,    # Use all CPU threads
                                "stop": []           # Don't stop early
                            }
                        },
                        timeout=300.0  # 5 minute timeout
                    )
                    response.raise_for_status()

                    batch_words = 0
                    async for chunk in response.aiter_bytes():
                        try:
                            # Ollama streams JSON objects, each on a new line
                            decoded_chunk = chunk.decode("utf-8")
                            for line in decoded_chunk.splitlines():
                                if line.strip():
                                    json_data = json.loads(line)
                                    if "response" in json_data:
                                        text_part = json_data["response"]
                                        generated_text_parts.append(text_part)

                                        # Count words in this chunk
                                        chunk_words = len(text_part.split())
                                        batch_words += chunk_words
                                        total_words_generated += chunk_words

                                        # Send progress update with word count
                                        if batch_words > 0:
                                            progress_msg = f"PROGRESS:{start_page}:{end_page}:{request.total_pages}:{batch_num + 1}:{total_batches}:Generating pages {start_page}-{end_page} ({batch_words}/{target_words_for_batch} words)\n"
                                            yield progress_msg.encode("utf-8")

                                        yield text_part.encode("utf-8")
                                    elif "error" in json_data:
                                        raise HTTPException(status_code=500, detail=f"Ollama error: {json_data['error']}")
                        except json.JSONDecodeError:
                            # Handle cases where a chunk might not be a complete JSON line
                            continue

            except httpx.RequestError as exc:
                raise HTTPException(status_code=500, detail=f"Ollama connection error: {exc}")
            except httpx.HTTPStatusError as exc:
                raise HTTPException(status_code=exc.response.status_code, detail=f"Ollama API error: {exc.response.text}")

            # Validate batch word count
            print(f"Batch {batch_num + 1} completed with {batch_words} words (target: {target_words_for_batch})")

            # If we're significantly under the target, try to generate more
            if batch_words < target_words_for_batch * 0.8:  # If less than 80% of target
                print(f"Warning: Batch {batch_num + 1} generated only {batch_words} words, expected {target_words_for_batch}")

                # Send additional generation request for remaining words
                remaining_words = target_words_for_batch - batch_words
                if remaining_words > 50:  # Only if significant shortfall
                    additional_prompt = f"Continue the previous content and add exactly {remaining_words} more words to complete pages {start_page}-{end_page}. Do not repeat previous content."

                    try:
                        async with httpx.AsyncClient() as client:
                            additional_response = await client.post(
                                OLLAMA_API_URL,
                                json={
                                    "model": request.model_name or "deepseek-r1:1.5b",
                                    "prompt": additional_prompt,
                                    "stream": True,
                                    "options": {
                                        "temperature": 0.7,
                                        "num_predict": remaining_words + 50,
                                        "num_ctx": 4096,
                                        "stop": []
                                    }
                                },
                                timeout=300.0
                            )

                            async for chunk in additional_response.aiter_bytes():
                                try:
                                    decoded_chunk = chunk.decode("utf-8")
                                    for line in decoded_chunk.splitlines():
                                        if line.strip():
                                            json_data = json.loads(line)
                                            if "response" in json_data:
                                                text_part = json_data["response"]
                                                generated_text_parts.append(text_part)
                                                yield text_part.encode("utf-8")
                                except json.JSONDecodeError:
                                    continue
                    except Exception as e:
                        print(f"Additional generation failed: {e}")

            # Add spacing between batches
            if batch_num < total_batches - 1:
                yield "\n\n".encode("utf-8")

        # After generation, save to database and send completion summary
        full_generated_text = "".join(generated_text_parts)
        final_word_count = len(full_generated_text.split())
        target_word_count = request.total_pages * 250

        # Send completion summary
        completion_msg = f"COMPLETION:{final_word_count}:{target_word_count}:{request.total_pages}:Document generation completed! Generated {final_word_count} words (target: {target_word_count})\n"
        yield completion_msg.encode("utf-8")

        print(f"Generation completed: {final_word_count} words generated (target: {target_word_count})")

        insert_document(
            title=request.topic, # Using topic as title for now, can be refined
            topic=request.topic,
            writing_style=request.writing_style,
            cultural_inflection_config=request.cultural_inflection_config,
            total_pages=request.total_pages,
            humanization=request.humanization,
            academic_formatting=request.academic_formatting.model_dump() if request.academic_formatting else None,
            generated_text=full_generated_text
        )

    return StreamingResponse(generate_and_stream(), media_type="text/plain")

@router.get("/test-ollama")
async def test_ollama(model_name: str = "deepseek-r1:1.5b"):
    """Test endpoint to check if Ollama is working"""
    try:
        print(f"Testing Ollama with model: {model_name}")
        print(f"Ollama URL: {OLLAMA_API_URL}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                OLLAMA_API_URL,
                json={
                    "model": model_name,
                    "prompt": "Say hello in one sentence.",
                    "stream": False
                },
                timeout=300.0
            )
            print(f"Response status: {response.status_code}")
            response.raise_for_status()
            result = response.json()
            print(f"Response content: {result}")
            return {"status": "success", "response": result.get("response", "No response")}
    except Exception as e:
        error_msg = str(e)
        print(f"Ollama test error: {error_msg}")
        return {"status": "error", "message": error_msg}


