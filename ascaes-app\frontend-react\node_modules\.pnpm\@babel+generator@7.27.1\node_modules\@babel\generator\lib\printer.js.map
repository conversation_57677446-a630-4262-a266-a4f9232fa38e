{"version": 3, "names": ["_buffer", "require", "n", "_t", "_tokenMap", "generatorFunctions", "_deprecated", "isExpression", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumMember", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "HAS_NEWLINE", "HAS_NEWLINE_OR_BlOCK_COMMENT_END", "commentIsNewline", "c", "type", "test", "value", "needsParens", "Printer", "constructor", "format", "map", "tokens", "originalCode", "inForStatementInit", "tokenContext", "_tokens", "_originalCode", "_currentNode", "_indent", "_indentRepeat", "_insideAux", "_noLineTerminator", "_noLineTerminatorAfterNode", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_endsWithDiv", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "tokenMap", "_boundGetRawIdentifier", "_getRawIdentifier", "bind", "_printSemicolonBeforeNextNode", "_printSemicolonBeforeNextToken", "indent", "style", "length", "_inputMap", "_buf", "<PERSON><PERSON><PERSON>", "enterForStatementInit", "enterDelimited", "oldInForStatementInit", "oldNoLineTerminatorAfterNode", "generate", "ast", "preserveFormat", "TokenMap", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "node", "start", "end", "endMatches", "getCurrentLine", "indexes", "getIndexes", "_catchUpTo", "loc", "_queue", "rightBrace", "minified", "removeLastSemicolon", "sourceWithOffset", "token", "rightParens", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "_maybePrintInnerComments", "_catchUpToCurrentToken", "charCodeAt", "_append", "number", "isNonDecimalLiteral", "secondChar", "Number", "isInteger", "maybeNewline", "occurrenceCount", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "String", "fromCharCode", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWith", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "cb", "_catchUp", "source", "prop", "columnOffset", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "findMatching", "appendChar", "_maybeIndent", "append", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "catchUp", "line", "count", "column", "index", "spacesCount", "getCurrentColumn", "spaces", "slice", "replace", "repeat", "printTerminatorless", "trailingCommentsLineOffset", "_node$extra", "_node$leadingComments", "_node$leadingComments2", "nodeType", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "parent", "oldInAux", "parenthesized", "extra", "shouldPrintParens", "retainFunctionParens", "leadingComments", "parentType", "callee", "indentParenthesized", "some", "oldInForStatementInitWasTrue", "isLastChild", "_node$trailingComment", "trailingComments", "_printLeadingComments", "_printTrailingComments", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "statement", "separator", "printTrailingSeparator", "addNewlines", "iterator", "_nodes$0$loc", "startLine", "newlineOpts", "nextNodeStartLine", "boundSeparator", "len", "_printNewline", "_node$trailingComment2", "_nextNode$loc", "nextNode", "printAndIndentOnComments", "printBlock", "body", "lineOffset", "innerComments", "_printComments", "comments", "nextTokenStr", "nextTokenOccurrenceCount", "_this$tokenMap", "printInnerComments", "nextToken", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "printList", "items", "commaSeparator", "shouldPrintTrailingComma", "listEnd", "listEndIndex", "findLastIndex", "matchesOriginal", "newLine", "opts", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "commentTok", "find", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "_comment$loc", "newlineRegex", "RegExp", "indentSize", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "addDeprecatedGenerators", "_default", "exports", "default", "last"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer.ts\";\nimport type { Loc } from \"./buffer.ts\";\nimport * as n from \"./node/index.ts\";\nimport type * as t from \"@babel/types\";\nimport {\n  isExpression,\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumMember,\n} from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport { TokenMap } from \"./token-map.ts\";\nimport type { GeneratorOptions } from \"./index.ts\";\nimport * as generatorFunctions from \"./generators/index.ts\";\nimport {\n  addDeprecatedGenerators,\n  type DeprecatedBabel7ASTTypes,\n} from \"./generators/deprecated.ts\";\nimport type SourceMap from \"./source-map.ts\";\nimport type { TraceMap } from \"@jridgewell/trace-mapping\";\nimport type { Token } from \"@babel/parser\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_NEWLINE_OR_BlOCK_COMMENT_END = /[\\n\\r\\u2028\\u2029]|\\*\\//;\n\nfunction commentIsNewline(c: t.Comment) {\n  return c.type === \"CommentLine\" || HAS_NEWLINE.test(c.value);\n}\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  preserveFormat: boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  /**\n   * @deprecated Removed in Babel 8, syntax type is always 'hash'\n   */\n  recordAndTupleSyntaxType?: GeneratorOptions[\"recordAndTupleSyntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: GeneratorOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer, occurrenceCount: number, last: boolean) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n  printTrailingSeparator?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(\n    format: Format,\n    map: SourceMap,\n    tokens?: Token[],\n    originalCode?: string,\n  ) {\n    this.format = format;\n\n    this._tokens = tokens;\n    this._originalCode = originalCode;\n\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n\n    this._buf = new Buffer(map, format.indent.style[0]);\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n\n  inForStatementInit: boolean = false;\n  enterForStatementInit() {\n    if (this.inForStatementInit) return () => {};\n    this.inForStatementInit = true;\n    return () => {\n      this.inForStatementInit = false;\n    };\n  }\n\n  enterDelimited() {\n    const oldInForStatementInit = this.inForStatementInit;\n    const oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n    if (\n      oldInForStatementInit === false &&\n      oldNoLineTerminatorAfterNode === null\n    ) {\n      return () => {};\n    }\n    this.inForStatementInit = false;\n    this._noLineTerminatorAfterNode = null;\n    return () => {\n      this.inForStatementInit = oldInForStatementInit;\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    };\n  }\n\n  tokenContext: number = 0;\n\n  _tokens: Token[] = null;\n  _originalCode: string | null = null;\n\n  declare _buf: Buffer;\n  _currentNode: t.Node = null;\n  _indent: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _noLineTerminator: boolean = false;\n  _noLineTerminatorAfterNode: t.Node | null = null;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _endsWithDiv = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n  tokenMap: TokenMap = null;\n\n  _boundGetRawIdentifier = this._getRawIdentifier.bind(this);\n\n  generate(ast: t.Node) {\n    if (this.format.preserveFormat) {\n      this.tokenMap = new TokenMap(ast, this._tokens, this._originalCode);\n    }\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    const { format } = this;\n    if (format.preserveFormat || format.compact || format.concise) {\n      return;\n    }\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    const { format } = this;\n    if (format.preserveFormat || format.compact || format.concise) {\n      return;\n    }\n\n    this._indent--;\n  }\n\n  /**\n   * If the next token is on the same line, we must first print a semicolon.\n   * This option is only used in `preserveFormat` node, for semicolons that\n   * might have omitted due to them being absent in the original code (thanks\n   * to ASI).\n   *\n   * We need both *NextToken and *NextNode because we only want to insert the\n   * semicolon when the next token starts a new node, and not in cases like\n   * foo} (where } is not starting a new node). So we first set *NextNode, and\n   * then the print() method will move it to *NextToken.\n   */\n  _printSemicolonBeforeNextNode: number = -1;\n  _printSemicolonBeforeNextToken: number = -1;\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n      this._noLineTerminator = false;\n      return;\n    }\n    if (this.tokenMap) {\n      const node = this._currentNode;\n      if (node.start != null && node.end != null) {\n        if (!this.tokenMap.endMatches(node, \";\")) {\n          // no semicolon\n          this._printSemicolonBeforeNextNode = this._buf.getCurrentLine();\n          return;\n        }\n        const indexes = this.tokenMap.getIndexes(this._currentNode);\n        this._catchUpTo(this._tokens[indexes[indexes.length - 1]].loc.start);\n      }\n    }\n    this._queue(charCodes.semicolon);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(node: t.Node): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\"}\");\n  }\n\n  rightParens(node: t.Node): void {\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\")\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    const { format } = this;\n    if (format.compact || format.preserveFormat) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments(str);\n\n    this._maybeAddAuxComment();\n\n    if (this.tokenMap) this._catchUpToCurrentToken(str);\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (this._endsWithDiv && str.charCodeAt(0) === charCodes.slash)\n    ) {\n      this._space();\n    }\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string, number?: number): void {\n    // const NON_DECIMAL_LITERAL = /^0[box]/;\n    function isNonDecimalLiteral(str: string) {\n      if (str.length > 2 && str.charCodeAt(0) === charCodes.digit0) {\n        const secondChar = str.charCodeAt(1);\n        return (\n          secondChar === charCodes.lowercaseB ||\n          secondChar === charCodes.lowercaseO ||\n          secondChar === charCodes.lowercaseX\n        );\n      }\n      return false;\n    }\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(number) &&\n      !isNonDecimalLiteral(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   *\n   * @param {string} str The string to append.\n   * @param {boolean} [maybeNewline=false] Wether `str` might potentially\n   *    contain a line terminator or not.\n   * @param {number} [occurrenceCount=0] The occurrence count of this token in\n   *    the current node. This is used when printing in `preserveFormat` mode,\n   *    to know which token we should map to (for example, to disambiguate the\n   *    commas in an array literal).\n   */\n  token(str: string, maybeNewline = false, occurrenceCount = 0): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments(str, occurrenceCount);\n\n    this._maybeAddAuxComment();\n\n    if (this.tokenMap) this._catchUpToCurrentToken(str, occurrenceCount);\n\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark &&\n        // space is mandatory to avoid outputting <!--\n        // http://javascript.spec.whatwg.org/#comment-syntax\n        (str === \"--\" ||\n          // Needs spaces to avoid changing a! == 0 to a!== 0\n          strFirst === charCodes.equalsTo)) ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n    this._append(str, maybeNewline);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this.tokenContext = 0;\n\n    const str = String.fromCharCode(char);\n    this._maybePrintInnerComments(str);\n\n    this._maybeAddAuxComment();\n\n    if (this.tokenMap) this._catchUpToCurrentToken(str);\n\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!loc || this.format.preserveFormat) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, columnOffset);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _catchUpToCurrentToken(str: string, occurrenceCount: number = 0): void {\n    // Assert: this.tokenMap\n\n    const token = this.tokenMap.findMatching(\n      this._currentNode,\n      str,\n      occurrenceCount,\n    );\n    if (token) this._catchUpTo(token.loc.start);\n\n    if (\n      this._printSemicolonBeforeNextToken !== -1 &&\n      this._printSemicolonBeforeNextToken === this._buf.getCurrentLine()\n    ) {\n      this._buf.appendChar(charCodes.semicolon);\n      this._endsWithWord = false;\n      this._endsWithInteger = false;\n      this._endsWithDiv = false;\n    }\n    this._printSemicolonBeforeNextToken = -1;\n    this._printSemicolonBeforeNextNode = -1;\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _queue(char: number) {\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    const { format } = this;\n    if (!format.preserveFormat) {\n      if (format.retainLines && loc?.[prop]) {\n        this.catchUp(loc[prop].line);\n      }\n      return;\n    }\n\n    // catch up to this nodes newline if we're behind\n    const pos = loc?.[prop];\n    if (pos != null) this._catchUpTo(pos);\n  }\n\n  _catchUpTo({ line, column, index }: Pos) {\n    const count = line - this._buf.getCurrentLine();\n    if (count > 0 && this._noLineTerminator) {\n      // We cannot inject new lines when _noLineTemrinator is set\n      // to `true`, or we would generate invalid code.\n      return;\n    }\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n\n    const spacesCount =\n      count > 0 ? column : column - this._buf.getCurrentColumn();\n    if (spacesCount > 0) {\n      const spaces = this._originalCode\n        ? this._originalCode\n            .slice(index - spacesCount, index)\n            // https://tc39.es/ecma262/#sec-white-space\n            .replace(/[^\\t\\v\\f\\uFEFF\\p{Space_Separator}]/gu, \" \")\n        : \" \".repeat(spacesCount);\n      this._append(spaces, false);\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    this._noLineTerminator = true;\n    this.print(node);\n  }\n\n  print(\n    node: t.Node | null,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          | DeprecatedBabel7ASTTypes\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    const parent = this._currentNode;\n    this._currentNode = node;\n\n    if (this.tokenMap) {\n      this._printSemicolonBeforeNextToken = this._printSemicolonBeforeNextNode;\n    }\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == null;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    const parenthesized = node.extra?.parenthesized as boolean | undefined;\n    let shouldPrintParens =\n      (parenthesized && format.preserveFormat) ||\n      (parenthesized &&\n        format.retainFunctionParens &&\n        nodeType === \"FunctionExpression\") ||\n      needsParens(\n        node,\n        parent,\n        this.tokenContext,\n        this.inForStatementInit,\n        format.preserveFormat ? this._boundGetRawIdentifier : undefined,\n      );\n\n    if (\n      !shouldPrintParens &&\n      parenthesized &&\n      node.leadingComments?.length &&\n      node.leadingComments[0].type === \"CommentBlock\"\n    ) {\n      const parentType = parent?.type;\n      switch (parentType) {\n        case \"ExpressionStatement\":\n        case \"VariableDeclarator\":\n        case \"AssignmentExpression\":\n        case \"ReturnStatement\":\n          break;\n        case \"CallExpression\":\n        case \"OptionalCallExpression\":\n        case \"NewExpression\":\n          if (parent.callee !== node) break;\n        // falls through\n        default:\n          shouldPrintParens = true;\n      }\n    }\n\n    let indentParenthesized = false;\n    if (\n      !shouldPrintParens &&\n      this._noLineTerminator &&\n      (node.leadingComments?.some(commentIsNewline) ||\n        (this.format.retainLines &&\n          node.loc &&\n          node.loc.start.line > this._buf.getCurrentLine()))\n    ) {\n      shouldPrintParens = true;\n      indentParenthesized = true;\n    }\n\n    let oldNoLineTerminatorAfterNode;\n    let oldInForStatementInitWasTrue;\n    if (!shouldPrintParens) {\n      noLineTerminatorAfter ||=\n        parent &&\n        this._noLineTerminatorAfterNode === parent &&\n        n.isLastChild(parent, node);\n      if (noLineTerminatorAfter) {\n        if (node.trailingComments?.some(commentIsNewline)) {\n          if (isExpression(node)) shouldPrintParens = true;\n        } else {\n          oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n          this._noLineTerminatorAfterNode = node;\n        }\n      }\n    }\n\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      if (indentParenthesized) this.indent();\n      this._endsWithInnerRaw = false;\n      if (this.inForStatementInit) {\n        oldInForStatementInitWasTrue = true;\n        this.inForStatementInit = false;\n      }\n      oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n      this._noLineTerminatorAfterNode = null;\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(\n      loc,\n      // @ts-expect-error Expected 1 arguments, but got 3.\n      printMethod.bind(this, node, parent),\n    );\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      if (indentParenthesized) {\n        this.dedent();\n        this.newline();\n      }\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n      if (oldInForStatementInitWasTrue) this.inForStatementInit = true;\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._currentNode = parent;\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    if (oldNoLineTerminatorAfterNode !== undefined) {\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    }\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra?.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    statement?: boolean,\n    indent?: boolean,\n    separator?: PrintJoinOptions[\"separator\"],\n    printTrailingSeparator?: boolean,\n    addNewlines?: PrintJoinOptions[\"addNewlines\"],\n    iterator?: PrintJoinOptions[\"iterator\"],\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!nodes?.length) return;\n\n    if (indent == null && this.format.retainLines) {\n      const startLine = nodes[0].loc?.start.line;\n      if (startLine != null && startLine !== this._buf.getCurrentLine()) {\n        indent = true;\n      }\n    }\n\n    if (indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const boundSeparator = separator?.bind(this);\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, undefined, trailingCommentsLineOffset || 0);\n\n      iterator?.(node, i);\n\n      if (boundSeparator != null) {\n        if (i < len - 1) boundSeparator(i, false);\n        else if (printTrailingSeparator) boundSeparator(i, true);\n      }\n\n      if (statement) {\n        if (!node.trailingComments?.length) {\n          this._lastCommentLine = 0;\n        }\n\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments(\n    nextTokenStr: string,\n    nextTokenOccurrenceCount?: number,\n  ) {\n    if (this._endsWithInnerRaw) {\n      this.printInnerComments(\n        this.tokenMap?.findMatching(\n          this._currentNode,\n          nextTokenStr,\n          nextTokenOccurrenceCount,\n        ),\n      );\n    }\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments(nextToken?: Token) {\n    const node = this._currentNode;\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(\n      COMMENT_TYPE.INNER,\n      comments,\n      node,\n      undefined,\n      undefined,\n      nextToken,\n    );\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(\n    nodes: t.Node[],\n    indent?: boolean,\n    trailingCommentsLineOffset?: number,\n    addNewlines?: PrintSequenceOptions[\"addNewlines\"],\n  ) {\n    this.printJoin(\n      nodes,\n      true,\n      indent ?? false,\n      undefined,\n      undefined,\n      addNewlines,\n      undefined,\n      trailingCommentsLineOffset,\n    );\n  }\n\n  printList(\n    items: t.Node[],\n    printTrailingSeparator?: boolean,\n    statement?: boolean,\n    indent?: boolean,\n    separator?: PrintListOptions[\"separator\"],\n    iterator?: PrintListOptions[\"iterator\"],\n  ) {\n    this.printJoin(\n      items,\n      statement,\n      indent,\n      separator ?? commaSeparator,\n      printTrailingSeparator,\n      undefined,\n      iterator,\n    );\n  }\n\n  shouldPrintTrailingComma(listEnd: string): boolean | null {\n    if (!this.tokenMap) return null;\n\n    const listEndIndex = this.tokenMap.findLastIndex(this._currentNode, token =>\n      this.tokenMap.matchesOriginal(token, listEnd),\n    );\n    if (listEndIndex <= 0) return null;\n    return this.tokenMap.matchesOriginal(this._tokens[listEndIndex - 1], \",\");\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    const format = this.format;\n\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (format.retainLines || format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(\n    comment: t.Comment,\n    nextToken?: Token,\n  ): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      HAS_NEWLINE_OR_BlOCK_COMMENT_END.test(comment.value)\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    if (nextToken && this.tokenMap) {\n      const commentTok = this.tokenMap.find(\n        this._currentNode,\n        token => token.value === comment.value,\n      );\n      if (commentTok && commentTok.start > nextToken.start) {\n        return PRINT_COMMENT_HINT.DEFER;\n      }\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace &&\n      lastCharCode !== charCodes.leftParenthesis\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n        if (this.format.concise) {\n          val = val.replace(/\\n(?!$)/g, `\\n`);\n        } else {\n          let indentSize = this.format.retainLines\n            ? 0\n            : this._buf.getCurrentColumn();\n\n          if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n            indentSize += this._getIndent();\n          }\n\n          val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n        }\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid converting a / operator into a line comment by appending /* to it\n    if (this._endsWithDiv) this._space();\n\n    if (this.tokenMap) {\n      const { _printSemicolonBeforeNextToken, _printSemicolonBeforeNextNode } =\n        this;\n      this._printSemicolonBeforeNextToken = -1;\n      this._printSemicolonBeforeNextNode = -1;\n      this.source(\"start\", comment.loc);\n      this._append(val, isBlockComment);\n      this._printSemicolonBeforeNextNode = _printSemicolonBeforeNextNode;\n      this._printSemicolonBeforeNextToken = _printSemicolonBeforeNextToken;\n    } else {\n      this.source(\"start\", comment.loc);\n      this._append(val, isBlockComment);\n    }\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n    nextToken?: Token,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment, nextToken);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine !== commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumMember(node);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n                ? COMMENT_SKIP_NEWLINE.TRAILING\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  addDeprecatedGenerators(Printer);\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer, occurrenceCount: number, last: boolean) {\n  this.token(\",\", false, occurrenceCount);\n  if (!last) this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AAUA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAAI,kBAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AAGoC;EAflCM,YAAY;EACZC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAc,IAAAT,EAAA;AAmBhB,MAAMU,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,gCAAgC,GAAG,yBAAyB;AAElE,SAASC,gBAAgBA,CAACC,CAAY,EAAE;EACtC,OAAOA,CAAC,CAACC,IAAI,KAAK,aAAa,IAAIJ,WAAW,CAACK,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC;AAC9D;AAEA,MAAM;EAAEC;AAAY,CAAC,GAAGpB,CAAC;AAmFzB,MAAMqB,OAAO,CAAC;EACZC,WAAWA,CACTC,MAAc,EACdC,GAAc,EACdC,MAAgB,EAChBC,YAAqB,EACrB;IAAA,KAgBFC,kBAAkB,GAAY,KAAK;IAAA,KA0BnCC,YAAY,GAAW,CAAC;IAAA,KAExBC,OAAO,GAAY,IAAI;IAAA,KACvBC,aAAa,GAAkB,IAAI;IAAA,KAGnCC,YAAY,GAAW,IAAI;IAAA,KAC3BC,OAAO,GAAW,CAAC;IAAA,KACnBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,0BAA0B,GAAkB,IAAI;IAAA,KAChDC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,CAAY,CAAC;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,YAAY,GAAG,KAAK;IAAA,KACpBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IAAA,KACpCC,QAAQ,GAAa,IAAI;IAAA,KAEzBC,sBAAsB,GAAG,IAAI,CAACC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;IAAA,KAiD1DC,6BAA6B,GAAW,CAAC,CAAC;IAAA,KAC1CC,8BAA8B,GAAW,CAAC,CAAC;IAjHzC,IAAI,CAAC5B,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACM,OAAO,GAAGJ,MAAM;IACrB,IAAI,CAACK,aAAa,GAAGJ,YAAY;IAEjC,IAAI,CAACO,aAAa,GAAGV,MAAM,CAAC6B,MAAM,CAACC,KAAK,CAACC,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAG/B,GAAG,oBAAHA,GAAG,CAAE+B,SAAS;IAE/B,IAAI,CAACC,IAAI,GAAG,IAAIC,eAAM,CAACjC,GAAG,EAAED,MAAM,CAAC6B,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD;EAMAK,qBAAqBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC/B,kBAAkB,EAAE,OAAO,MAAM,CAAC,CAAC;IAC5C,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAC9B,OAAO,MAAM;MACX,IAAI,CAACA,kBAAkB,GAAG,KAAK;IACjC,CAAC;EACH;EAEAgC,cAAcA,CAAA,EAAG;IACf,MAAMC,qBAAqB,GAAG,IAAI,CAACjC,kBAAkB;IACrD,MAAMkC,4BAA4B,GAAG,IAAI,CAACzB,0BAA0B;IACpE,IACEwB,qBAAqB,KAAK,KAAK,IAC/BC,4BAA4B,KAAK,IAAI,EACrC;MACA,OAAO,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAClC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACS,0BAA0B,GAAG,IAAI;IACtC,OAAO,MAAM;MACX,IAAI,CAACT,kBAAkB,GAAGiC,qBAAqB;MAC/C,IAAI,CAACxB,0BAA0B,GAAGyB,4BAA4B;IAChE,CAAC;EACH;EA0BAC,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,IAAI,CAACxC,MAAM,CAACyC,cAAc,EAAE;MAC9B,IAAI,CAAClB,QAAQ,GAAG,IAAImB,kBAAQ,CAACF,GAAG,EAAE,IAAI,CAAClC,OAAO,EAAE,IAAI,CAACC,aAAa,CAAC;IACrE;IACA,IAAI,CAACoC,KAAK,CAACH,GAAG,CAAC;IACf,IAAI,CAACI,mBAAmB,CAAC,CAAC;IAE1B,OAAO,IAAI,CAACX,IAAI,CAACY,GAAG,CAAC,CAAC;EACxB;EAMAhB,MAAMA,CAAA,EAAS;IACb,MAAM;MAAE7B;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACyC,cAAc,IAAIzC,MAAM,CAAC8C,OAAO,IAAI9C,MAAM,CAAC+C,OAAO,EAAE;MAC7D;IACF;IAEA,IAAI,CAACtC,OAAO,EAAE;EAChB;EAMAuC,MAAMA,CAAA,EAAS;IACb,MAAM;MAAEhD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACyC,cAAc,IAAIzC,MAAM,CAAC8C,OAAO,IAAI9C,MAAM,CAAC+C,OAAO,EAAE;MAC7D;IACF;IAEA,IAAI,CAACtC,OAAO,EAAE;EAChB;EAmBAwC,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,CAAC,CAAC;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,GAAoB,CAAC;MACrC,IAAI,CAACvC,iBAAiB,GAAG,KAAK;MAC9B;IACF;IACA,IAAI,IAAI,CAACW,QAAQ,EAAE;MACjB,MAAM6B,IAAI,GAAG,IAAI,CAAC5C,YAAY;MAC9B,IAAI4C,IAAI,CAACC,KAAK,IAAI,IAAI,IAAID,IAAI,CAACE,GAAG,IAAI,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAACgC,UAAU,CAACH,IAAI,EAAE,GAAG,CAAC,EAAE;UAExC,IAAI,CAACzB,6BAA6B,GAAG,IAAI,CAACM,IAAI,CAACuB,cAAc,CAAC,CAAC;UAC/D;QACF;QACA,MAAMC,OAAO,GAAG,IAAI,CAAClC,QAAQ,CAACmC,UAAU,CAAC,IAAI,CAAClD,YAAY,CAAC;QAC3D,IAAI,CAACmD,UAAU,CAAC,IAAI,CAACrD,OAAO,CAACmD,OAAO,CAACA,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC6B,GAAG,CAACP,KAAK,CAAC;MACtE;IACF;IACA,IAAI,CAACQ,MAAM,GAAoB,CAAC;IAChC,IAAI,CAACjD,iBAAiB,GAAG,KAAK;EAChC;EAMAkD,UAAUA,CAACV,IAAY,EAAQ;IAC7B,IAAI,IAAI,CAACpD,MAAM,CAAC+D,QAAQ,EAAE;MACxB,IAAI,CAAC9B,IAAI,CAAC+B,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEb,IAAI,CAACQ,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACM,SAAK,IAAI,CAAC;EACjB;EAEAC,WAAWA,CAACf,IAAY,EAAQ;IAC9B,IAAI,CAACa,gBAAgB,CAAC,KAAK,EAAEb,IAAI,CAACQ,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACM,SAAK,GAAI,CAAC;EACjB;EAMAE,KAAKA,CAAClB,KAAc,GAAG,KAAK,EAAQ;IAClC,MAAM;MAAElD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAAC8C,OAAO,IAAI9C,MAAM,CAACyC,cAAc,EAAE;IAE7C,IAAIS,KAAK,EAAE;MACT,IAAI,CAACmB,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI,IAAI,CAACpC,IAAI,CAACqC,UAAU,CAAC,CAAC,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,CAAC,CAAC;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAACtE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACuE,wBAAwB,CAACF,GAAG,CAAC;IAElC,IAAI,CAAC9B,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAACrB,QAAQ,EAAE,IAAI,CAACsD,sBAAsB,CAACH,GAAG,CAAC;IAGnD,IACE,IAAI,CAACxD,aAAa,IACjB,IAAI,CAACC,YAAY,IAAIuD,GAAG,CAACI,UAAU,CAAC,CAAC,CAAC,OAAqB,EAC5D;MACA,IAAI,CAACT,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAACU,OAAO,CAACL,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAACxD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACN,iBAAiB,GAAG+D,qBAAqB;EAChD;EAMAK,MAAMA,CAACN,GAAW,EAAEM,MAAe,EAAQ;IAEzC,SAASC,mBAAmBA,CAACP,GAAW,EAAE;MACxC,IAAIA,GAAG,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,GAAG,CAACI,UAAU,CAAC,CAAC,CAAC,OAAqB,EAAE;QAC5D,MAAMI,UAAU,GAAGR,GAAG,CAACI,UAAU,CAAC,CAAC,CAAC;QACpC,OACEI,UAAU,OAAyB,IACnCA,UAAU,QAAyB,IACnCA,UAAU,QAAyB;MAEvC;MACA,OAAO,KAAK;IACd;IACA,IAAI,CAACT,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAACzD,gBAAgB,GACnBkE,MAAM,CAACC,SAAS,CAACJ,MAAM,CAAC,IACxB,CAACC,mBAAmB,CAACP,GAAG,CAAC,IACzB,CAACtF,mBAAmB,CAACO,IAAI,CAAC+E,GAAG,CAAC,IAC9B,CAACrF,oBAAoB,CAACM,IAAI,CAAC+E,GAAG,CAAC,IAC/BA,GAAG,CAACI,UAAU,CAACJ,GAAG,CAAC3C,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAaAmC,KAAKA,CAACQ,GAAW,EAAEW,YAAY,GAAG,KAAK,EAAEC,eAAe,GAAG,CAAC,EAAQ;IAClE,IAAI,CAACjF,YAAY,GAAG,CAAC;IAErB,IAAI,CAACuE,wBAAwB,CAACF,GAAG,EAAEY,eAAe,CAAC;IAEnD,IAAI,CAAC1C,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAACrB,QAAQ,EAAE,IAAI,CAACsD,sBAAsB,CAACH,GAAG,EAAEY,eAAe,CAAC;IAEpE,MAAMC,QAAQ,GAAG,IAAI,CAACf,WAAW,CAAC,CAAC;IACnC,MAAMgB,QAAQ,GAAGd,GAAG,CAACI,UAAU,CAAC,CAAC,CAAC;IAClC,IACGS,QAAQ,OAA8B,KAGpCb,GAAG,KAAK,IAAI,IAEXc,QAAQ,OAAuB,CAAC,IAEnCA,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAACvE,gBAAiB,EACrD;MACA,IAAI,CAACoD,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAACU,OAAO,CAACL,GAAG,EAAEW,YAAY,CAAC;IAC/B,IAAI,CAACzE,iBAAiB,GAAG,KAAK;EAChC;EAEA6E,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAACrF,YAAY,GAAG,CAAC;IAErB,MAAMqE,GAAG,GAAGiB,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC;IACrC,IAAI,CAACd,wBAAwB,CAACF,GAAG,CAAC;IAElC,IAAI,CAAC9B,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAACrB,QAAQ,EAAE,IAAI,CAACsD,sBAAsB,CAACH,GAAG,CAAC;IAEnD,MAAMa,QAAQ,GAAG,IAAI,CAACf,WAAW,CAAC,CAAC;IACnC,IAEGkB,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAACzE,gBAAiB,EACjD;MACA,IAAI,CAACoD,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAAClB,WAAW,CAACuC,IAAI,CAAC;IACtB,IAAI,CAAC9E,iBAAiB,GAAG,KAAK;EAChC;EAQAiF,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAE5C,KAAe,EAAQ;IAC5C,IAAI4C,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAAC5C,KAAK,EAAE;MACV,IAAI,IAAI,CAAClD,MAAM,CAAC+F,WAAW,IAAI,IAAI,CAAC/F,MAAM,CAAC8C,OAAO,EAAE;MAEpD,IAAI,IAAI,CAAC9C,MAAM,CAAC+C,OAAO,EAAE;QACvB,IAAI,CAACqB,KAAK,CAAC,CAAC;QACZ;MACF;IACF;IAEA,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAAC7D,IAAI,CAAC+D,eAAe,CAAC,CAAC;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA;EACF;EAEAC,QAAQA,CAACT,IAAY,EAAW;IAC9B,OAAO,IAAI,CAAClB,WAAW,CAAC,CAAC,KAAKkB,IAAI;EACpC;EAEAlB,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAACvC,IAAI,CAACuC,WAAW,CAAC,CAAC;EAChC;EAEA4B,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAACnE,IAAI,CAACmE,sBAAsB,CAAC,CAAC;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAACpE,IAAI,CAACoE,qBAAqB,CAAC,CAAC;EACnC;EAEAC,WAAWA,CAAC1C,GAAoB,EAAE2C,EAAc,EAAE;IAChD,IAAI,CAAC3C,GAAG,EAAE;MACR2C,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAE5C,GAAG,CAAC;IAE3B,IAAI,CAAC3B,IAAI,CAACqE,WAAW,CAAC1C,GAAG,EAAE2C,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAE9C,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAAC4C,QAAQ,CAACE,IAAI,EAAE9C,GAAG,CAAC;IAExB,IAAI,CAAC3B,IAAI,CAACwE,MAAM,CAACC,IAAI,EAAE9C,GAAG,CAAC;EAC7B;EAEAK,gBAAgBA,CACdyC,IAAqB,EACrB9C,GAAoB,EACpB+C,YAAoB,EACd;IACN,IAAI,CAAC/C,GAAG,IAAI,IAAI,CAAC5D,MAAM,CAACyC,cAAc,EAAE;IAExC,IAAI,CAAC+D,QAAQ,CAACE,IAAI,EAAE9C,GAAG,CAAC;IAExB,IAAI,CAAC3B,IAAI,CAACgC,gBAAgB,CAACyC,IAAI,EAAE9C,GAAG,EAAE+C,YAAY,CAAC;EACrD;EAEAC,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAAC7E,IAAI,CAAC8E,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAAC/E,IAAI,CAACgF,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEAxC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACR,MAAM,GAAgB,CAAC;EAC9B;EAEAqC,QAAQA,CAAA,EAAS;IACf,IAAI,CAACrC,MAAM,GAAmB,CAAC;EACjC;EAEAgB,sBAAsBA,CAACH,GAAW,EAAEY,eAAuB,GAAG,CAAC,EAAQ;IAGrE,MAAMpB,KAAK,GAAG,IAAI,CAAC3C,QAAQ,CAAC4F,YAAY,CACtC,IAAI,CAAC3G,YAAY,EACjBkE,GAAG,EACHY,eACF,CAAC;IACD,IAAIpB,KAAK,EAAE,IAAI,CAACP,UAAU,CAACO,KAAK,CAACN,GAAG,CAACP,KAAK,CAAC;IAE3C,IACE,IAAI,CAACzB,8BAA8B,KAAK,CAAC,CAAC,IAC1C,IAAI,CAACA,8BAA8B,KAAK,IAAI,CAACK,IAAI,CAACuB,cAAc,CAAC,CAAC,EAClE;MACA,IAAI,CAACvB,IAAI,CAACmF,UAAU,GAAoB,CAAC;MACzC,IAAI,CAAClG,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;IAC3B;IACA,IAAI,CAACS,8BAA8B,GAAG,CAAC,CAAC;IACxC,IAAI,CAACD,6BAA6B,GAAG,CAAC,CAAC;EACzC;EAEAoD,OAAOA,CAACL,GAAW,EAAEW,YAAqB,EAAQ;IAChD,IAAI,CAACgC,YAAY,CAAC3C,GAAG,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAAC7C,IAAI,CAACqF,MAAM,CAAC5C,GAAG,EAAEW,YAAY,CAAC;IAGnC,IAAI,CAACnE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAgC,WAAWA,CAACuC,IAAY,EAAQ;IAC9B,IAAI,CAAC2B,YAAY,CAAC3B,IAAI,CAAC;IAEvB,IAAI,CAACzD,IAAI,CAACmF,UAAU,CAAC1B,IAAI,CAAC;IAG1B,IAAI,CAACxE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEA0C,MAAMA,CAAC6B,IAAY,EAAE;IACnB,IAAI,CAAC2B,YAAY,CAAC3B,IAAI,CAAC;IAEvB,IAAI,CAACzD,IAAI,CAACsF,KAAK,CAAC7B,IAAI,CAAC;IAErB,IAAI,CAACxE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAoG,YAAYA,CAACG,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAAC/G,OAAO,IACZ+G,SAAS,OAAuB,IAChC,IAAI,CAACrB,QAAQ,GAAmB,CAAC,EACjC;MACA,IAAI,CAAClE,IAAI,CAACwF,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAC/C;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAAC/G,OAAO,IACZ+G,SAAS,OAAuB,IAChC,IAAI,CAACrB,QAAQ,GAAmB,CAAC,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAyB,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC7H,MAAM,CAAC+F,WAAW,EAAE;IAG9B,MAAM+B,KAAK,GAAGD,IAAI,GAAG,IAAI,CAAC5F,IAAI,CAACuB,cAAc,CAAC,CAAC;IAE/C,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;EACF;EAEAM,QAAQA,CAACE,IAAqB,EAAE9C,GAAS,EAAE;IACzC,MAAM;MAAE5D;IAAO,CAAC,GAAG,IAAI;IACvB,IAAI,CAACA,MAAM,CAACyC,cAAc,EAAE;MAC1B,IAAIzC,MAAM,CAAC+F,WAAW,IAAInC,GAAG,YAAHA,GAAG,CAAG8C,IAAI,CAAC,EAAE;QACrC,IAAI,CAACkB,OAAO,CAAChE,GAAG,CAAC8C,IAAI,CAAC,CAACmB,IAAI,CAAC;MAC9B;MACA;IACF;IAGA,MAAMf,GAAG,GAAGlD,GAAG,oBAAHA,GAAG,CAAG8C,IAAI,CAAC;IACvB,IAAII,GAAG,IAAI,IAAI,EAAE,IAAI,CAACnD,UAAU,CAACmD,GAAG,CAAC;EACvC;EAEAnD,UAAUA,CAAC;IAAEkE,IAAI;IAAEE,MAAM;IAAEC;EAAW,CAAC,EAAE;IACvC,MAAMF,KAAK,GAAGD,IAAI,GAAG,IAAI,CAAC5F,IAAI,CAACuB,cAAc,CAAC,CAAC;IAC/C,IAAIsE,KAAK,GAAG,CAAC,IAAI,IAAI,CAAClH,iBAAiB,EAAE;MAGvC;IACF;IAEA,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;IAEA,MAAM+B,WAAW,GACfH,KAAK,GAAG,CAAC,GAAGC,MAAM,GAAGA,MAAM,GAAG,IAAI,CAAC9F,IAAI,CAACiG,gBAAgB,CAAC,CAAC;IAC5D,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,MAAME,MAAM,GAAG,IAAI,CAAC5H,aAAa,GAC7B,IAAI,CAACA,aAAa,CACf6H,KAAK,CAACJ,KAAK,GAAGC,WAAW,EAAED,KAAK,CAAC,CAEjCK,OAAO,CAAC,+DAAsC,EAAE,GAAG,CAAC,GACvD,GAAG,CAACC,MAAM,CAACL,WAAW,CAAC;MAC3B,IAAI,CAAClD,OAAO,CAACoD,MAAM,EAAE,KAAK,CAAC;IAC7B;EACF;EAMAT,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAAChH,aAAa,GAAG,IAAI,CAACD,OAAO;EAC1C;EAEA8H,mBAAmBA,CAACnF,IAAY,EAAE;IAgBhC,IAAI,CAACxC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC+B,KAAK,CAACS,IAAI,CAAC;EAClB;EAEAT,KAAKA,CACHS,IAAmB,EACnBuB,qBAA+B,EAG/B6D,0BAAmC,EACnC;IAAA,IAAAC,WAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACA,IAAI,CAACvF,IAAI,EAAE;IAEX,IAAI,CAAC/B,iBAAiB,GAAG,KAAK;IAE9B,MAAMuH,QAAQ,GAAGxF,IAAI,CAAC1D,IAAI;IAC1B,MAAMM,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAM6I,UAAU,GAAG7I,MAAM,CAAC+C,OAAO;IACjC,IAEEK,IAAI,CAAC0F,QAAQ,EACb;MACA9I,MAAM,CAAC+C,OAAO,GAAG,IAAI;IACvB;IAEA,MAAMgG,WAAW,GACf,IAAI,CACFH,QAAQ,CAMT;IACH,IAAIG,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACtB,wBAAwBC,IAAI,CAACC,SAAS,CACpCP,QACF,CAAC,qBAAqBM,IAAI,CAACC,SAAS,CAAC/F,IAAI,CAACrD,WAAW,CAACqJ,IAAI,CAAC,EAC7D,CAAC;IACH;IAEA,MAAMC,MAAM,GAAG,IAAI,CAAC7I,YAAY;IAChC,IAAI,CAACA,YAAY,GAAG4C,IAAI;IAExB,IAAI,IAAI,CAAC7B,QAAQ,EAAE;MACjB,IAAI,CAACK,8BAA8B,GAAG,IAAI,CAACD,6BAA6B;IAC1E;IAEA,MAAM2H,QAAQ,GAAG,IAAI,CAAC3I,UAAU;IAChC,IAAI,CAACA,UAAU,GAAGyC,IAAI,CAACQ,GAAG,IAAI,IAAI;IAClC,IAAI,CAAChB,mBAAmB,CAAC,IAAI,CAACjC,UAAU,IAAI,CAAC2I,QAAQ,CAAC;IAEtD,MAAMC,aAAa,IAAAd,WAAA,GAAGrF,IAAI,CAACoG,KAAK,qBAAVf,WAAA,CAAYc,aAAoC;IACtE,IAAIE,iBAAiB,GAClBF,aAAa,IAAIvJ,MAAM,CAACyC,cAAc,IACtC8G,aAAa,IACZvJ,MAAM,CAAC0J,oBAAoB,IAC3Bd,QAAQ,KAAK,oBAAqB,IACpC/I,WAAW,CACTuD,IAAI,EACJiG,MAAM,EACN,IAAI,CAAChJ,YAAY,EACjB,IAAI,CAACD,kBAAkB,EACvBJ,MAAM,CAACyC,cAAc,GAAG,IAAI,CAACjB,sBAAsB,GAAGwH,SACxD,CAAC;IAEH,IACE,CAACS,iBAAiB,IAClBF,aAAa,KAAAb,qBAAA,GACbtF,IAAI,CAACuG,eAAe,aAApBjB,qBAAA,CAAsB3G,MAAM,IAC5BqB,IAAI,CAACuG,eAAe,CAAC,CAAC,CAAC,CAACjK,IAAI,KAAK,cAAc,EAC/C;MACA,MAAMkK,UAAU,GAAGP,MAAM,oBAANA,MAAM,CAAE3J,IAAI;MAC/B,QAAQkK,UAAU;QAChB,KAAK,qBAAqB;QAC1B,KAAK,oBAAoB;QACzB,KAAK,sBAAsB;QAC3B,KAAK,iBAAiB;UACpB;QACF,KAAK,gBAAgB;QACrB,KAAK,wBAAwB;QAC7B,KAAK,eAAe;UAClB,IAAIP,MAAM,CAACQ,MAAM,KAAKzG,IAAI,EAAE;QAE9B;UACEqG,iBAAiB,GAAG,IAAI;MAC5B;IACF;IAEA,IAAIK,mBAAmB,GAAG,KAAK;IAC/B,IACE,CAACL,iBAAiB,IAClB,IAAI,CAAC7I,iBAAiB,KACrB,CAAA+H,sBAAA,GAAAvF,IAAI,CAACuG,eAAe,aAApBhB,sBAAA,CAAsBoB,IAAI,CAACvK,gBAAgB,CAAC,IAC1C,IAAI,CAACQ,MAAM,CAAC+F,WAAW,IACtB3C,IAAI,CAACQ,GAAG,IACRR,IAAI,CAACQ,GAAG,CAACP,KAAK,CAACwE,IAAI,GAAG,IAAI,CAAC5F,IAAI,CAACuB,cAAc,CAAC,CAAE,CAAC,EACtD;MACAiG,iBAAiB,GAAG,IAAI;MACxBK,mBAAmB,GAAG,IAAI;IAC5B;IAEA,IAAIxH,4BAA4B;IAChC,IAAI0H,4BAA4B;IAChC,IAAI,CAACP,iBAAiB,EAAE;MACtB9E,qBAAqB,KAArBA,qBAAqB,GACnB0E,MAAM,IACN,IAAI,CAACxI,0BAA0B,KAAKwI,MAAM,IAC1C5K,CAAC,CAACwL,WAAW,CAACZ,MAAM,EAAEjG,IAAI,CAAC;MAC7B,IAAIuB,qBAAqB,EAAE;QAAA,IAAAuF,qBAAA;QACzB,KAAAA,qBAAA,GAAI9G,IAAI,CAAC+G,gBAAgB,aAArBD,qBAAA,CAAuBH,IAAI,CAACvK,gBAAgB,CAAC,EAAE;UACjD,IAAIV,YAAY,CAACsE,IAAI,CAAC,EAAEqG,iBAAiB,GAAG,IAAI;QAClD,CAAC,MAAM;UACLnH,4BAA4B,GAAG,IAAI,CAACzB,0BAA0B;UAC9D,IAAI,CAACA,0BAA0B,GAAGuC,IAAI;QACxC;MACF;IACF;IAEA,IAAIqG,iBAAiB,EAAE;MACrB,IAAI,CAACvF,SAAK,GAAI,CAAC;MACf,IAAI4F,mBAAmB,EAAE,IAAI,CAACjI,MAAM,CAAC,CAAC;MACtC,IAAI,CAACR,iBAAiB,GAAG,KAAK;MAC9B,IAAI,IAAI,CAACjB,kBAAkB,EAAE;QAC3B4J,4BAA4B,GAAG,IAAI;QACnC,IAAI,CAAC5J,kBAAkB,GAAG,KAAK;MACjC;MACAkC,4BAA4B,GAAG,IAAI,CAACzB,0BAA0B;MAC9D,IAAI,CAACA,0BAA0B,GAAG,IAAI;IACxC;IAEA,IAAI,CAACO,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAACgJ,qBAAqB,CAAChH,IAAI,EAAEiG,MAAM,CAAC;IAExC,MAAMzF,GAAG,GAAGgF,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGxF,IAAI,CAACQ,GAAG;IAE3E,IAAI,CAAC0C,WAAW,CACd1C,GAAG,EAEHmF,WAAW,CAACrH,IAAI,CAAC,IAAI,EAAE0B,IAAI,EAAEiG,MAAM,CACrC,CAAC;IAED,IAAII,iBAAiB,EAAE;MACrB,IAAI,CAACY,sBAAsB,CAACjH,IAAI,EAAEiG,MAAM,CAAC;MACzC,IAAIS,mBAAmB,EAAE;QACvB,IAAI,CAAC9G,MAAM,CAAC,CAAC;QACb,IAAI,CAAC6C,OAAO,CAAC,CAAC;MAChB;MACA,IAAI,CAAC3B,SAAK,GAAI,CAAC;MACf,IAAI,CAACtD,iBAAiB,GAAG+D,qBAAqB;MAC9C,IAAIqF,4BAA4B,EAAE,IAAI,CAAC5J,kBAAkB,GAAG,IAAI;IAClE,CAAC,MAAM,IAAIuE,qBAAqB,IAAI,CAAC,IAAI,CAAC/D,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACyJ,sBAAsB,CAACjH,IAAI,EAAEiG,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACgB,sBAAsB,CAACjH,IAAI,EAAEiG,MAAM,EAAEb,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAAChI,YAAY,GAAG6I,MAAM;IAC1BrJ,MAAM,CAAC+C,OAAO,GAAG8F,UAAU;IAC3B,IAAI,CAAClI,UAAU,GAAG2I,QAAQ;IAE1B,IAAIhH,4BAA4B,KAAK0G,SAAS,EAAE;MAC9C,IAAI,CAACnI,0BAA0B,GAAGyB,4BAA4B;IAChE;IAEA,IAAI,CAACjB,iBAAiB,GAAG,KAAK;EAChC;EAEAuB,mBAAmBA,CAAC0H,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,IAAI,CAAC5J,UAAU,EAAE,IAAI,CAAC6J,qBAAqB,CAAC,CAAC;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACzJ,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAM2J,OAAO,GAAG,IAAI,CAACzK,MAAM,CAAC0K,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACEjL,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAE6K;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAC1J,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAM2J,OAAO,GAAG,IAAI,CAACzK,MAAM,CAAC4K,qBAAqB;IACjD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACEjL,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAE6K;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAI,cAAcA,CACZzH,IAKa,EACO;IACpB,MAAMoG,KAAK,GAAGpG,IAAI,CAACoG,KAAK;IACxB,IACE,CAAAA,KAAK,oBAALA,KAAK,CAAEsB,GAAG,KAAI,IAAI,IAClBtB,KAAK,CAACuB,QAAQ,IAAI,IAAI,IACtB3H,IAAI,CAACxD,KAAK,KAAK4J,KAAK,CAACuB,QAAQ,EAC7B;MAEA,OAAOvB,KAAK,CAACsB,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCC,SAAmB,EACnBrJ,MAAgB,EAChBsJ,SAAyC,EACzCC,sBAAgC,EAChCC,WAA6C,EAC7CC,QAAuC,EACvC9C,0BAAmC,EACnC;IACA,IAAI,EAACyC,KAAK,YAALA,KAAK,CAAElJ,MAAM,GAAE;IAEpB,IAAIF,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC7B,MAAM,CAAC+F,WAAW,EAAE;MAAA,IAAAwF,YAAA;MAC7C,MAAMC,SAAS,IAAAD,YAAA,GAAGN,KAAK,CAAC,CAAC,CAAC,CAACrH,GAAG,qBAAZ2H,YAAA,CAAclI,KAAK,CAACwE,IAAI;MAC1C,IAAI2D,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,IAAI,CAACvJ,IAAI,CAACuB,cAAc,CAAC,CAAC,EAAE;QACjE3B,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAIA,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IAEzB,MAAM4J,WAA+B,GAAG;MACtCJ,WAAW,EAAEA,WAAW;MACxBK,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,cAAc,GAAGR,SAAS,oBAATA,SAAS,CAAEzJ,IAAI,CAAC,IAAI,CAAC;IAE5C,MAAMkK,GAAG,GAAGX,KAAK,CAAClJ,MAAM;IACxB,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,GAAG,EAAE9F,CAAC,EAAE,EAAE;MAC5B,MAAM1C,IAAI,GAAG6H,KAAK,CAACnF,CAAC,CAAC;MACrB,IAAI,CAAC1C,IAAI,EAAE;MAEX,IAAI8H,SAAS,EAAE,IAAI,CAACW,aAAa,CAAC/F,CAAC,KAAK,CAAC,EAAE2F,WAAW,CAAC;MAEvD,IAAI,CAAC9I,KAAK,CAACS,IAAI,EAAE4F,SAAS,EAAER,0BAA0B,IAAI,CAAC,CAAC;MAE5D8C,QAAQ,YAARA,QAAQ,CAAGlI,IAAI,EAAE0C,CAAC,CAAC;MAEnB,IAAI6F,cAAc,IAAI,IAAI,EAAE;QAC1B,IAAI7F,CAAC,GAAG8F,GAAG,GAAG,CAAC,EAAED,cAAc,CAAC7F,CAAC,EAAE,KAAK,CAAC,CAAC,KACrC,IAAIsF,sBAAsB,EAAEO,cAAc,CAAC7F,CAAC,EAAE,IAAI,CAAC;MAC1D;MAEA,IAAIoF,SAAS,EAAE;QAAA,IAAAY,sBAAA;QACb,IAAI,GAAAA,sBAAA,GAAC1I,IAAI,CAAC+G,gBAAgB,aAArB2B,sBAAA,CAAuB/J,MAAM,GAAE;UAClC,IAAI,CAACX,gBAAgB,GAAG,CAAC;QAC3B;QAEA,IAAI0E,CAAC,GAAG,CAAC,KAAK8F,GAAG,EAAE;UACjB,IAAI,CAAC/F,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAAkG,aAAA;UACL,MAAMC,QAAQ,GAAGf,KAAK,CAACnF,CAAC,GAAG,CAAC,CAAC;UAC7B2F,WAAW,CAACC,iBAAiB,GAAG,EAAAK,aAAA,GAAAC,QAAQ,CAACpI,GAAG,qBAAZmI,aAAA,CAAc1I,KAAK,CAACwE,IAAI,KAAI,CAAC;UAE7D,IAAI,CAACgE,aAAa,CAAC,IAAI,EAAEJ,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAI5J,MAAM,EAAE,IAAI,CAACmB,MAAM,CAAC,CAAC;EAC3B;EAEAiJ,wBAAwBA,CAAC7I,IAAY,EAAE;IACrC,MAAMvB,MAAM,GAAGuB,IAAI,CAACuG,eAAe,IAAIvG,IAAI,CAACuG,eAAe,CAAC5H,MAAM,GAAG,CAAC;IACtE,IAAIF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACc,KAAK,CAACS,IAAI,CAAC;IAChB,IAAIvB,MAAM,EAAE,IAAI,CAACmB,MAAM,CAAC,CAAC;EAC3B;EAEAkJ,UAAUA,CAAC7C,MAA8C,EAAE;IACzD,MAAMjG,IAAI,GAAGiG,MAAM,CAAC8C,IAAI;IAExB,IAAI/I,IAAI,CAAC1D,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAAC0E,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAACzB,KAAK,CAACS,IAAI,CAAC;EAClB;EAEAiH,sBAAsBA,CAACjH,IAAY,EAAEiG,MAAe,EAAE+C,UAAmB,EAAE;IACzE,MAAM;MAAEC,aAAa;MAAElC;IAAiB,CAAC,GAAG/G,IAAI;IAIhD,IAAIiJ,aAAa,YAAbA,aAAa,CAAEtK,MAAM,EAAE;MACzB,IAAI,CAACuK,cAAc,IAEjBD,aAAa,EACbjJ,IAAI,EACJiG,MAAM,EACN+C,UACF,CAAC;IACH;IACA,IAAIjC,gBAAgB,YAAhBA,gBAAgB,CAAEpI,MAAM,EAAE;MAC5B,IAAI,CAACuK,cAAc,IAEjBnC,gBAAgB,EAChB/G,IAAI,EACJiG,MAAM,EACN+C,UACF,CAAC;IACH;EACF;EAEAhC,qBAAqBA,CAAChH,IAAY,EAAEiG,MAAc,EAAE;IAClD,MAAMkD,QAAQ,GAAGnJ,IAAI,CAACuG,eAAe;IACrC,IAAI,EAAC4C,QAAQ,YAARA,QAAQ,CAAExK,MAAM,GAAE;IACvB,IAAI,CAACuK,cAAc,IAAuBC,QAAQ,EAAEnJ,IAAI,EAAEiG,MAAM,CAAC;EACnE;EAEAzE,wBAAwBA,CACtB4H,YAAoB,EACpBC,wBAAiC,EACjC;IACA,IAAI,IAAI,CAACpL,iBAAiB,EAAE;MAAA,IAAAqL,cAAA;MAC1B,IAAI,CAACC,kBAAkB,EAAAD,cAAA,GACrB,IAAI,CAACnL,QAAQ,qBAAbmL,cAAA,CAAevF,YAAY,CACzB,IAAI,CAAC3G,YAAY,EACjBgM,YAAY,EACZC,wBACF,CACF,CAAC;IACH;IACA,IAAI,CAACpL,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEAqL,kBAAkBA,CAACC,SAAiB,EAAE;IACpC,MAAMxJ,IAAI,GAAG,IAAI,CAAC5C,YAAY;IAC9B,MAAM+L,QAAQ,GAAGnJ,IAAI,CAACiJ,aAAa;IACnC,IAAI,EAACE,QAAQ,YAARA,QAAQ,CAAExK,MAAM,GAAE;IAEvB,MAAM8K,QAAQ,GAAG,IAAI,CAAC1G,QAAQ,GAAgB,CAAC;IAC/C,MAAMtE,MAAM,GAAG,IAAI,CAACP,oBAAoB;IACxC,MAAMwL,oBAAoB,GAAG,IAAI,CAAC/L,gBAAgB,CAACgM,IAAI;IACvD,IAAIlL,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACyK,cAAc,IAEjBC,QAAQ,EACRnJ,IAAI,EACJ4F,SAAS,EACTA,SAAS,EACT4D,SACF,CAAC;IACD,IAAIC,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAAC/L,gBAAgB,CAACgM,IAAI,EAAE;MACnE,IAAI,CAAC3I,KAAK,CAAC,CAAC;IACd;IACA,IAAIvC,MAAM,EAAE,IAAI,CAACmB,MAAM,CAAC,CAAC;EAC3B;EAEAgK,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAAC1L,oBAAoB,GAAG,KAAK;EACnC;EAEA2L,aAAaA,CACXhC,KAAe,EACfpJ,MAAgB,EAChB2G,0BAAmC,EACnC6C,WAAiD,EACjD;IACA,IAAI,CAACL,SAAS,CACZC,KAAK,EACL,IAAI,EACJpJ,MAAM,WAANA,MAAM,GAAI,KAAK,EACfmH,SAAS,EACTA,SAAS,EACTqC,WAAW,EACXrC,SAAS,EACTR,0BACF,CAAC;EACH;EAEA0E,SAASA,CACPC,KAAe,EACf/B,sBAAgC,EAChCF,SAAmB,EACnBrJ,MAAgB,EAChBsJ,SAAyC,EACzCG,QAAuC,EACvC;IACA,IAAI,CAACN,SAAS,CACZmC,KAAK,EACLjC,SAAS,EACTrJ,MAAM,EACNsJ,SAAS,WAATA,SAAS,GAAIiC,cAAc,EAC3BhC,sBAAsB,EACtBpC,SAAS,EACTsC,QACF,CAAC;EACH;EAEA+B,wBAAwBA,CAACC,OAAe,EAAkB;IACxD,IAAI,CAAC,IAAI,CAAC/L,QAAQ,EAAE,OAAO,IAAI;IAE/B,MAAMgM,YAAY,GAAG,IAAI,CAAChM,QAAQ,CAACiM,aAAa,CAAC,IAAI,CAAChN,YAAY,EAAE0D,KAAK,IACvE,IAAI,CAAC3C,QAAQ,CAACkM,eAAe,CAACvJ,KAAK,EAAEoJ,OAAO,CAC9C,CAAC;IACD,IAAIC,YAAY,IAAI,CAAC,EAAE,OAAO,IAAI;IAClC,OAAO,IAAI,CAAChM,QAAQ,CAACkM,eAAe,CAAC,IAAI,CAACnN,OAAO,CAACiN,YAAY,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EAC3E;EAEA1B,aAAaA,CAAC6B,OAAgB,EAAEC,IAAwB,EAAE;IACxD,MAAM3N,MAAM,GAAG,IAAI,CAACA,MAAM;IAG1B,IAAIA,MAAM,CAAC+F,WAAW,IAAI/F,MAAM,CAAC8C,OAAO,EAAE;IAI1C,IAAI9C,MAAM,CAAC+C,OAAO,EAAE;MAClB,IAAI,CAACqB,KAAK,CAAC,CAAC;MACZ;IACF;IAEA,IAAI,CAACsJ,OAAO,EAAE;MACZ;IACF;IAEA,MAAMlC,SAAS,GAAGmC,IAAI,CAACjC,iBAAiB;IACxC,MAAMkC,eAAe,GAAG,IAAI,CAACxM,gBAAgB;IAC7C,IAAIoK,SAAS,GAAG,CAAC,IAAIoC,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGrC,SAAS,GAAGoC,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAAChI,OAAO,CAACgI,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAAC5L,IAAI,CAACqC,UAAU,CAAC,CAAC,EAAE;MAa1B,IAAI,CAACuB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAiI,mBAAmBA,CACjBrD,OAAkB,EAClBmC,SAAiB,EACG;IAGpB,IAAInC,OAAO,CAACsD,MAAM,EAAE;IAEpB,IAAI,IAAI,CAAChN,gBAAgB,CAACiN,GAAG,CAACvD,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAAC7J,iBAAiB,IACtBrB,gCAAgC,CAACI,IAAI,CAAC8K,OAAO,CAAC7K,KAAK,CAAC,EACpD;MACA;IACF;IAEA,IAAIgN,SAAS,IAAI,IAAI,CAACrL,QAAQ,EAAE;MAC9B,MAAM0M,UAAU,GAAG,IAAI,CAAC1M,QAAQ,CAAC2M,IAAI,CACnC,IAAI,CAAC1N,YAAY,EACjB0D,KAAK,IAAIA,KAAK,CAACtE,KAAK,KAAK6K,OAAO,CAAC7K,KACnC,CAAC;MACD,IAAIqO,UAAU,IAAIA,UAAU,CAAC5K,KAAK,GAAGuJ,SAAS,CAACvJ,KAAK,EAAE;QACpD;MACF;IACF;IAEA,IAAI,CAACtC,gBAAgB,CAACoN,GAAG,CAAC1D,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAACzK,MAAM,CAACoO,kBAAkB,CAAC3D,OAAO,CAAC7K,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEA+K,aAAaA,CAACF,OAAkB,EAAE4D,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAAC1N,iBAAiB;IAC/C,MAAM2N,cAAc,GAAG9D,OAAO,CAAC/K,IAAI,KAAK,cAAc;IAItD,MAAM8O,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAACzN,iBAAiB;IAEzB,IACE4N,aAAa,IACb,IAAI,CAACvM,IAAI,CAACqC,UAAU,CAAC,CAAC,IACtB+J,YAAY,MAAiC,EAC7C;MACA,IAAI,CAACxI,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAM4I,YAAY,GAAG,IAAI,CAACjK,WAAW,CAAC,CAAC;IACvC,IACEiK,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,IACzCA,YAAY,OAA8B,EAC1C;MACA,IAAI,CAACrK,KAAK,CAAC,CAAC;IACd;IAEA,IAAIsK,GAAG;IACP,IAAIH,cAAc,EAAE;MAClBG,GAAG,GAAG,KAAKjE,OAAO,CAAC7K,KAAK,IAAI;MAC5B,IAAI,IAAI,CAACI,MAAM,CAAC6B,MAAM,CAAC8M,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMf,MAAM,IAAAe,YAAA,GAAGnE,OAAO,CAAC7G,GAAG,qBAAXgL,YAAA,CAAavL,KAAK,CAAC0E,MAAM;QACxC,IAAI8F,MAAM,EAAE;UACV,MAAMgB,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGjB,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEa,GAAG,GAAGA,GAAG,CAACrG,OAAO,CAACwG,YAAY,EAAE,IAAI,CAAC;QACvC;QACA,IAAI,IAAI,CAAC7O,MAAM,CAAC+C,OAAO,EAAE;UACvB2L,GAAG,GAAGA,GAAG,CAACrG,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QACrC,CAAC,MAAM;UACL,IAAI0G,UAAU,GAAG,IAAI,CAAC/O,MAAM,CAAC+F,WAAW,GACpC,CAAC,GACD,IAAI,CAAC9D,IAAI,CAACiG,gBAAgB,CAAC,CAAC;UAEhC,IAAI,IAAI,CAACP,aAAa,GAAgB,CAAC,IAAI,IAAI,CAAC3H,MAAM,CAAC+F,WAAW,EAAE;YAClEgJ,UAAU,IAAI,IAAI,CAACrH,UAAU,CAAC,CAAC;UACjC;UAEAgH,GAAG,GAAGA,GAAG,CAACrG,OAAO,CAAC,UAAU,EAAE,KAAK,GAAG,CAACC,MAAM,CAACyG,UAAU,CAAC,EAAE,CAAC;QAC9D;MACF;IACF,CAAC,MAAM,IAAI,CAACT,gBAAgB,EAAE;MAC5BI,GAAG,GAAG,KAAKjE,OAAO,CAAC7K,KAAK,EAAE;IAC5B,CAAC,MAAM;MAIL8O,GAAG,GAAG,KAAKjE,OAAO,CAAC7K,KAAK,IAAI;IAC9B;IAGA,IAAI,IAAI,CAACuB,YAAY,EAAE,IAAI,CAACkD,MAAM,CAAC,CAAC;IAEpC,IAAI,IAAI,CAAC9C,QAAQ,EAAE;MACjB,MAAM;QAAEK,8BAA8B;QAAED;MAA8B,CAAC,GACrE,IAAI;MACN,IAAI,CAACC,8BAA8B,GAAG,CAAC,CAAC;MACxC,IAAI,CAACD,6BAA6B,GAAG,CAAC,CAAC;MACvC,IAAI,CAAC8E,MAAM,CAAC,OAAO,EAAEgE,OAAO,CAAC7G,GAAG,CAAC;MACjC,IAAI,CAACmB,OAAO,CAAC2J,GAAG,EAAEH,cAAc,CAAC;MACjC,IAAI,CAAC5M,6BAA6B,GAAGA,6BAA6B;MAClE,IAAI,CAACC,8BAA8B,GAAGA,8BAA8B;IACtE,CAAC,MAAM;MACL,IAAI,CAAC6E,MAAM,CAAC,OAAO,EAAEgE,OAAO,CAAC7G,GAAG,CAAC;MACjC,IAAI,CAACmB,OAAO,CAAC2J,GAAG,EAAEH,cAAc,CAAC;IACnC;IAEA,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAACzI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAI2I,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAACxI,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEAyG,cAAcA,CACZ5M,IAAkB,EAClB6M,QAA8B,EAC9BnJ,IAAY,EACZiG,MAAe,EACf+C,UAAkB,GAAG,CAAC,EACtBQ,SAAiB,EACjB;IACA,MAAMoC,OAAO,GAAG5L,IAAI,CAACQ,GAAG;IACxB,MAAMgI,GAAG,GAAGW,QAAQ,CAACxK,MAAM;IAC3B,IAAIkN,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAAC3L,KAAK,CAACwE,IAAI,GAAG,CAAC;IACrD,MAAMsH,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAAC1L,GAAG,CAACuE,IAAI,GAAG,CAAC;IACjD,IAAIuH,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAMhK,YAAY,GAAG,IAAI,CAACzE,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAACiF,OAAO,CAACnE,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,GAAG,EAAE9F,CAAC,EAAE,EAAE;MAC5B,MAAM2E,OAAO,GAAG8B,QAAQ,CAACzG,CAAC,CAAC;MAE3B,MAAMwJ,WAAW,GAAG,IAAI,CAACxB,mBAAmB,CAACrD,OAAO,EAAEmC,SAAS,CAAC;MAChE,IAAI0C,WAAW,MAA6B,EAAE;QAC5CL,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAIxE,OAAO,CAAC7G,GAAG,IAAI0L,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAG9E,OAAO,CAAC7G,GAAG,CAACP,KAAK,CAACwE,IAAI;QAC/C,MAAM2H,cAAc,GAAG/E,OAAO,CAAC7G,GAAG,CAACN,GAAG,CAACuE,IAAI;QAC3C,IAAInI,IAAI,MAAyB,EAAE;UACjC,IAAImO,MAAM,GAAG,CAAC;UACd,IAAI/H,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAAC7D,IAAI,CAACqC,UAAU,CAAC,CAAC,KACrBmG,OAAO,CAAC/K,IAAI,KAAK,aAAa,IAC7B6P,gBAAgB,KAAKC,cAAc,CAAC,EACtC;cACA3B,MAAM,GAAGwB,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACLxB,MAAM,GAAG0B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBnK,YAAY,CAACwI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI3E,CAAC,GAAG,CAAC,KAAK8F,GAAG,EAAE;YACjBvG,YAAY,CACVoK,IAAI,CAACC,GAAG,CAACR,aAAa,GAAGE,QAAQ,EAAEC,qBAAqB,CAC1D,CAAC;YACDD,QAAQ,GAAGF,aAAa;UAC1B;QACF,CAAC,MAAM,IAAIxP,IAAI,MAAuB,EAAE;UACtC,MAAMmO,MAAM,GACV0B,gBAAgB,IAAIzJ,CAAC,KAAK,CAAC,GAAGoJ,aAAa,GAAGE,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBnK,YAAY,CAACwI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI3E,CAAC,GAAG,CAAC,KAAK8F,GAAG,EAAE;YACjBvG,YAAY,CAACoK,IAAI,CAACE,GAAG,CAAC,CAAC,EAAER,WAAW,GAAGC,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGD,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMtB,MAAM,GACV0B,gBAAgB,IAAIzJ,CAAC,KAAK,CAAC,GAAGqJ,WAAW,GAAG/C,UAAU,GAAGgD,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBnK,YAAY,CAACwI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;QACvD;MACF,CAAC,MAAM;QACLwE,MAAM,GAAG,KAAK;QACd,IAAIK,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAI1D,GAAG,KAAK,CAAC,EAAE;UACb,MAAMgE,UAAU,GAAGnF,OAAO,CAAC7G,GAAG,GAC1B6G,OAAO,CAAC7G,GAAG,CAACP,KAAK,CAACwE,IAAI,KAAK4C,OAAO,CAAC7G,GAAG,CAACN,GAAG,CAACuE,IAAI,GAC/C,CAACvI,WAAW,CAACK,IAAI,CAAC8K,OAAO,CAAC7K,KAAK,CAAC;UAEpC,MAAMiQ,iBAAiB,GACrBD,UAAU,IACV,CAAC5Q,WAAW,CAACoE,IAAI,CAAC,IAClB,CAACnE,WAAW,CAACoK,MAAM,CAAC,IACpB,CAACnK,iBAAiB,CAACmK,MAAM,CAAC,IAC1B,CAAClK,cAAc,CAACiE,IAAI,CAAC;UAEvB,IAAI1D,IAAI,MAAyB,EAAE;YACjC,IAAI,CAACiL,aAAa,CAChBF,OAAO,EACNoF,iBAAiB,IAAIzM,IAAI,CAAC1D,IAAI,KAAK,kBAAkB,IACnDkQ,UAAU,IAAI7Q,UAAU,CAACsK,MAAM,EAAE;cAAE8C,IAAI,EAAE/I;YAAK,CAAC,CAAE,QAGtD,CAAC;UACH,CAAC,MAAM,IAAIyM,iBAAiB,IAAInQ,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAACiL,aAAa,CAACF,OAAO,GAA0B,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,GAA8B,CAAC;UAC3D;QACF,CAAC,MAAM,IACL/K,IAAI,MAAuB,IAC3B,EAAE0D,IAAI,CAAC1D,IAAI,KAAK,kBAAkB,IAAI0D,IAAI,CAAC0M,UAAU,CAAC/N,MAAM,GAAG,CAAC,CAAC,IACjEqB,IAAI,CAAC1D,IAAI,KAAK,WAAW,IACzB0D,IAAI,CAAC1D,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAACiL,aAAa,CAChBF,OAAO,EACP3E,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAK8F,GAAG,GAAG,CAAC,QAGnB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACjB,aAAa,CAACF,OAAO,GAA8B,CAAC;QAC3D;MACF;IACF;IAEA,IAAI/K,IAAI,MAA0B,IAAIuP,MAAM,IAAIG,QAAQ,EAAE;MACxD,IAAI,CAAChO,gBAAgB,GAAGgO,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAAClQ,OAAO,CAACmQ,SAAS,EAAErR,kBAAkB,CAAC;AAEjB;EACjC,IAAAsR,mCAAuB,EAACpQ,OAAO,CAAC;AAClC;AAAC,IAAAqQ,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKcvQ,OAAO;AAEtB,SAASsN,cAAcA,CAAgB9H,eAAuB,EAAEgL,IAAa,EAAE;EAC7E,IAAI,CAACpM,KAAK,CAAC,GAAG,EAAE,KAAK,EAAEoB,eAAe,CAAC;EACvC,IAAI,CAACgL,IAAI,EAAE,IAAI,CAAClM,KAAK,CAAC,CAAC;AACzB", "ignoreList": []}