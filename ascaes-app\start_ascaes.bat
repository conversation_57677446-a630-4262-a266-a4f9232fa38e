@echo off
title ASCAES Application Launcher
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ASCAES Application                        ║
echo ║              Academic Document Generation                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Check prerequisites
echo [1/7] Checking prerequisites...
where ollama >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Ollama not found. Please install from https://ollama.ai/
    pause & exit /b 1
)
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js not found. Please install from https://nodejs.org/
    pause & exit /b 1
)
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python not found. Please install from https://python.org/
    pause & exit /b 1
)
echo ✅ All prerequisites found

:: Stop existing services
echo.
echo [2/7] Stopping existing services...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
echo ✅ Cleaned up existing processes

:: Check Ollama
echo.
echo [3/7] Setting up Ollama...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting Ollama server...
    start "Ollama Server" cmd /k "title Ollama Server && echo Ollama Server Starting... && ollama serve"
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ Ollama is already running
)

:: Setup backend
echo.
echo [4/7] Setting up backend...
cd backend-python

if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

echo Installing backend dependencies...
venv\Scripts\pip.exe install --quiet --upgrade pip
venv\Scripts\pip.exe install --quiet fastapi uvicorn httpx pydantic

echo Starting backend server...
start "ASCAES Backend" cmd /k "title ASCAES Backend && cd /d %cd% && echo Backend Server Starting... && venv\Scripts\python.exe -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload"

cd ..

:: Setup frontend
echo.
echo [5/7] Setting up frontend...
cd frontend-react

if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install --silent
) else (
    echo ✅ Frontend dependencies already installed
)

echo Starting frontend server...
start "ASCAES Frontend" cmd /k "title ASCAES Frontend && cd /d %cd% && echo Frontend Server Starting... && npm run dev"

cd ..

:: Wait and verify
echo.
echo [6/7] Waiting for services to start...
timeout /t 15 /nobreak >nul

echo Verifying services...
curl -s http://localhost:8001/api/v1/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend is running on http://localhost:8001
) else (
    echo ⚠️  Backend may still be starting...
)

curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend is running on http://localhost:5173
) else (
    echo ⚠️  Frontend may still be starting...
)

:: Launch application
echo.
echo [7/7] Launching application...
start http://localhost:5173

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 ASCAES IS READY! 🚀                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📱 Application: http://localhost:5173
echo 🔧 Backend API: http://localhost:8001
echo 🤖 Ollama: http://localhost:11434
echo.
echo 🛑 To stop all services: run stop_all.bat
echo 📋 Service windows are open in separate terminals
echo.
echo Press any key to close this window...
pause >nul
