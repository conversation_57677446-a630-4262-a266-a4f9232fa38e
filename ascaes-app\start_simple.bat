@echo off
echo ========================================
echo    ASCAES Quick Start Script
echo ========================================
echo.

:: Kill any existing processes first
echo Stopping any existing services...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

echo.
echo Step 1: Checking Ollama...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting Ollama server...
    start "Ollama" cmd /k "ollama serve"
    timeout /t 5 /nobreak >nul
) else (
    echo ✓ Ollama is running
)

echo.
echo Step 2: Installing backend dependencies...
cd backend-python

:: Create virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

:: Install packages directly to virtual environment
echo Installing required packages...
venv\Scripts\pip.exe install --quiet --upgrade pip
venv\Scripts\pip.exe install --quiet fastapi uvicorn httpx pydantic

:: Check if uvicorn is available
venv\Scripts\python.exe -c "import uvicorn" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing uvicorn again...
    venv\Scripts\pip.exe install uvicorn
)

echo ✓ Backend dependencies ready

echo.
echo Step 3: Starting backend server...
start "ASCAES Backend" cmd /k "cd /d %cd% && echo Starting ASCAES Backend Server... && venv\Scripts\python.exe -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload"

cd ..

echo.
echo Step 4: Installing frontend dependencies...
cd frontend-react

if not exist "node_modules" (
    echo Installing Node.js packages...
    npm install
)

echo ✓ Frontend dependencies ready

echo.
echo Step 5: Starting frontend server...
start "ASCAES Frontend" cmd /k "npm run dev"

cd ..

echo.
echo Step 6: Waiting for services to start...
timeout /t 10 /nobreak >nul

echo.
echo Step 7: Opening application...
start http://localhost:5173

echo.
echo ========================================
echo    ASCAES Started Successfully!
echo ========================================
echo.
echo Services:
echo - Ollama: Running
echo - Backend: http://localhost:8001
echo - Frontend: http://localhost:5173
echo.
echo To stop: Close the opened command windows
echo         or run stop_all.bat
echo.
pause
