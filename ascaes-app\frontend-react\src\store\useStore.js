import { create } from 'zustand'

const useStore = create((set, get) => ({
  // Theme state
  theme: 'light',
  toggleTheme: () => set((state) => ({ theme: state.theme === 'light' ? 'dark' : 'light' })),

  // Current view state
  currentView: 'chat',
  setCurrentView: (view) => set({ currentView: view }),

  // Generation settings
  generationSettings: {
    topic: '',
    writingStyle: 'Analytical',
    culturalInflection: {},
    pages: 1,
    humanization: false,
    selectedModel: 'deepseek-r1:1.5b', // Default model
    academicFormatting: {
      enabled: false,
      citationStyle: 'MLA',
      studentName: '',
      professorName: '',
      courseInfo: ''
    }
  },
  updateGenerationSettings: (settings) => set((state) => ({
    generationSettings: { ...state.generationSettings, ...settings }
  })),

  // Documents state
  documents: [],
  setDocuments: (documents) => set({ documents }),
  addDocument: (document) => set((state) => ({ documents: [document, ...state.documents] })),
  deleteDocument: (id) => set((state) => ({ 
    documents: state.documents.filter(doc => doc.id !== id) 
  })),

  // Models state (using fixed DeepSeek-R1 model)
  models: [{ name: 'deepseek-r1:1.5b' }],
  setModels: (models) => set({ models }),

  // Generation state
  isGenerating: false,
  generatedText: '',
  generationAbortController: null,
  generationProgress: {
    current: 0,
    total: 0,
    status: '',
    batchStart: 0,
    batchEnd: 0,
    currentBatch: 0,
    totalBatches: 0,
    wordCount: 0,
    batchWordCount: 0,
    finalWordCount: 0,
    targetWordCount: 0
  },
  setIsGenerating: (isGenerating) => set({ isGenerating }),
  setGeneratedText: (text) => set({ generatedText: text }),
  appendGeneratedText: (text) => set((state) => ({
    generatedText: state.generatedText + text
  })),
  setGenerationProgress: (progress) => set({ generationProgress: progress }),
  setGenerationAbortController: (controller) => set({ generationAbortController: controller }),
  stopGeneration: () => {
    const { generationAbortController } = get()
    if (generationAbortController) {
      generationAbortController.abort()
      set({
        isGenerating: false,
        generationAbortController: null,
        generationProgress: {
          current: 0,
          total: 0,
          status: 'Generation stopped',
          batchStart: 0,
          batchEnd: 0,
          wordCount: 0
        }
      })
    }
  },

  // Settings panel state
  settingsTab: 'document',
  setSettingsTab: (tab) => set({ settingsTab: tab }),
}))

export default useStore

