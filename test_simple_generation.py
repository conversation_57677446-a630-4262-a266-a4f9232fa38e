#!/usr/bin/env python3
import requests
import json
import time

def test_ollama_direct():
    """Test Ollama directly"""
    print("Testing Ollama directly...")
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.2:3b",
                "prompt": "Write a short paragraph about education.",
                "stream": False
            },
            timeout=120
        )
        if response.status_code == 200:
            result = response.json()
            print("✅ Ollama working!")
            print("Response:", result.get("response", "No response"))
            return True
        else:
            print(f"❌ Ollama failed with status {response.status_code}")
            print("Response:", response.text)
            return False
    except Exception as e:
        print(f"❌ Ollama error: {e}")
        return False

def test_backend_health():
    """Test backend health"""
    print("\nTesting backend health...")
    try:
        response = requests.get("http://localhost:8001/api/v1/health", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("✅ Backend health check passed!")
            print("Status:", result.get("status"))
            return True
        else:
            print(f"❌ Backend health failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health error: {e}")
        return False

def test_generation():
    """Test document generation"""
    print("\nTesting document generation...")
    try:
        payload = {
            "topic": "The benefits of online learning",
            "writing_style": "Analytical",
            "cultural_inflection_config": {"style": "neutral"},
            "total_pages": 1,
            "humanization": False,
            "academic_formatting": None
        }
        
        response = requests.post(
            "http://localhost:8001/api/v1/generate",
            json=payload,
            timeout=180,
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ Generation started successfully!")
            content = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    content += chunk
                    print(chunk, end="", flush=True)
            print(f"\n\n✅ Generation completed! Total length: {len(content)} characters")
            return True
        else:
            print(f"❌ Generation failed with status {response.status_code}")
            print("Response:", response.text)
            return False
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

if __name__ == "__main__":
    print("ASCAES Generation Test")
    print("=" * 50)
    
    # Test Ollama first
    ollama_ok = test_ollama_direct()
    
    # Test backend health
    backend_ok = test_backend_health()
    
    # Test generation if both are working
    if ollama_ok and backend_ok:
        test_generation()
    else:
        print("\n❌ Skipping generation test due to previous failures")
    
    print("\n" + "=" * 50)
    print("Test completed!")
