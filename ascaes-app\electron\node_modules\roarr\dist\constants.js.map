{"version": 3, "sources": ["../src/constants.js"], "names": ["logLevels", "debug", "error", "fatal", "info", "trace", "warn"], "mappings": ";;;;;;AAEO,MAAMA,SAAS,GAAG;AACvBC,EAAAA,KAAK,EAAE,EADgB;AAEvBC,EAAAA,KAAK,EAAE,EAFgB;AAGvBC,EAAAA,KAAK,EAAE,EAHgB;AAIvBC,EAAAA,IAAI,EAAE,EAJiB;AAKvBC,EAAAA,KAAK,EAAE,EALgB;AAMvBC,EAAAA,IAAI,EAAE;AANiB,CAAlB", "sourcesContent": ["// @flow\n\nexport const logLevels = {\n  debug: 20,\n  error: 50,\n  fatal: 60,\n  info: 30,\n  trace: 10,\n  warn: 40,\n};\n"], "file": "constants.js"}