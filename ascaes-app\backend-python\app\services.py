# This is the literal content for services.py
from typing import Optional

# --- Part 1: Writing Style Definitions (The "What") ---
WRITING_STYLE_DEFINITIONS = {
    "Analytical": {"CORE_OBJECTIVE": "To dissect a topic into its fundamental components and explain its internal logic.", "TONE": "Formal, objective, intellectual, detached.", "STRUCTURE": "Thesis first -> Systematic breakdown -> Logical connectives -> Synthesis.", "FOCUS": "The 'why' and 'how' of a system.", "VOCABULARY": "Precise, academic, discipline-specific."},
    "Instructional": {"CORE_OBJECTIVE": "To teach a user how to perform a task.", "TONE": "Direct, clear, encouraging, authoritative.", "STRUCTURE": "Objective first -> Sequential steps -> Numbered lists/commands -> Examples.", "FOCUS": "Practical application and skill acquisition.", "VOCABULARY": "Simple, action-oriented. Jargon is defined."},
    "Reporting": {"CORE_OBJECTIVE": "To present factual information concisely and objectively.", "TONE": "Neutral, formal, data-driven, impersonal.", "STRUCTURE": "Executive summary -> Headings -> Bullet points.", "FOCUS": "The 'what,' 'where,' 'when'; verified facts only.", "VOCABULARY": "Formal, precise, standardized."},
    "Argumentative / Persuasive": {"CORE_OBJECTIVE": "To advocate for a viewpoint and persuade the reader.", "TONE": "Strong, assertive, confident, passionate.", "STRUCTURE": "Strong thesis -> Supporting evidence -> Refute counter-arguments -> Call to action.", "FOCUS": "Convincing the reader of a truth or policy.", "VOCABULARY": "Strong, convincing, emotionally resonant."},
    "Exploratory / Reflective": {"CORE_OBJECTIVE": "To contemplate the deeper meaning or philosophical implications.", "TONE": "Introspective, philosophical, questioning.", "STRUCTURE": "Associative/meandering -> Poses open-ended questions -> Journey of discovery.", "FOCUS": "The 'feeling' of a subject; the process of discovery.", "VOCABULARY": "Tentative, investigative, philosophical, metaphorical."},
    "Descriptive": {"CORE_OBJECTIVE": "To create a vivid, detailed picture of a subject.", "TONE": "Objective yet evocative; technically precise.", "STRUCTURE": "General overview -> Specific, granular details. Organized spatially or by feature.", "FOCUS": "The 'what it is like'; forming a precise mental model.", "VOCABULARY": "Technical, precise, concrete nouns, specific adjectives."},
    "Narrative": {"CORE_OBJECTIVE": "To tell a story about the topic's history or development.", "TONE": "Engaging, storytelling, chronological.", "STRUCTURE": "Classic narrative arc (Beginning, Middle, End).", "FOCUS": "Progression of events through time.", "VOCABULARY": "Accessible, flowing, story-driven."},
    "Schematic / Referential": {"CORE_OBJECTIVE": "To provide a quick, structured, reference-style overview.", "TONE": "Terse, factual, highly condensed, utilitarian.", "STRUCTURE": "Heavy use of headings, lists, bold keywords; phrases over prose.", "FOCUS": "High-density information transfer for quick look-up.", "VOCABULARY": "Standardized, technical, concise."}
}

# --- Part 2: Cultural Inflection Definitions (The "How") ---
CULTURAL_INFLECTION_DEFINITIONS = {
    "American": {"RHETORICAL_STYLE": "Direct, 'Bottom Line Up Front' (BLUF).", "TONE": "Confident, optimistic, pragmatic.", "KEY_VALUES_EMPHASIZED": "Individualism, innovation, efficiency, results."},
    "Russian": {"RHETORICAL_STYLE": "Context-heavy, builds background first.", "TONE": "Formal, serious, skeptical (gravitas).", "KEY_VALUES_EMPHASIZED": "Historical precedent, foundational principles, endurance, depth of thought."},
    "German": {"RHETORICAL_STYLE": "Formal, logical, exhaustive.", "TONE": "Very formal, precise, direct, risk-averse.", "KEY_VALUES_EMPHASIZED": "Order, precision, quality, reliability, process."},
    "Japanese": {"RHETORICAL_STYLE": "Indirect, builds consensus ('nemawashi').", "TONE": "Polite, formal, humble, harmonious.", "KEY_VALUES_EMPHASIZED": "Group harmony, consensus, long-term perspective, respect for process."},
    "French": {"RHETORICAL_STYLE": "Dialectical (thesis -> antithesis -> synthesis). Abstract and philosophical.", "TONE": "Intellectual, sophisticated, critical.", "KEY_VALUES_EMPHASIZED": "Logic, theoretical elegance, intellectual heritage, debate."}
}

# This protocol should also be stored in services.py and appended to the main prompt when requested by the user.

ACADEMIC_FORMATTING_PROTOCOL = """
### ACADEMIC FORMATTING PROTOCOL ###

You must generate text that strictly adheres to the following rules for standard academic papers. The citation style to be used is: [Citation Style].

1.  **GLOBAL SETTINGS:**
    *   **Font:** Assume final render is 12pt Times New Roman.
    *   **Margins:** Assume 1-inch margins on all sides.
    *   **Spacing:** The entire document must be double-spaced with NO extra space between paragraphs.
    *   **Alignment:** Left-aligned. DO NOT justify.

2.  **FIRST PAGE STRUCTURE:**
    *   **Header Block (Top-Left):**
        [Student's Name]
        [Professor's Name]
        [Course Info]
        [Date]
    *   **Title:** Centered, Title Case. No bold, underline, or all caps. One double-spaced line after the header block.

3.  **RUNNING HEAD & PAGE NUMBERS:**
    *   **Placement:** Top-right corner of every page.
    *   **Content:** Adhere to the specified [Citation Style] for the running head content (e.g., 'Last Name Page#' for MLA, 'Short Title Page#' for APA).
    *   **First Page Exception:** The running head MUST NOT appear on the first page. It must begin on page 2.

4.  **PARAGRAPH FORMATTING:**
    *   **Indentation:** The first line of every paragraph must be indented by 0.5 inches (one Tab stop).
    *   **Block Quotations:** For quotes longer than four lines, indent the entire block 0.5 inches from the left margin and DO NOT use quotation marks. The citation follows the final punctuation.

5.  **SECTION BREAKS:**
    *   Use a page break before the final bibliography section (e.g., "Works Cited," "References"). The title should be centered.
"""




def construct_prompt(topic: str, writing_style: str, cultural_inflection_config: dict, total_pages: int, humanization: bool, academic_formatting: Optional[dict]) -> str:
    # Calculate exact word count
    target_words = total_pages * 250

    # Get style definition
    style_def = WRITING_STYLE_DEFINITIONS.get(writing_style, WRITING_STYLE_DEFINITIONS["Analytical"])

    # Build cultural inflection instructions (background - not shown in output)
    cultural_instructions = ""
    if cultural_inflection_config:
        active_cultures = {k: v for k, v in cultural_inflection_config.items() if v > 0}
        if active_cultures:
            cultural_instructions = "\n\nIMPORTANT - CULTURAL WRITING STYLE (apply but do not mention in output):\n"
            for culture, percentage in active_cultures.items():
                if culture in CULTURAL_INFLECTION_DEFINITIONS:
                    culture_def = CULTURAL_INFLECTION_DEFINITIONS[culture]
                    cultural_instructions += f"Write with {percentage}% {culture} influence:\n"
                    cultural_instructions += f"- Use {culture_def.get('RHETORICAL_STYLE', 'standard')} rhetorical style\n"
                    cultural_instructions += f"- Adopt {culture_def.get('TONE', 'neutral')} tone\n"
                    cultural_instructions += f"- Emphasize {culture_def.get('KEY_VALUES_EMPHASIZED', 'universal values')}\n"

    # Build humanization instructions
    humanization_instructions = ""
    if humanization:
        humanization_instructions = "\n\nHUMANIZATION: Write in a more natural, conversational tone while maintaining academic rigor."

    # Build academic formatting instructions
    academic_instructions = ""
    if academic_formatting and academic_formatting.get('enabled'):
        citation_style = academic_formatting.get("citation_style", "")
        student_name = academic_formatting.get("student_name", "")
        professor_name = academic_formatting.get("professor_name", "")
        course_info = academic_formatting.get("course_info", "")

        formatted_protocol = ACADEMIC_FORMATTING_PROTOCOL.replace("[Citation Style]", citation_style)
        formatted_protocol = formatted_protocol.replace("[Student's Name]", student_name)
        formatted_protocol = formatted_protocol.replace("[Professor's Name]", professor_name)
        formatted_protocol = formatted_protocol.replace("[Course Info]", course_info)
        academic_instructions = f"\n\n{formatted_protocol}"

    # Construct the main prompt with strict word count
    prompt = f"""Write a comprehensive {writing_style.lower()} academic essay about {topic}.

CRITICAL WORD COUNT REQUIREMENTS:
- You MUST write exactly {target_words} words (not approximately, but EXACTLY)
- This is equivalent to {total_pages} pages at 250 words per page
- Count every single word as you write to ensure accuracy
- Do NOT stop writing until you reach exactly {target_words} words
- Do NOT exceed {target_words} words
- Do not include word count in the output
- Do not mention cultural influences in the text

WRITING STYLE REQUIREMENTS:
- Core Objective: {style_def['CORE_OBJECTIVE']}
- Tone: {style_def['TONE']}
- Structure: {style_def['STRUCTURE']}
- Focus: {style_def['FOCUS']}
- Vocabulary: {style_def['VOCABULARY']}

CONTENT STRUCTURE GUIDELINES:
- Introduction: ~10% of total words
- Main body: ~80% of total words (divided into logical sections)
- Conclusion: ~10% of total words
- Use clear transitions between sections
- Maintain academic rigor throughout

{cultural_instructions}
{humanization_instructions}
{academic_instructions}

Begin writing now and continue until you reach exactly {target_words} words. Remember: EXACTLY {target_words} words, no more, no less."""

    return prompt


