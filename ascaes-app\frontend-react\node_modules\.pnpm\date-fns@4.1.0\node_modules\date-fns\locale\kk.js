import { formatDistance } from "./kk/_lib/formatDistance.js";
import { formatLong } from "./kk/_lib/formatLong.js";
import { formatRelative } from "./kk/_lib/formatRelative.js";
import { localize } from "./kk/_lib/localize.js";
import { match } from "./kk/_lib/match.js";

/**
 * @category Locales
 * @summary Kazakh locale.
 * @language Kazakh
 * @iso-639-2 kaz
 * <AUTHOR> [@drugoi](https://github.com/drugoi)
 */
export const kk = {
  code: "kk",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default kk;
