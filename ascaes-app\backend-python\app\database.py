import sqlite3

DATABASE_FILE = "ascaes.db"

def init_db():
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS documents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            topic TEXT NOT NULL,
            writing_style TEXT NOT NULL,
            cultural_inflection_config TEXT,
            total_pages INTEGER,
            humanization BOOLEAN,
            academic_formatting TEXT,
            generated_text TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    conn.commit()
    conn.close()

def insert_document(title, topic, writing_style, cultural_inflection_config, total_pages, humanization, academic_formatting, generated_text):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute("""
        INSERT INTO documents (title, topic, writing_style, cultural_inflection_config, total_pages, humanization, academic_formatting, generated_text)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (title, topic, writing_style, str(cultural_inflection_config), total_pages, humanization, str(academic_formatting), generated_text))
    conn.commit()
    conn.close()

def get_all_documents():
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM documents ORDER BY timestamp DESC")
    documents = cursor.fetchall()
    conn.close()
    return documents

def get_document_by_id(doc_id):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM documents WHERE id = ?", (doc_id,))
    document = cursor.fetchone()
    conn.close()
    return document

def delete_document(doc_id):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute("DELETE FROM documents WHERE id = ?", (doc_id,))
    conn.commit()
    conn.close()


