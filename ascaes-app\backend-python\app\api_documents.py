from fastapi import APIRouter, HTTPException
from app.database import get_all_documents, get_document_by_id, delete_document

router = APIRouter()

@router.get("/documents")
async def get_documents():
    documents = get_all_documents()
    # Convert boolean values from SQLite (0 or 1) to Python booleans
    formatted_documents = []
    for doc in documents:
        # Assuming the order of columns as per insert_document in database.py
        # id, title, topic, writing_style, cultural_inflection_config, total_pages, humanization, academic_formatting, generated_text, timestamp
        formatted_doc = {
            "id": doc[0],
            "title": doc[1],
            "topic": doc[2],
            "writing_style": doc[3],
            "cultural_inflection_config": doc[4], # This is stored as string, might need parsing on frontend
            "total_pages": doc[5],
            "humanization": bool(doc[6]),
            "academic_formatting": doc[7], # This is stored as string, might need parsing on frontend
            "generated_text": doc[8],
            "timestamp": doc[9]
        }
        formatted_documents.append(formatted_doc)
    return formatted_documents

@router.get("/documents/{doc_id}")
async def get_single_document(doc_id: int):
    document = get_document_by_id(doc_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    formatted_doc = {
        "id": document[0],
        "title": document[1],
        "topic": document[2],
        "writing_style": document[3],
        "cultural_inflection_config": document[4],
        "total_pages": document[5],
        "humanization": bool(document[6]),
        "academic_formatting": document[7],
        "generated_text": document[8],
        "timestamp": document[9]
    }
    return formatted_doc

@router.delete("/documents/{doc_id}")
async def delete_single_document(doc_id: int):
    delete_document(doc_id)
    return {"message": "Document deleted successfully"}


