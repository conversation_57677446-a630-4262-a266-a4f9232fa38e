@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\src\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\src\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\mkdirp@3.0.1\node_modules;E:\edu\ascaes-app-v1.0.0\ascaes-app\frontend-react\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\src\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\src\bin.js" %*
)
