@echo off
echo ========================================
echo    Optimizing Ollama for ASCAES
echo ========================================
echo.

echo Setting Ollama environment variables for optimal performance...

:: Set environment variables for better performance
set OLLAMA_NUM_PARALLEL=4
set OLLAMA_MAX_LOADED_MODELS=1
set OLLAMA_MAX_QUEUE=512
set OLLAMA_FLASH_ATTENTION=1
set OLLAMA_HOST=0.0.0.0:11434

:: GPU optimization (if available)
set OLLAMA_GPU_OVERHEAD=0
set OLLAMA_LOAD_TIMEOUT=5m

echo ✅ Environment variables set for optimal performance
echo.

echo Current Ollama configuration:
echo - Parallel requests: %OLLAMA_NUM_PARALLEL%
echo - Max loaded models: %OLLAMA_MAX_LOADED_MODELS%
echo - Max queue size: %OLLAMA_MAX_QUEUE%
echo - Flash attention: %OLLAMA_FLASH_ATTENTION%
echo - Host: %OLLAMA_HOST%
echo.

echo Checking llama3.2:1b model...
ollama list | findstr "llama3.2:1b"
if %errorlevel% equ 0 (
    echo ✅ llama3.2:1b model is available
) else (
    echo ❌ llama3.2:1b model not found
    echo Pulling llama3.2:1b model...
    ollama pull llama3.2:1b
)

echo.
echo ========================================
echo    Ollama Optimization Complete!
echo ========================================
echo.
echo Performance optimizations applied:
echo - Parallel processing enabled
echo - Memory usage optimized
echo - GPU acceleration configured
echo - Flash attention enabled
echo.
echo You can now start ASCAES with improved performance!
echo.
pause
