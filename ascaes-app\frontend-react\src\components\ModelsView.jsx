import { useEffect, useState } from 'react'
import { Settings, CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import useStore from '../store/useStore'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'

const ModelsView = () => {
  const { models, setModels } = useStore()
  const [loading, setLoading] = useState(false)
  const [ollamaStatus, setOllamaStatus] = useState('unknown')

  useEffect(() => {
    fetchModels()
  }, [])

  const fetchModels = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:8001/api/v1/models')
      if (response.ok) {
        const data = await response.json()
        setModels(data.models || [])
        setOllamaStatus('connected')
      } else {
        setOllamaStatus('error')
      }
    } catch (error) {
      console.error('Failed to fetch models:', error)
      setOllamaStatus('disconnected')
    } finally {
      setLoading(false)
    }
  }

  const formatSize = (bytes) => {
    if (!bytes) return 'Unknown'
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = () => {
    switch (ollamaStatus) {
      case 'connected': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'disconnected': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = () => {
    switch (ollamaStatus) {
      case 'connected': return <CheckCircle size={20} className="text-green-600" />
      case 'error': return <XCircle size={20} className="text-red-600" />
      case 'disconnected': return <XCircle size={20} className="text-red-600" />
      default: return <Settings size={20} className="text-gray-600" />
    }
  }

  const getStatusText = () => {
    switch (ollamaStatus) {
      case 'connected': return 'Connected to Ollama'
      case 'error': return 'Ollama API Error'
      case 'disconnected': return 'Cannot connect to Ollama'
      default: return 'Checking Ollama status...'
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-semibold">Model Management</h2>
            <p className="text-muted-foreground">
              Manage your local Ollama models
            </p>
          </div>
          <Button onClick={fetchModels} disabled={loading}>
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Status */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          {getStatusIcon()}
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        {ollamaStatus === 'disconnected' && (
          <p className="text-sm text-muted-foreground mt-2">
            Make sure Ollama is running on localhost:11434
          </p>
        )}
      </div>

      {/* Models Grid */}
      <div className="flex-1 p-6 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw size={48} className="mx-auto mb-4 animate-spin opacity-50" />
              <p className="text-lg mb-2">Loading models...</p>
            </div>
          </div>
        ) : models.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <Settings size={48} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">No models found</p>
              <p className="text-sm">
                {ollamaStatus === 'connected' 
                  ? 'No models are installed in Ollama'
                  : 'Check your Ollama connection'
                }
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {models.map((model, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-2">
                      {model.name || 'Unknown Model'}
                    </CardTitle>
                    {model.name === 'gemma3n:e2b' && (
                      <Badge variant="default" className="ml-2">
                        Active
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Size:</span> {formatSize(model.size)}
                    </p>
                    <p>
                      <span className="font-medium">Modified:</span> {formatDate(model.modified_at)}
                    </p>
                    {model.digest && (
                      <p className="text-xs text-muted-foreground">
                        <span className="font-medium">Digest:</span> {model.digest.substring(0, 16)}...
                      </p>
                    )}
                  </div>
                  
                  {model.details && (
                    <div className="text-xs text-muted-foreground space-y-1">
                      {model.details.family && (
                        <p><span className="font-medium">Family:</span> {model.details.family}</p>
                      )}
                      {model.details.parameter_size && (
                        <p><span className="font-medium">Parameters:</span> {model.details.parameter_size}</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ModelsView

